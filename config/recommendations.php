<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Product Recommendations Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the product recommendation
    | system, including caching settings, algorithm weights, and limits.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configure caching behavior for product recommendations to improve
    | performance and reduce database queries.
    |
    */
    'cache' => [
        'enabled' => env('RECOMMENDATIONS_CACHE_ENABLED', true),
        'ttl' => env('RECOMMENDATIONS_CACHE_TTL', 3600), // 1 hour in seconds
        'prefix' => env('RECOMMENDATIONS_CACHE_PREFIX', 'product_recommendations'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Algorithm Weights
    |--------------------------------------------------------------------------
    |
    | Configure the importance of different recommendation strategies.
    | Higher weights mean the strategy will contribute more products
    | to the final recommendation list.
    |
    */
    'weights' => [
        'cross_sell' => env('RECOMMENDATIONS_WEIGHT_CROSS_SELL', 0.3),
        'user_history' => env('RECOMMENDATIONS_WEIGHT_USER_HISTORY', 0.25),
        'popular_products' => env('RECOMMENDATIONS_WEIGHT_POPULAR', 0.25),
        'frequently_bought_together' => env('RECOMMENDATIONS_WEIGHT_FREQUENTLY_BOUGHT', 0.2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Recommendation Limits
    |--------------------------------------------------------------------------
    |
    | Configure limits for different recommendation contexts to control
    | performance and user experience.
    |
    */
    'limits' => [
        'cart_upsells' => env('RECOMMENDATIONS_LIMIT_CART_UPSELLS', 6),
        'product_related' => env('RECOMMENDATIONS_LIMIT_PRODUCT_RELATED', 8),
        'category_popular' => env('RECOMMENDATIONS_LIMIT_CATEGORY_POPULAR', 12),
        'fallback' => env('RECOMMENDATIONS_LIMIT_FALLBACK', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Strategy Configuration
    |--------------------------------------------------------------------------
    |
    | Enable or disable specific recommendation strategies and configure
    | their behavior.
    |
    */
    'strategies' => [
        'cross_sell' => [
            'enabled' => env('RECOMMENDATIONS_CROSS_SELL_ENABLED', true),
            'same_category_only' => env('RECOMMENDATIONS_CROSS_SELL_SAME_CATEGORY', true),
            'include_subcategories' => env('RECOMMENDATIONS_CROSS_SELL_SUBCATEGORIES', false),
        ],
        
        'user_history' => [
            'enabled' => env('RECOMMENDATIONS_USER_HISTORY_ENABLED', true),
            'min_orders' => env('RECOMMENDATIONS_USER_HISTORY_MIN_ORDERS', 1),
            'max_days_back' => env('RECOMMENDATIONS_USER_HISTORY_MAX_DAYS', 365),
        ],
        
        'popular_products' => [
            'enabled' => env('RECOMMENDATIONS_POPULAR_ENABLED', true),
            'min_order_count' => env('RECOMMENDATIONS_POPULAR_MIN_ORDERS', 5),
            'time_window_days' => env('RECOMMENDATIONS_POPULAR_TIME_WINDOW', 90),
        ],
        
        'frequently_bought_together' => [
            'enabled' => env('RECOMMENDATIONS_FREQUENTLY_BOUGHT_ENABLED', true),
            'min_co_occurrence' => env('RECOMMENDATIONS_FREQUENTLY_BOUGHT_MIN_COUNT', 3),
            'time_window_days' => env('RECOMMENDATIONS_FREQUENTLY_BOUGHT_TIME_WINDOW', 180),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Configure performance-related settings for the recommendation engine.
    |
    */
    'performance' => [
        'max_query_time' => env('RECOMMENDATIONS_MAX_QUERY_TIME', 2), // seconds
        'use_database_indexes' => env('RECOMMENDATIONS_USE_DB_INDEXES', true),
        'batch_size' => env('RECOMMENDATIONS_BATCH_SIZE', 100),
        'enable_query_logging' => env('RECOMMENDATIONS_QUERY_LOGGING', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Settings
    |--------------------------------------------------------------------------
    |
    | Configure fallback behavior when primary recommendation strategies
    | don't return enough results.
    |
    */
    'fallback' => [
        'strategy' => env('RECOMMENDATIONS_FALLBACK_STRATEGY', 'newest'), // newest, popular, random
        'min_stock' => env('RECOMMENDATIONS_FALLBACK_MIN_STOCK', 1),
        'exclude_out_of_stock' => env('RECOMMENDATIONS_FALLBACK_EXCLUDE_OOS', true),
    ],
];
