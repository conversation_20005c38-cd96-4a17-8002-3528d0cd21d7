<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'molos' => [
        'api_key' => env('MOLOS_API_KEY'),
    ],
    
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
    ],

    'inpost' => [
        'organization_id' => env('INPOST_ORGANIZATION_ID'),
        'token' => env('INPOST_API_TOKEN'),
        'environment' => env('INPOST_ENVIRONMENT', 'production'),
    ],

    'payu' => [
        'environment' => env('PAYU_ENVIRONMENT', 'sandbox'),
        'pos_id' => env('PAYU_POS_ID'),
        'oauth_client_id' => env('PAYU_POS_ID'),
        'oauth_client_secret' => env('PAYU_CLIENT_SECRET'),
        'signature_key' => env('PAYU_SECOND_KEY'),
    ],
];
