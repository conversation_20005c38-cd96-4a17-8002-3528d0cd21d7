<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Invoice Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for PDF invoice generation,
    | including seller/company information and invoice settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Seller/Company Information
    |--------------------------------------------------------------------------
    |
    | Information about the company issuing invoices. This information
    | will appear on all generated invoices.
    |
    */
    'seller' => [
        'company_name' => env('INVOICE_COMPANY_NAME', 'Your Company Name'),
        'address' => [
            'street' => env('INVOICE_COMPANY_STREET', 'ul. Example 123'),
            'building_number' => env('INVOICE_COMPANY_BUILDING', ''),
            'apartment_number' => env('INVOICE_COMPANY_APARTMENT', ''),
            'post_code' => env('INVOICE_COMPANY_POST_CODE', '00-000'),
            'city' => env('INVOICE_COMPANY_CITY', 'Warsaw'),
            'country' => env('INVOICE_COMPANY_COUNTRY', 'Poland'),
        ],
        'tax_id' => env('INVOICE_COMPANY_NIP', ''), // NIP number
        'regon' => env('INVOICE_COMPANY_REGON', ''), // REGON number
        'krs' => env('INVOICE_COMPANY_KRS', ''), // KRS number (optional)
        'email' => env('INVOICE_COMPANY_EMAIL', '<EMAIL>'),
        'phone' => env('INVOICE_COMPANY_PHONE', '+48 ***********'),
        'website' => env('INVOICE_COMPANY_WEBSITE', 'https://example.com'),
        'bank_details' => [
            'bank_name' => env('INVOICE_BANK_NAME', 'Bank Name'),
            'account_number' => env('INVOICE_BANK_ACCOUNT', '12 3456 7890 1234 5678 9012 3456'),
            'swift' => env('INVOICE_BANK_SWIFT', ''), // Optional for international transfers
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Invoice Settings
    |--------------------------------------------------------------------------
    |
    | General settings for invoice generation and formatting.
    |
    */
    'settings' => [
        'currency' => env('INVOICE_CURRENCY', 'PLN'),
        'currency_symbol' => env('INVOICE_CURRENCY_SYMBOL', 'zł'),
        'date_format' => env('INVOICE_DATE_FORMAT', 'd.m.Y'),
        'number_format' => [
            'decimals' => 2,
            'decimal_separator' => ',',
            'thousands_separator' => ' ',
        ],
        'logo_path' => env('INVOICE_LOGO_PATH', 'http://localhost/img/zoologo2.png'),
        'logo_width' => env('INVOICE_LOGO_WIDTH', 150), // in pixels
        'logo_height' => env('INVOICE_LOGO_HEIGHT', 60), // in pixels
    ],

    /*
    |--------------------------------------------------------------------------
    | Invoice Numbering
    |--------------------------------------------------------------------------
    |
    | Settings for automatic invoice number generation.
    | Polish invoices typically use format: FV/YYYY/MM/NNNN
    |
    */
    'numbering' => [
        'prefix' => env('INVOICE_NUMBER_PREFIX', 'FV'),
        'format' => env('INVOICE_NUMBER_FORMAT', '{prefix}/{year}/{month}/{number}'),
        'padding' => env('INVOICE_NUMBER_PADDING', 4), // Number of digits for sequential number
        'reset_yearly' => env('INVOICE_NUMBER_RESET_YEARLY', true),
        'reset_monthly' => env('INVOICE_NUMBER_RESET_MONTHLY', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | VAT Settings
    |--------------------------------------------------------------------------
    |
    | VAT rates and settings for Polish invoices.
    |
    */
    'vat' => [
        'default_rate' => env('INVOICE_VAT_DEFAULT_RATE', 23), // Default VAT rate in %
        'rates' => [
            0 => '0%',
            5 => '5%',
            8 => '8%',
            23 => '23%',
        ],
        'exempt_reason' => env('INVOICE_VAT_EXEMPT_REASON', 'Zwolnione z VAT'),
    ],

    /*
    |--------------------------------------------------------------------------
    | PDF Settings
    |--------------------------------------------------------------------------
    |
    | Settings for PDF generation using DomPDF.
    |
    */
    'pdf' => [
        'paper' => env('INVOICE_PDF_PAPER', 'A4'),
        'orientation' => env('INVOICE_PDF_ORIENTATION', 'portrait'),
        'dpi' => env('INVOICE_PDF_DPI', 96),
        'font_size' => env('INVOICE_PDF_FONT_SIZE', 10),
        'font_family' => env('INVOICE_PDF_FONT_FAMILY', 'DejaVu Sans'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Legal Information
    |--------------------------------------------------------------------------
    |
    | Legal text and information required on Polish invoices.
    |
    */
    'legal' => [
        'payment_terms' => env('INVOICE_PAYMENT_TERMS', '14 dni'),
        'footer_text' => env('INVOICE_FOOTER_TEXT', 'Dziękujemy za zakup!'),
        'notes' => env('INVOICE_NOTES', ''),
    ],
];
