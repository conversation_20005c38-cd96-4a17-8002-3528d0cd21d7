APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:1H6QwMNzUcAXmGmpjfFSl2LvmpcDuZeuCShwQBDhzLM=
APP_DEBUG=false
APP_URL=http://localhost

LOG_CHANNEL=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=emergency

# Test Database Configuration - Using SQLite for fast, isolated tests
DB_CONNECTION=sqlite_testing
DB_DATABASE=:memory:

# Alternative: Use file-based SQLite for debugging (uncomment if needed)
# DB_CONNECTION=sqlite_testing
# DB_DATABASE=database/testing.sqlite

# Cache and Session Configuration for Testing
BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

# Disable external services during testing
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration - Use array driver for testing (no actual emails sent)
MAIL_MAILER=array
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Test App"

# Disable external API services during testing
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Disable search during testing
SCOUT_DRIVER=null
MEILISEARCH_HOST=
MEILISEARCH_KEY=

# Disable payment gateways during testing
PAYU_ENVIRONMENT=testing
PAYU_POS_ID=test
PAYU_CLIENT_ID=test
PAYU_SECOND_KEY=test
PAYU_CLIENT_SECRET=test

# Disable shipping APIs during testing
INPOST_ORGANIZATION_ID=test
INPOST_API_TOKEN=test
INPOST_ENVIRONMENT=testing

# Disable external APIs during testing
OPENAI_API_KEY=test
MOLOS_API_KEY=test

# Image processing for testing
IMAGE_DRIVER=gd

# Invoice Settings (same as production for consistency)
INVOICE_CURRENCY="PLN"
INVOICE_CURRENCY_SYMBOL="zł"
INVOICE_DATE_FORMAT="d.m.Y"
INVOICE_LOGO_PATH="http://localhost/img/zoologo2.png"
INVOICE_LOGO_WIDTH=150
INVOICE_LOGO_HEIGHT=60

# Invoice Numbering
INVOICE_NUMBER_PREFIX="FV"
INVOICE_NUMBER_FORMAT="{prefix}/{year}/{month}/{number}"
INVOICE_NUMBER_PADDING=4
INVOICE_NUMBER_RESET_YEARLY=true
INVOICE_NUMBER_RESET_MONTHLY=true

# VAT Settings
INVOICE_VAT_DEFAULT_RATE=23
INVOICE_VAT_EXEMPT_REASON="Zwolnione z VAT"

# Legal Information
INVOICE_PAYMENT_TERMS="14 dni"
INVOICE_FOOTER_TEXT="Dziękujemy za zakup!"
INVOICE_NOTES=""
