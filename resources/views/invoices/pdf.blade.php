<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faktura {{ $invoice->invoice_number }}</title>
    <style>
        @page {
            margin: 2cm 1.5cm;
            size: A4;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .header-left {
            display: table-cell;
            width: 60%;
            vertical-align: top;
        }
        
        .header-right {
            display: table-cell;
            width: 40%;
            vertical-align: top;
            text-align: right;
        }
        
        .logo {
            max-width: 150px;
            max-height: 60px;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 24pt;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .invoice-details {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .invoice-details table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .invoice-details td {
            padding: 5px 10px;
            border: none;
        }
        
        .invoice-details .label {
            font-weight: bold;
            width: 40%;
        }
        
        .parties {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .seller, .buyer {
            display: table-cell;
            width: 48%;
            vertical-align: top;
            padding: 15px;
            border: 1px solid #dee2e6;
        }
        
        .seller {
            margin-right: 4%;
        }
        
        .party-title {
            font-size: 12pt;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .party-info {
            line-height: 1.6;
        }
        
        .party-info .company-name {
            font-weight: bold;
            font-size: 11pt;
            margin-bottom: 5px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 9pt;
        }
        
        .items-table th {
            background-color: #2c3e50;
            color: white;
            padding: 10px 5px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #2c3e50;
        }
        
        .items-table td {
            padding: 8px 5px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .items-table .item-name {
            text-align: left;
            max-width: 200px;
        }
        
        .items-table .number {
            text-align: right;
        }
        
        .items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .totals {
            width: 50%;
            margin-left: auto;
            margin-bottom: 30px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 8px 10px;
            border: 1px solid #dee2e6;
        }
        
        .totals .label {
            font-weight: bold;
            background-color: #f8f9fa;
            text-align: right;
        }
        
        .totals .amount {
            text-align: right;
            font-weight: bold;
        }
        
        .totals .total-row {
            background-color: #2c3e50;
            color: white;
            font-size: 11pt;
        }
        
        .payment-info {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .payment-info h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 9pt;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .font-bold {
            font-weight: bold;
        }
        
        .mb-10 {
            margin-bottom: 10px;
        }
        
        .mb-20 {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Header with Logo and Invoice Title -->
    <div class="header">
        <div class="header-left">
            @if(config('invoice.settings.logo_path'))
                <img src="{{ config('invoice.settings.logo_path') }}" alt="Logo" class="logo">
            @endif
        </div>
        <div class="header-right">
            <div class="invoice-title">FAKTURA</div>
            <div style="font-size: 12pt; color: #666;">
                Nr {{ $invoice->invoice_number }}
            </div>
        </div>
    </div>

    <!-- Invoice Details -->
    <div class="invoice-details">
        <table>
            <tr>
                <td class="label">Data wystawienia:</td>
                <td>{{ $invoice->issue_date->format(config('invoice.settings.date_format', 'd.m.Y')) }}</td>
                <td class="label">Data sprzedaży:</td>
                <td>{{ $invoice->sale_date->format(config('invoice.settings.date_format', 'd.m.Y')) }}</td>
            </tr>
            <tr>
                <td class="label">Termin płatności:</td>
                <td>{{ $invoice->due_date->format(config('invoice.settings.date_format', 'd.m.Y')) }}</td>
                <td class="label">Sposób płatności:</td>
                <td>{{ $invoice->payment_method ?? 'Przelew bankowy' }}</td>
            </tr>
        </table>
    </div>

    <!-- Seller and Buyer Information -->
    <div class="parties">
        <div class="seller">
            <div class="party-title">Sprzedawca</div>
            <div class="party-info">
                <div class="company-name">{{ $invoice->seller_data['company_name'] }}</div>
                <div>{{ $invoice->seller_data['address']['street'] }} {{ $invoice->seller_data['address']['building_number'] }}</div>
                @if($invoice->seller_data['address']['apartment_number'])
                    <div>{{ $invoice->seller_data['address']['apartment_number'] }}</div>
                @endif
                <div>{{ $invoice->seller_data['address']['post_code'] }} {{ $invoice->seller_data['address']['city'] }}</div>
                <div>{{ $invoice->seller_data['address']['country'] }}</div>
                <br>
                @if($invoice->seller_data['tax_id'])
                    <div><strong>NIP:</strong> {{ $invoice->seller_data['tax_id'] }}</div>
                @endif
                @if($invoice->seller_data['regon'])
                    <div><strong>REGON:</strong> {{ $invoice->seller_data['regon'] }}</div>
                @endif
                @if($invoice->seller_data['email'])
                    <div><strong>Email:</strong> {{ $invoice->seller_data['email'] }}</div>
                @endif
                @if($invoice->seller_data['phone'])
                    <div><strong>Tel:</strong> {{ $invoice->seller_data['phone'] }}</div>
                @endif
            </div>
        </div>
        
        <div class="buyer">
            <div class="party-title">Nabywca</div>
            <div class="party-info">
                <div class="company-name">{{ $invoice->buyer_data['name'] }}</div>
                <div>{{ $invoice->buyer_data['address']['street'] }} {{ $invoice->buyer_data['address']['building_number'] }}</div>
                @if(isset($invoice->buyer_data['address']['apartment_number']) && $invoice->buyer_data['address']['apartment_number'])
                    <div>{{ $invoice->buyer_data['address']['apartment_number'] }}</div>
                @endif
                <div>{{ $invoice->buyer_data['address']['post_code'] }} {{ $invoice->buyer_data['address']['city'] }}</div>
                <div>{{ $invoice->buyer_data['address']['country'] ?? 'Polska' }}</div>
                <br>
                @if(isset($invoice->buyer_data['email']) && $invoice->buyer_data['email'])
                    <div><strong>Email:</strong> {{ $invoice->buyer_data['email'] }}</div>
                @endif
                @if(isset($invoice->buyer_data['phone']) && $invoice->buyer_data['phone'])
                    <div><strong>Tel:</strong> {{ $invoice->buyer_data['phone'] }}</div>
                @endif
                @if(isset($invoice->buyer_data['tax_id']) && $invoice->buyer_data['tax_id'])
                    <div><strong>NIP:</strong> {{ $invoice->buyer_data['tax_id'] }}</div>
                @endif
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%;">Lp.</th>
                <th style="width: 40%;">Nazwa towaru/usługi</th>
                <th style="width: 8%;">Ilość</th>
                <th style="width: 5%;">Jedn.</th>
                <th style="width: 12%;">Cena netto</th>
                <th style="width: 8%;">VAT %</th>
                <th style="width: 10%;">Kwota VAT</th>
                <th style="width: 12%;">Wartość brutto</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->items as $index => $item)
            <tr>
                <td class="text-center">{{ $index + 1 }}</td>
                <td class="item-name">{{ $item['name'] }}</td>
                <td class="text-center">{{ $item['quantity'] }}</td>
                <td class="text-center">szt.</td>
                <td class="number">{{ number_format($item['price_net'], 2, ',', ' ') }} {{ $invoice->currency }}</td>
                <td class="text-center">{{ $item['vat_rate'] }}%</td>
                <td class="number">{{ number_format($item['vat_amount'], 2, ',', ' ') }} {{ $invoice->currency }}</td>
                <td class="number">{{ number_format($item['price_gross'], 2, ',', ' ') }} {{ $invoice->currency }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Totals -->
    <div class="totals">
        <table>
            <tr>
                <td class="label">Wartość netto:</td>
                <td class="amount">{{ number_format($invoice->subtotal, 2, ',', ' ') }} {{ $invoice->currency }}</td>
            </tr>
            <tr>
                <td class="label">VAT:</td>
                <td class="amount">{{ number_format($invoice->vat_amount, 2, ',', ' ') }} {{ $invoice->currency }}</td>
            </tr>
            <tr class="total-row">
                <td class="label">RAZEM DO ZAPŁATY:</td>
                <td class="amount">{{ number_format($invoice->total, 2, ',', ' ') }} {{ $invoice->currency }}</td>
            </tr>
        </table>
    </div>

    <!-- Payment Information -->
    @if(isset($invoice->seller_data['bank_details']))
    <div class="payment-info">
        <h4>Dane do przelewu:</h4>
        <div><strong>Bank:</strong> {{ $invoice->seller_data['bank_details']['bank_name'] }}</div>
        <div><strong>Numer konta:</strong> {{ $invoice->seller_data['bank_details']['account_number'] }}</div>
        <div><strong>Tytuł przelewu:</strong> Faktura {{ $invoice->invoice_number }}</div>
    </div>
    @endif

    <!-- Notes -->
    @if($invoice->notes)
    <div class="mb-20">
        <h4>Uwagi:</h4>
        <p>{{ $invoice->notes }}</p>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        @if(config('invoice.legal.footer_text'))
            <p>{{ config('invoice.legal.footer_text') }}</p>
        @endif
        <p>Faktura wygenerowana automatycznie dnia {{ now()->format('d.m.Y H:i') }}</p>
    </div>
</body>
</html>
