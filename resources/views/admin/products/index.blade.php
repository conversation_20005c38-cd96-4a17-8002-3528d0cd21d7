@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <h2 class="text-xl font-bold mb-4">Products</h2>
    <a href="{{ route('admin.products.create') }}" class="bg-blue-500 text-white py-2 px-4 rounded mb-4 inline-block">Add Product</a>
    

    <!-- Filter Form -->
    <form method="GET" action="{{ route('admin.products.index') }}" class="mb-4">
        <!-- Text Filter -->
        <input type="text" name="name" placeholder="Filter by name" value="{{ request('name') }}" class="border rounded p-2 mb-2">
        
        <!-- Relation Filter -->
        <select name="category_id" class="border rounded p-2 mb-2">
            <option value="">All Categories</option>
            @foreach ($categories as $category)
                <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                    {{ $category->name }}
                </option>
            @endforeach
        </select>

        <!-- Number Range Filter -->
        <input type="number" name="price_from" placeholder="Price from" value="{{ request('price_from') }}" class="border rounded p-2 mb-2">
        <input type="number" name="price_to" placeholder="Price to" value="{{ request('price_to') }}" class="border rounded p-2 mb-2">

        <!-- Date Range Filter -->
        <input type="date" name="created_at_from" value="{{ request('created_at_from') }}" class="border rounded p-2 mb-2">
        <input type="date" name="created_at_to" value="{{ request('created_at_to') }}" class="border rounded p-2 mb-2">

        <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded">Filter</button>
    </form>

    <form id="massMoveForm" action="{{ route('admin.products.mass-move') }}" method="POST" class="mb-4">
        <!-- Products Table -->
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr>
                    <th class="py-2">
                        <input type="checkbox" id="selectAllTable" class="mr-2">
                    </th>
                    <th class="py-2">
                        <a href="{{ route('admin.products.index', array_merge(request()->query(), ['sort' => 'name', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                            Name @if(request('sort') == 'name')
                                <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                            @endif
                        </a>
                    </th>
                    <th class="py-2">
                        <a href="{{ route('admin.products.index', array_merge(request()->query(), ['sort' => 'sku', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                            SKU @if(request('sort') == 'sku')
                                <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                            @endif
                        </a>
                    </th>
                    <th class="py-2">
                        <a href="{{ route('admin.products.index', array_merge(request()->query(), ['sort' => 'price', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                            Price @if(request('sort') == 'price')
                                <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                            @endif
                        </a>
                    </th>
                    <th class="py-2">
                        <a href="{{ route('admin.products.index', array_merge(request()->query(), ['sort' => 'vat', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                            VAT Rate @if(request('sort') == 'vat')
                                <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                            @endif
                        </a>
                    </th>
                    <th class="py-2">
                        <a href="{{ route('admin.products.index', array_merge(request()->query(), ['sort' => 'stock', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                            Stock @if(request('sort') == 'stock')
                                <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                            @endif
                        </a>
                    </th>
                    <th class="py-2">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($products as $product)
                <tr>
                    <td class="py-2">
                        <input type="checkbox" name="product_ids[]" value="{{ $product->id }}" class="product-checkbox">
                    </td>
                    <td class="py-2">{{ $product->name }}</td>
                    <td class="py-2">{{ $product->sku }}</td>
                    <td class="py-2">${{ $product->currentPrice() }}</td>
                    <td class="py-2">
                        @if($product->vat !== null)
                            {{ number_format($product->vat, 0) }}%
                        @else
                            <span class="text-gray-400">N/A</span>
                        @endif
                    </td>
                    <td class="py-2">{{ $product->stock }}</td>
                    <td class="py-2">
                        <a href="{{ route('admin.products.edit', $product->id) }}" class="text-blue-500">Edit</a>
                        <form action="{{ route('admin.products.destroy', $product->id) }}" method="POST" class="inline-block">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-500 ml-2">Delete</button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @csrf
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
                <input type="checkbox" id="selectAll" class="mr-2">
                <label for="selectAll">Select all on this page</label>
            </div>
            <div class="flex gap-2">
                <select name="target_category_id" class="border rounded p-2" required>
                    <option value="">Select target category</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                    @endforeach
                </select>
                <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded">Move Selected</button>
            </div>
        </div>
    </form>

    <!-- Pagination Links -->
    <div class="mt-4">
        {{ $products->links() }}
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const selectAllTable = document.getElementById('selectAllTable');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    const form = document.getElementById('massMoveForm');
    
    function updateSelectAll() {
        const allChecked = Array.from(productCheckboxes).every(checkbox => checkbox.checked);
        selectAll.checked = allChecked;
        selectAllTable.checked = allChecked;
    }

    selectAll.addEventListener('change', function() {
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    selectAllTable.addEventListener('change', function() {
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        selectAll.checked = this.checked;
    });

    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectAll);
    });

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
        if (selectedProducts.length === 0) {
            alert('Please select at least one product');
            return;
        }

        if (!form.target_category_id.value) {
            alert('Please select a target category');
            return;
        }

        if (confirm('Are you sure you want to move the selected products?')) {
            form.submit();
        }
    });
});
</script>
@endpush
@endsection
