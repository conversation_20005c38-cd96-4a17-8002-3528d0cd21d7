@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-box-seam me-2"></i>Products Management
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.products.create') }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-plus-circle me-1"></i>Add Product
            </a>
            <button type="button" id="bulkMoveBtn" class="bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600" disabled>
                <i class="bi bi-arrow-right me-1"></i>Move Selected
            </button>
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
        <form method="GET" action="{{ route('admin.products.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text"
                           name="name"
                           value="{{ request('name') }}"
                           placeholder="Product name or SKU..."
                           class="border rounded p-2 w-full">
                </div>

                <!-- Category Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select name="category_id" class="border rounded p-2 w-full">
                        <option value="">All Categories</option>
                        @foreach ($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Price Range -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Price From</label>
                    <input type="number"
                           name="price_from"
                           value="{{ request('price_from') }}"
                           placeholder="Min price..."
                           class="border rounded p-2 w-full"
                           step="0.01">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Price To</label>
                    <input type="number"
                           name="price_to"
                           value="{{ request('price_to') }}"
                           placeholder="Max price..."
                           class="border rounded p-2 w-full"
                           step="0.01">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Date Range -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Created From</label>
                    <input type="date"
                           name="created_at_from"
                           value="{{ request('created_at_from') }}"
                           class="border rounded p-2 w-full">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Created To</label>
                    <input type="date"
                           name="created_at_to"
                           value="{{ request('created_at_to') }}"
                           class="border rounded p-2 w-full">
                </div>

                <!-- Stock Status -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
                    <select name="stock_status" class="border rounded p-2 w-full">
                        <option value="">All Stock Levels</option>
                        <option value="in_stock" {{ request('stock_status') === 'in_stock' ? 'selected' : '' }}>In Stock</option>
                        <option value="low_stock" {{ request('stock_status') === 'low_stock' ? 'selected' : '' }}>Low Stock (≤10)</option>
                        <option value="out_of_stock" {{ request('stock_status') === 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                    </select>
                </div>

                <!-- Items per page -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Items per page</label>
                    <select name="per_page" class="border rounded p-2 w-full">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                    </select>
                </div>
            </div>

            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
                <a href="{{ route('admin.products.index') }}" class="bg-gray-300 text-black py-2 px-4 rounded hover:bg-gray-400">
                    <i class="bi bi-x-circle me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Bulk Actions Form -->
    <form id="massMoveForm" action="{{ route('admin.products.mass-move') }}" method="POST">
        @csrf
        <input type="hidden" name="target_category_id" id="targetCategoryId">

        <div class="overflow-x-auto">
            <table class="min-w-full bg-white mt-4">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="py-3 px-4 border-b text-left">
                            <input type="checkbox" id="selectAll" class="rounded">
                        </th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                               class="text-gray-700 hover:text-gray-900">
                                Product Name
                                @if(request('sort') === 'name')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'sku', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                               class="text-gray-700 hover:text-gray-900">
                                SKU
                                @if(request('sort') === 'sku')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-left">Category</th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'price', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                               class="text-gray-700 hover:text-gray-900">
                                Price
                                @if(request('sort') === 'price')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-center">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'stock', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                               class="text-gray-700 hover:text-gray-900">
                                Stock
                                @if(request('sort') === 'stock')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-center">Status</th>
                        <th class="py-3 px-4 border-b text-center">Actions</th>
                    </tr>
                </thead>
            <tbody>
                @forelse ($products as $product)
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 border-b">
                        <input type="checkbox" name="product_ids[]" value="{{ $product->id }}" class="product-checkbox rounded">
                    </td>
                    <td class="py-3 px-4 border-b">
                        <div class="font-medium">{{ Str::limit($product->name, 50) }}</div>
                        @if($product->short_description)
                            <div class="text-sm text-gray-500 mt-1">{{ Str::limit($product->short_description, 60) }}</div>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b">
                        <code class="bg-gray-100 px-2 py-1 rounded text-sm">{{ $product->sku }}</code>
                    </td>
                    <td class="py-3 px-4 border-b">
                        <div class="font-medium">{{ $product->category->name ?? 'No Category' }}</div>
                        @if($product->producent)
                            <div class="text-sm text-gray-500 mt-1">{{ $product->producent->name }}</div>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b">
                        <div class="font-medium">{{ number_format($product->currentPrice(), 2) }} PLN</div>
                        @if($product->vat !== null)
                            <div class="text-sm text-gray-500 mt-1">VAT: {{ number_format($product->vat, 0) }}%</div>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        @if($product->stock > 10)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="In stock">
                                <i class="bi bi-check-circle me-1"></i>{{ $product->stock }}
                            </span>
                        @elseif($product->stock > 0)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" title="Low stock">
                                <i class="bi bi-exclamation-triangle me-1"></i>{{ $product->stock }}
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800" title="Out of stock">
                                <i class="bi bi-x-circle me-1"></i>{{ $product->stock }}
                            </span>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        @if($product->is_active ?? true)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="Active">
                                <i class="bi bi-check-circle me-1"></i>Active
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800" title="Inactive">
                                <i class="bi bi-dash-circle me-1"></i>Inactive
                            </span>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                            <a href="{{ route('admin.products.show', $product) }}"
                               class="text-blue-500 hover:text-blue-700 text-sm"
                               title="View Product">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="{{ route('admin.products.edit', $product) }}"
                               class="text-green-500 hover:text-green-700 text-sm"
                               title="Edit Product">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <form action="{{ route('admin.products.destroy', $product) }}"
                                  method="POST"
                                  class="inline-block delete-form">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="text-red-500 hover:text-red-700 text-sm"
                                        title="Delete Product">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="py-8 px-4 text-center text-gray-500">
                        No products found matching your criteria.
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    </form>

    @if($products->hasPages())
        <div class="mt-6">
            {{ $products->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $products->firstItem() ?? 0 }} to {{ $products->lastItem() ?? 0 }}
            of {{ $products->total() }} products
        </p>
    </div>

    <!-- Bulk Move Modal -->
    <div id="bulkMoveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold mb-4">Move Selected Products</h3>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Target Category</label>
                <select id="modalCategorySelect" class="border rounded p-2 w-full">
                    <option value="">Select target category</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex justify-end space-x-2">
                <button type="button" id="cancelMove" class="bg-gray-300 text-black py-2 px-4 rounded hover:bg-gray-400">
                    Cancel
                </button>
                <button type="button" id="confirmMove" class="bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600">
                    Move Products
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    const bulkMoveBtn = document.getElementById('bulkMoveBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const bulkMoveModal = document.getElementById('bulkMoveModal');
    const modalCategorySelect = document.getElementById('modalCategorySelect');
    const cancelMove = document.getElementById('cancelMove');
    const confirmMove = document.getElementById('confirmMove');
    const massMoveForm = document.getElementById('massMoveForm');

    // Handle select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButton();
    });

    // Handle individual checkbox changes
    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkButton();
        });
    });

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        const totalCount = productCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkButton() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        bulkMoveBtn.disabled = checkedCount === 0;

        if (checkedCount > 0) {
            bulkMoveBtn.innerHTML = `<i class="bi bi-arrow-right me-1"></i>Move ${checkedCount} Selected`;
        } else {
            bulkMoveBtn.innerHTML = '<i class="bi bi-arrow-right me-1"></i>Move Selected';
        }
    }

    // Handle bulk move button click
    bulkMoveBtn.addEventListener('click', function() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;

        if (checkedCount === 0) {
            alert('Please select at least one product.');
            return;
        }

        bulkMoveModal.classList.remove('hidden');
        bulkMoveModal.classList.add('flex');
    });

    // Handle modal cancel
    cancelMove.addEventListener('click', function() {
        bulkMoveModal.classList.add('hidden');
        bulkMoveModal.classList.remove('flex');
        modalCategorySelect.value = '';
    });

    // Handle modal confirm
    confirmMove.addEventListener('click', function() {
        const targetCategoryId = modalCategorySelect.value;
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;

        if (!targetCategoryId) {
            alert('Please select a target category.');
            return;
        }

        if (confirm(`Move ${checkedCount} selected products to the selected category?`)) {
            document.getElementById('targetCategoryId').value = targetCategoryId;
            massMoveForm.submit();
        }
    });

    // Handle refresh button
    refreshBtn.addEventListener('click', function() {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

        setTimeout(() => {
            location.reload();
        }, 500);
    });

    // Handle delete forms
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
                this.submit();
            }
        });
    });

    // Initialize button state
    updateBulkButton();
});
</script>
@endpush
@endsection
