@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold">Invoice Details - {{ $invoice->invoice_number }}</h2>
        <div class="flex gap-2">
            <a href="{{ route('admin.orders.show', $order) }}" class="text-blue-500 hover:text-blue-700">&larr; Back to Order</a>
            @if($invoice->hasPdf())
                <a href="{{ route('admin.orders.download-invoice', $order) }}"
                   class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Download PDF
                </a>
            @endif
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- Invoice Information -->
        <div>
            <h3 class="text-lg font-semibold mb-2">Invoice Information</h3>
            <div class="bg-gray-50 p-4 rounded">
                <p><strong>Invoice Number:</strong> {{ $invoice->invoice_number }}</p>
                <p><strong>Issue Date:</strong> {{ $invoice->issue_date->format('d.m.Y') }}</p>
                <p><strong>Sale Date:</strong> {{ $invoice->sale_date->format('d.m.Y') }}</p>
                <p><strong>Due Date:</strong> {{ $invoice->due_date->format('d.m.Y') }}</p>
                <p><strong>Status:</strong> 
                    <span class="px-2 py-1 rounded text-sm 
                        @if($invoice->status === 'paid') bg-green-100 text-green-800
                        @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                        @else bg-yellow-100 text-yellow-800 @endif">
                        {{ ucfirst($invoice->status) }}
                    </span>
                </p>
                <p><strong>Payment Method:</strong> {{ $invoice->payment_method }}</p>
                @if($invoice->hasPdf())
                    <p><strong>PDF Generated:</strong> {{ $invoice->pdf_generated_at->format('Y-m-d H:i:s') }}</p>
                @endif
            </div>
        </div>

        <!-- Order Information -->
        <div>
            <h3 class="text-lg font-semibold mb-2">Related Order</h3>
            <div class="bg-gray-50 p-4 rounded">
                <p><strong>Order ID:</strong> 
                    <a href="{{ route('admin.orders.show', $order) }}" class="text-blue-500 hover:text-blue-700">
                        #{{ $order->uuid }}
                    </a>
                </p>
                <p><strong>Order Date:</strong> {{ $order->created_at->format('Y-m-d H:i') }}</p>
                <p><strong>Customer:</strong> {{ $order->first_name }} {{ $order->last_name }}</p>
                <p><strong>Email:</strong> {{ $order->email }}</p>
                <p><strong>Order Status:</strong> 
                    <span class="px-2 py-1 rounded text-sm {{ $order->status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ ucfirst($order->status) }}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Seller Information -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Seller Information</h3>
        <div class="bg-gray-50 p-4 rounded">
            <p><strong>Company:</strong> {{ $invoice->seller_data['company_name'] }}</p>
            <p><strong>Address:</strong> 
                {{ $invoice->seller_data['address']['street'] }} {{ $invoice->seller_data['address']['building_number'] }},
                {{ $invoice->seller_data['address']['post_code'] }} {{ $invoice->seller_data['address']['city'] }}
            </p>
            @if($invoice->seller_data['tax_id'])
                <p><strong>NIP:</strong> {{ $invoice->seller_data['tax_id'] }}</p>
            @endif
            @if($invoice->seller_data['regon'])
                <p><strong>REGON:</strong> {{ $invoice->seller_data['regon'] }}</p>
            @endif
        </div>
    </div>

    <!-- Buyer Information -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Buyer Information</h3>
        <div class="bg-gray-50 p-4 rounded">
            <p><strong>Name:</strong> {{ $invoice->buyer_data['name'] }}</p>
            <p><strong>Address:</strong> 
                {{ $invoice->buyer_data['address']['street'] }} {{ $invoice->buyer_data['address']['building_number'] }},
                {{ $invoice->buyer_data['address']['post_code'] }} {{ $invoice->buyer_data['address']['city'] }}
            </p>
            @if($invoice->buyer_data['email'])
                <p><strong>Email:</strong> {{ $invoice->buyer_data['email'] }}</p>
            @endif
            @if($invoice->buyer_data['phone'])
                <p><strong>Phone:</strong> {{ $invoice->buyer_data['phone'] }}</p>
            @endif
        </div>
    </div>

    <!-- Invoice Items -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Invoice Items</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-3 px-4 border-b text-left">Item</th>
                        <th class="py-3 px-4 border-b text-center">Quantity</th>
                        <th class="py-3 px-4 border-b text-right">Price Net</th>
                        <th class="py-3 px-4 border-b text-center">VAT %</th>
                        <th class="py-3 px-4 border-b text-right">VAT Amount</th>
                        <th class="py-3 px-4 border-b text-right">Price Gross</th>
                        <th class="py-3 px-4 border-b text-right">Total Gross</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->items as $item)
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">{{ $item['name'] }}</td>
                        <td class="py-3 px-4 border-b text-center">{{ $item['quantity'] }}</td>
                        <td class="py-3 px-4 border-b text-right">{{ number_format($item['price_net'], 2) }} {{ $invoice->currency }}</td>
                        <td class="py-3 px-4 border-b text-center">{{ $item['vat_rate'] }}%</td>
                        <td class="py-3 px-4 border-b text-right">{{ number_format($item['vat_amount'], 2) }} {{ $invoice->currency }}</td>
                        <td class="py-3 px-4 border-b text-right">{{ number_format($item['price_gross'], 2) }} {{ $invoice->currency }}</td>
                        <td class="py-3 px-4 border-b text-right font-medium">{{ number_format($item['total_gross'], 2) }} {{ $invoice->currency }}</td>
                    </tr>
                    @endforeach
                </tbody>
                <tfoot class="bg-gray-50">
                    <tr>
                        <td colspan="5" class="py-3 px-4 text-right font-semibold">Subtotal (Net):</td>
                        <td class="py-3 px-4 text-right font-semibold">{{ number_format($invoice->subtotal, 2) }} {{ $invoice->currency }}</td>
                        <td class="py-3 px-4"></td>
                    </tr>
                    <tr>
                        <td colspan="5" class="py-3 px-4 text-right font-semibold">VAT:</td>
                        <td class="py-3 px-4 text-right font-semibold">{{ number_format($invoice->vat_amount, 2) }} {{ $invoice->currency }}</td>
                        <td class="py-3 px-4"></td>
                    </tr>
                    <tr class="border-t-2">
                        <td colspan="5" class="py-3 px-4 text-right font-bold text-lg">TOTAL:</td>
                        <td class="py-3 px-4 text-right font-bold text-lg">{{ number_format($invoice->total, 2) }} {{ $invoice->currency }}</td>
                        <td class="py-3 px-4"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Bank Details -->
    @if(isset($invoice->seller_data['bank_details']))
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Payment Details</h3>
        <div class="bg-blue-50 p-4 rounded">
            <p><strong>Bank:</strong> {{ $invoice->seller_data['bank_details']['bank_name'] }}</p>
            <p><strong>Account Number:</strong> {{ $invoice->seller_data['bank_details']['account_number'] }}</p>
            <p><strong>Payment Reference:</strong> Invoice {{ $invoice->invoice_number }}</p>
        </div>
    </div>
    @endif

    <!-- Notes -->
    @if($invoice->notes)
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Notes</h3>
        <div class="bg-gray-50 p-4 rounded">
            <p>{{ $invoice->notes }}</p>
        </div>
    </div>
    @endif

    <!-- Actions -->
    <div class="flex gap-2 justify-end">
        @if($invoice->hasPdf())
            <a href="{{ route('admin.orders.download-invoice', $order) }}"
               class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                Download PDF
            </a>
            <form action="{{ route('admin.orders.regenerate-invoice-pdf', $order) }}" method="POST" class="inline">
                @csrf
                <button type="submit"
                        class="bg-orange-500 text-white px-6 py-2 rounded hover:bg-orange-600"
                        onclick="return confirm('Are you sure you want to regenerate the invoice PDF?')">
                    Regenerate PDF
                </button>
            </form>
        @else
            <form action="{{ route('admin.orders.generate-invoice', $order) }}" method="POST" class="inline">
                @csrf
                <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    Generate PDF
                </button>
            </form>
        @endif
    </div>
</div>
@endsection
