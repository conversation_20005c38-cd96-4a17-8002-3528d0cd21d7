@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold">Edytuj stronę: {{ $page->title }}</h2>
        <div class="flex space-x-2">
            <a href="{{ route('pages.show', $page->slug) }}" 
               class="text-blue-500 hover:text-blue-700" 
               target="_blank">
                👁️ Podgląd
            </a>
            <a href="{{ route('admin.pages.index') }}" class="text-blue-500 hover:text-blue-700">
                &larr; Powrót do listy stron
            </a>
        </div>
    </div>

    <form action="{{ route('admin.pages.update', $page) }}" method="POST">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Tytuł strony <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                           value="{{ old('title', $page->title) }}" 
                           required>
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Slug -->
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                        Slug URL
                        <span class="text-gray-500 text-xs">(zostanie wygenerowany automatycznie jeśli pusty)</span>
                    </label>
                    <input type="text" 
                           id="slug" 
                           name="slug" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                           value="{{ old('slug', $page->slug) }}">
                    @error('slug')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">
                        Aktualny URL: <code class="bg-gray-100 px-1 rounded">{{ url('/') }}/{{ $page->slug }}</code>
                    </p>
                </div>

                <!-- Content -->
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        Treść strony <span class="text-red-500">*</span>
                    </label>
                    <textarea id="content" 
                              name="content" 
                              rows="20" 
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              required>{{ old('content', $page->content) }}</textarea>
                    @error('content')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Publish Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Ustawienia publikacji</h3>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="hidden" name="is_published" value="0">
                            <input type="checkbox" 
                                   name="is_published" 
                                   value="1" 
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                   {{ old('is_published', $page->is_published) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-gray-700">Opublikowana</span>
                        </label>

                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">
                                Kolejność sortowania
                            </label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   min="0" 
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   value="{{ old('sort_order', $page->sort_order) }}">
                            @error('sort_order')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Ustawienia SEO</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-1">
                                Meta tytuł
                                <span class="text-gray-500 text-xs">(domyślnie tytuł strony)</span>
                            </label>
                            <input type="text" 
                                   id="meta_title" 
                                   name="meta_title" 
                                   maxlength="255"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   value="{{ old('meta_title', $page->meta_title) }}">
                            @error('meta_title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-1">
                                Meta opis
                            </label>
                            <textarea id="meta_description" 
                                      name="meta_description" 
                                      rows="3" 
                                      maxlength="500"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('meta_description', $page->meta_description) }}</textarea>
                            @error('meta_description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-xs text-gray-500 mt-1">Maksymalnie 500 znaków</p>
                        </div>
                    </div>
                </div>

                <!-- Page Info -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Informacje o stronie</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div>
                            <strong>Utworzona:</strong> {{ $page->created_at->format('d.m.Y H:i') }}
                            @if($page->creator)
                                <br>przez {{ $page->creator->first_name }} {{ $page->creator->last_name }}
                            @endif
                        </div>
                        @if($page->updated_at->ne($page->created_at))
                            <div>
                                <strong>Ostatnia modyfikacja:</strong> {{ $page->updated_at->format('d.m.Y H:i') }}
                                @if($page->updater)
                                    <br>przez {{ $page->updater->first_name }} {{ $page->updater->last_name }}
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.pages.index') }}" 
               class="bg-gray-300 text-gray-700 py-2 px-6 rounded-md hover:bg-gray-400 transition-colors">
                Anuluj
            </a>
            <div class="flex space-x-3">
                <button type="submit" 
                        class="bg-blue-500 text-white py-2 px-6 rounded-md hover:bg-blue-600 transition-colors">
                    Zapisz zmiany
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Rich Text Editor -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
    ClassicEditor
        .create(document.querySelector('#content'), {
            toolbar: [
                'heading', '|',
                'bold', 'italic', 'link', '|',
                'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'blockQuote', 'insertTable', '|',
                'undo', 'redo'
            ],
            language: 'pl'
        })
        .catch(error => {
            console.error(error);
        });
</script>
@endsection
