@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-file-text me-2"></i>Zarząd<PERSON><PERSON> stronami
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.pages.create') }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-plus-circle me-1"></i><PERSON><PERSON><PERSON> stron<PERSON>
            </a>
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Odśwież
            </button>
        </div>
    </div>

    <!-- Filters -->
    @include('admin.components.filter', ['filters' => $filters, 'sorts' => $sorts])

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'title', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                           class="text-gray-700 hover:text-gray-900">
                            Tytuł
                            @if(request('sort') === 'title')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'slug', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                           class="text-gray-700 hover:text-gray-900">
                            Slug
                            @if(request('sort') === 'slug')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">Status</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'sort_order', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                           class="text-gray-700 hover:text-gray-900">
                            Kolejność
                            @if(request('sort') === 'sort_order')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'updated_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                           class="text-gray-700 hover:text-gray-900">
                            Ostatnia modyfikacja
                            @if(request('sort') === 'updated_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Akcje</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($pages as $page)
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 border-b">
                        <div class="font-medium">{{ $page->title }}</div>
                        @if($page->meta_description)
                            <div class="text-sm text-gray-500 mt-1">{{ Str::limit($page->meta_description, 80) }}</div>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b">
                        <code class="bg-gray-100 px-2 py-1 rounded text-sm">{{ $page->slug }}</code>
                    </td>
                    <td class="py-3 px-4 border-b">
                        <span class="px-2 py-1 rounded-full text-xs font-medium {{ $page->is_published ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}" title="{{ $page->is_published ? 'Strona opublikowana' : 'Strona ukryta' }}">
                            @if($page->is_published)
                                <i class="bi bi-check-circle me-1"></i>Opublikowana
                            @else
                                <i class="bi bi-x-circle me-1"></i>Ukryta
                            @endif
                        </span>
                    </td>
                    <td class="py-3 px-4 border-b">{{ $page->sort_order }}</td>
                    <td class="py-3 px-4 border-b text-sm text-gray-500">
                        {{ $page->updated_at->format('d.m.Y H:i') }}
                        @if($page->updater)
                            <br><span class="text-xs">przez {{ $page->updater->first_name }} {{ $page->updater->last_name }}</span>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                            <a href="{{ route('pages.show', $page->slug) }}"
                               class="text-blue-500 hover:text-blue-700 text-sm"
                               target="_blank"
                               title="Podgląd">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="{{ route('admin.pages.edit', $page) }}"
                               class="text-green-500 hover:text-green-700 text-sm"
                               title="Edytuj">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <form action="{{ route('admin.pages.toggle-published', $page) }}"
                                  method="POST"
                                  class="inline-block toggle-form">
                                @csrf
                                @method('PATCH')
                                <button type="submit"
                                        class="text-yellow-500 hover:text-yellow-700 text-sm"
                                        title="{{ $page->is_published ? 'Ukryj' : 'Opublikuj' }}">
                                    @if($page->is_published)
                                        <i class="bi bi-eye-slash"></i>
                                    @else
                                        <i class="bi bi-eye"></i>
                                    @endif
                                </button>
                            </form>
                            <form action="{{ route('admin.pages.destroy', $page) }}"
                                  method="POST"
                                  class="inline-block delete-form">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="text-red-500 hover:text-red-700 text-sm"
                                        title="Usuń">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="py-8 px-4 text-center text-gray-500">
                        Brak stron do wyświetlenia.
                        <a href="{{ route('admin.pages.create') }}" class="text-blue-500 hover:text-blue-700 ml-2">
                            Dodaj pierwszą stronę
                        </a>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    @if($pages->hasPages())
        <div class="mt-6">
            {{ $pages->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $pages->firstItem() ?? 0 }} to {{ $pages->lastItem() ?? 0 }}
            of {{ $pages->total() }} pages
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Odświeżanie...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Handle delete forms
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Czy na pewno chcesz usunąć tę stronę? Ta akcja nie może być cofnięta.')) {
                this.submit();
            }
        });
    });

    // Handle toggle forms
    document.querySelectorAll('.toggle-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const button = this.querySelector('button');
            const isPublished = button.querySelector('.bi-eye-slash') !== null;
            const action = isPublished ? 'ukryć' : 'opublikować';

            if (confirm(`Czy na pewno chcesz ${action} tę stronę?`)) {
                this.submit();
            }
        });
    });
});
</script>
@endpush
@endsection
