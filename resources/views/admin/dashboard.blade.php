@extends('admin.layout')

@section('content')
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Admin Dashboard</h1>

    <!-- Dashboard Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold">Total Products</h2>
            <p class="text-gray-600">{{ \App\Models\Product::count() }}</p>
        </div>
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold">Total Categories</h2>
            <p class="text-gray-600">{{ \App\Models\Category::count() }}</p>
        </div>
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold">Total Users</h2>
            <p class="text-gray-600">{{ \App\Models\User::count() }}</p>
        </div>
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold">Delivery Methods</h2>
            <p class="text-gray-600">{{ \App\Models\DeliveryMethod::count() }}</p>
        </div>
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold">Total Orders</h2>
            <p class="text-gray-600">{{ \App\Models\Order::count() }}</p>
        </div>
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold">Total Sales</h2>
            <p class="text-gray-600">{{ number_format(\App\Models\Order::sum('total'), 2) }} PLN</p>
        </div>
    </div>

    <!-- Recent Activity or Additional Stats -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Recent Activity</h2>
        <ul class="list-disc list-inside">
            <li>User John added a new product.</li>
            <li>Category "New Arrivals" was updated.</li>
            <li>Admin updated pricing for several products.</li>
        </ul>
    </div>

    <!-- Quick Links -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Quick Links</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('admin.import') }}" class="bg-green-500 text-white py-2 px-4 rounded text-center hover:bg-green-600">
                Import Products
            </a>
            <a href="{{ route('admin.properties.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Manage Properties</a>
            <a href="{{ route('admin.products.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Manage Products</a>
            <a href="{{ route('admin.product-variants.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Product Variants</a>
            <a href="{{ route('admin.product-variants.generate') }}" class="bg-purple-500 text-white py-2 px-4 rounded text-center hover:bg-purple-600">Generate Variants</a>
            <a href="{{ route('admin.product-dimensions.index') }}" class="bg-indigo-500 text-white py-2 px-4 rounded text-center hover:bg-indigo-600">Generate Dimensions</a>
            <a href="{{ route('admin.producents.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Manage Producents</a>
            <a href="{{ route('admin.categories.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Manage Categories</a>
            <a href="{{ route('admin.discounts.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Manage Discounts</a>
            <a href="#" class="bg-blue-500 text-white py-2 px-4 rounded text-center">View Orders</a>
            <a href="{{ route('admin.users.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">Manage Users</a>
            <a href="{{ route('admin.delivery-methods.index') }}" class="bg-blue-500 text-white py-2 px-4 rounded text-center">
                Manage Delivery Methods
            </a>
        </div>
    </div>
</div>
@endsection
