@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <h2 class="text-xl font-bold mb-4">Users</h2>

    <table class="min-w-full bg-white mt-4">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b">ID</th>
                <th class="py-2 px-4 border-b">
                    <a href="{{ route('admin.users.index', array_merge(request()->query(), ['sort' => 'first_name', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Name @if(request('sort') == 'first_name')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2 px-4 border-b">
                    <a href="{{ route('admin.users.index', array_merge(request()->query(), ['sort' => 'email', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Email @if(request('sort') == 'email')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2 px-4 border-b">Phone</th>
                <th class="py-2 px-4 border-b">
                    <a href="{{ route('admin.users.index', array_merge(request()->query(), ['sort' => 'created_at', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Registered @if(request('sort') == 'created_at')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2 px-4 border-b">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($users as $user)
            <tr>
                <td class="py-2 px-4 border-b">{{ $user->id }}</td>
                <td class="py-2 px-4 border-b">{{ $user->first_name }} {{ $user->last_name }}</td>
                <td class="py-2 px-4 border-b">{{ $user->email }}</td>
                <td class="py-2 px-4 border-b">{{ $user->phone }}</td>
                <td class="py-2 px-4 border-b">{{ $user->created_at->format('Y-m-d H:i') }}</td>
                <td class="py-2 px-4 border-b">
                    <a href="{{ route('admin.users.show', $user) }}" 
                       class="text-blue-500 hover:text-blue-700">View Details</a>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="mt-4">
        {{ $users->links() }}
    </div>
</div>
@endsection 