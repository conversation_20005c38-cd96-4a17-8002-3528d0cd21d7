@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-people me-2"></i>Users Management
        </h2>
        <div class="flex space-x-2">
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
        <form method="GET" action="{{ route('admin.users.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Name, email, or phone..."
                           class="border rounded p-2 w-full">
                </div>

                <!-- Registration Date Range -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Registered From</label>
                    <input type="date"
                           name="date_from"
                           value="{{ request('date_from') }}"
                           class="border rounded p-2 w-full">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Registered To</label>
                    <input type="date"
                           name="date_to"
                           value="{{ request('date_to') }}"
                           class="border rounded p-2 w-full">
                </div>

                <!-- Items per page -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Items per page</label>
                    <select name="per_page" class="border rounded p-2 w-full">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                    </select>
                </div>
            </div>

            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
                <a href="{{ route('admin.users.index') }}" class="bg-gray-300 text-black py-2 px-4 rounded hover:bg-gray-400">
                    <i class="bi bi-x-circle me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'id', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            ID
                            @if(request('sort') === 'id')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'first_name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Name
                            @if(request('sort') === 'first_name')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'email', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Email
                            @if(request('sort') === 'email')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">Contact</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Registered
                            @if(request('sort') === 'created_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
        <tbody>
            @forelse ($users as $user)
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">#{{ $user->id }}</div>
                </td>
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $user->first_name }} {{ $user->last_name }}</div>
                    @if($user->email_verified_at)
                        <div class="text-sm text-green-600 mt-1">
                            <i class="bi bi-check-circle me-1"></i>Verified
                        </div>
                    @else
                        <div class="text-sm text-orange-600 mt-1">
                            <i class="bi bi-exclamation-triangle me-1"></i>Unverified
                        </div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $user->email }}</div>
                    @if($user->orders_count ?? $user->orders->count())
                        <div class="text-sm text-gray-500 mt-1">{{ $user->orders_count ?? $user->orders->count() }} orders</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    @if($user->phone)
                        <div class="font-medium">{{ $user->phone }}</div>
                    @else
                        <span class="text-gray-400 text-sm">Not provided</span>
                    @endif
                    @if($user->city)
                        <div class="text-sm text-gray-500 mt-1">{{ $user->city }}</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-sm text-gray-500">
                    {{ $user->created_at->format('d.m.Y H:i') }}
                    @if($user->last_login_at)
                        <br><span class="text-xs">Last login: {{ $user->last_login_at->format('d.m.Y H:i') }}</span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    <div class="flex justify-center space-x-2">
                        <a href="{{ route('admin.users.show', $user) }}"
                           class="text-blue-500 hover:text-blue-700 text-sm"
                           title="View User Details">
                            <i class="bi bi-eye"></i>
                        </a>
                        @if(!$user->email_verified_at)
                            <button type="button"
                                    class="text-green-500 hover:text-green-700 text-sm verify-email-btn"
                                    data-user-id="{{ $user->id }}"
                                    title="Verify Email">
                                <i class="bi bi-check-circle"></i>
                            </button>
                        @endif
                        <button type="button"
                                class="text-purple-500 hover:text-purple-700 text-sm send-message-btn"
                                data-user-id="{{ $user->id }}"
                                title="Send Message">
                            <i class="bi bi-envelope"></i>
                        </button>
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="6" class="py-8 px-4 text-center text-gray-500">
                    No users found matching your criteria.
                </td>
            </tr>
            @endforelse
        </tbody>
        </table>
    </div>

    @if($users->hasPages())
        <div class="mt-6">
            {{ $users->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }}
            of {{ $users->total() }} users
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Verify email buttons (placeholder for future implementation)
    document.querySelectorAll('.verify-email-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.dataset.userId;
            alert('Email verification functionality will be implemented in a future update.');
        });
    });

    // Send message buttons (placeholder for future implementation)
    document.querySelectorAll('.send-message-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.dataset.userId;
            alert('Send message functionality will be implemented in a future update.');
        });
    });
});
</script>
@endpush
@endsection