@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-building me-2"></i>Producents Management
        </h2>
        <div class="flex space-x-2">
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    @include('admin.components.filter', ['filters' => $filters, 'sorts' => $sorts])

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-center">Logo</th>

                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Producent Name
                            @if(request('sort') === 'name')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>

                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'description', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Description
                            @if(request('sort') === 'description')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>

                    <th class="py-3 px-4 border-b text-center">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'priority', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Priority
                            @if(request('sort') === 'priority')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>

                    <th class="py-3 px-4 border-b text-center">Products</th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
        <tbody>
            @forelse ($producents as $producent)
            <tr class="hover:bg-gray-50">
                <!-- Logo Preview -->
                <td class="py-3 px-4 border-b text-center">
                    @if($producent->preview_logo)
                        <img src="{{ $producent->preview_logo }}"
                             alt="{{ $producent->name }} Logo"
                             class="rounded mx-auto object-cover"
                             style="max-width: 80px; max-height: 80px;">
                    @else
                        <div class="w-20 h-20 bg-gray-100 rounded flex items-center justify-center mx-auto">
                            <i class="bi bi-image text-gray-400 text-2xl"></i>
                        </div>
                    @endif
                </td>

                <!-- Name -->
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $producent->name }}</div>
                    @if($producent->website)
                        <div class="text-sm text-blue-500 mt-1">
                            <a href="{{ $producent->website }}" target="_blank" class="hover:text-blue-700">
                                <i class="bi bi-globe me-1"></i>{{ parse_url($producent->website, PHP_URL_HOST) }}
                            </a>
                        </div>
                    @endif
                </td>

                <!-- Description -->
                <td class="py-3 px-4 border-b">
                    @if($producent->description)
                        <div class="text-sm">{{ Str::limit($producent->description, 120) }}</div>
                    @else
                        <span class="text-gray-400 text-sm">No description</span>
                    @endif
                </td>

                <!-- Priority -->
                <td class="py-3 px-4 border-b text-center">
                    @if($producent->priority)
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" title="Priority producent">
                            <i class="bi bi-star-fill me-1"></i>Priority
                        </span>
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800" title="Standard producent">
                            <i class="bi bi-dash me-1"></i>Standard
                        </span>
                    @endif
                </td>

                <!-- Products Count -->
                <td class="py-3 px-4 border-b text-center">
                    @php
                        $productCount = $producent->products()->count();
                    @endphp
                    @if($productCount > 0)
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" title="{{ $productCount }} products">
                            <i class="bi bi-box-seam me-1"></i>{{ $productCount }}
                        </span>
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800" title="No products">
                            <i class="bi bi-dash me-1"></i>0
                        </span>
                    @endif
                </td>

                <!-- Actions -->
                <td class="py-3 px-4 border-b text-center">
                    <div class="flex justify-center space-x-2">
                        @if($producent->website)
                            <a href="{{ $producent->website }}"
                               target="_blank"
                               class="text-blue-500 hover:text-blue-700 text-sm"
                               title="Visit Website">
                                <i class="bi bi-globe"></i>
                            </a>
                        @endif
                        <a href="{{ route('admin.producents.edit', $producent) }}"
                           class="text-green-500 hover:text-green-700 text-sm"
                           title="Edit Producent">
                            <i class="bi bi-pencil"></i>
                        </a>
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="6" class="py-8 px-4 text-center text-gray-500">
                    No producents found matching your criteria.
                </td>
            </tr>
            @endforelse
        </tbody>
        </table>
    </div>

    @if($producents->hasPages())
        <div class="mt-6">
            {{ $producents->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $producents->firstItem() ?? 0 }} to {{ $producents->lastItem() ?? 0 }}
            of {{ $producents->total() }} producents
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }
});
</script>
@endpush
@endsection
