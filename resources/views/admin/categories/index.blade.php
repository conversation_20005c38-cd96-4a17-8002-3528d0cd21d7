@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <h2 class="text-xl font-bold mb-4">
        {{ $parentCategory ? 'Subcategories of ' . $parentCategory->name : 'Categories' }}
    </h2>
    <a href="{{ route('admin.categories.create') }}" class="bg-blue-500 text-white py-2 px-4 rounded mb-4 inline-block">Add Category</a>

    <!-- Include the filter component with filters data -->
    <form method="GET" action="{{ route('admin.categories.index', ['parentId' => $parentCategory ? $parentCategory->id : null]) }}">
        @include('admin.components.filter', ['filters' => $filters])
    </form>
    <!-- Categories Table -->
    <table class="min-w-full bg-white mt-4">
        <thead>
            <tr>
                <th class="py-2"><input type="checkbox" id="select-all"></th>
                <th class="py-2">
                    <a href="{{ route('admin.categories.index', array_merge(request()->query(), ['sort' => 'name', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Name @if(request('sort') == 'name')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2">
                    <a href="{{ route('admin.categories.index', array_merge(request()->query(), ['sort' => 'description', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Description @if(request('sort') == 'description')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2">
                    <a href="{{ route('admin.categories.index', array_merge(request()->query(), ['sort' => 'sort', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Sort Order @if(request('sort') == 'sort')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2">
                    <a href="{{ route('admin.categories.index', array_merge(request()->query(), ['sort' => 'created_at', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc'])) }}" class="text-blue-500">
                        Created @if(request('sort') == 'created_at')
                            <span>{{ request('direction') == 'asc' ? '🔼' : '🔽' }}</span>
                        @endif
                    </a>
                </th>
                <th class="py-2">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($categories as $category)
            <tr>
                <td class="py-2"><input type="checkbox" name="selected_categories[]" value="{{ $category->id }}"></td>
                <td class="py-2">
                    <a href="{{ route('admin.categories.index', ['parentId' => $category->id]) }}" class="text-blue-500">
                        {{ $category->name }}
                    </a>
                </td>
                <td class="py-2">{{ $category->description }}</td>
                <td class="py-2">{{ $category->sort ?? 'N/A' }}</td>
                <td class="py-2">{{ $category->created_at->format('Y-m-d H:i') }}</td>
                <td class="py-2">
                    <a href="{{ route('admin.categories.edit', $category->id) }}" class="text-blue-500">Edit</a>
                    
                    <form action="{{ route('admin.categories.destroy', $category->id) }}" method="POST" class="inline-block">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="text-red-500 ml-2" 
                            onclick="return confirm('Are you sure? Products will be moved to parent category.')">
                            Delete
                        </button>
                    </form>

                    @if($category->children->count() > 0)
                        <form action="{{ route('admin.categories.delete-with-children', $category->id) }}" 
                              method="POST" class="inline-block">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-500 ml-2" 
                                onclick="return confirm('Are you sure? All child categories will be deleted and their products moved to this category.')">
                                Delete with Children
                            </button>
                        </form>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    <div class="mt-4">
        <form id="moveCategoriesForm" action="" method="POST">
            @csrf
            <label for="destination_category" class="block text-gray-700">Move to Category</label>
            <select name="destination_category" id="destination_category" class="w-full border rounded p-2">
                <option value="" disabled selected>Select a category</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}">{{ $category->name }}</option>
                @endforeach
            </select>
            <button type="submit" class="mt-2 bg-blue-500 text-white p-2 rounded">Move Selected Categories</button>
        </form>
    </div>

    <script>
        // JavaScript to handle "Select All" functionality
        document.getElementById('select-all').addEventListener('click', function(event) {
            let checkboxes = document.querySelectorAll('input[name="selected_categories[]"]');
            checkboxes.forEach(checkbox => checkbox.checked = event.target.checked);
        });
    </script>
    <!-- Pagination Links -->
    <div class="mt-4">
        {{ $categories->links() }}
    </div>

    <!-- Back Button to Parent Category -->
    @if($parentCategory && $parentCategory->parent)
        <a href="{{ route('admin.categories.index', ['parentId' => $parentCategory->parent->id]) }}" class="text-blue-500">
            Back to {{ $parentCategory->parent->name }}
        </a>
    @elseif($parentCategory)
        <a href="{{ route('admin.categories.index') }}" class="text-blue-500">Back to Categories</a>
    @endif
</div>
@endsection
