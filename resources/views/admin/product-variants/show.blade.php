@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Product Variant: {{ $productVariant->name ?: 'Unnamed Variant' }}</h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.product-variants.edit', $productVariant) }}" 
               class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                Edit Variant
            </a>
            <a href="{{ route('admin.product-variants.index') }}" 
               class="text-blue-500 hover:text-blue-700">
                &larr; Back to Variants
            </a>
        </div>
    </div>

    <!-- Variant Information -->
    <div class="mb-8 p-4 bg-gray-50 rounded">
        <h3 class="text-lg font-semibold mb-2">Variant Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <strong>Name:</strong> {{ $productVariant->name ?: 'Unnamed Variant' }}
            </div>
            <div>
                <strong>Display Title:</strong>
                @if($productVariant->show_name)
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">{{ $productVariant->show_name }}</span>
                @else
                    <span class="text-gray-400">Default ("Dostępne warianty:")</span>
                @endif
            </div>
            <div>
                <strong>Products Count:</strong> {{ $productVariant->products->count() }}
            </div>
            <div>
                <strong>Created:</strong> {{ $productVariant->created_at->format('Y-m-d H:i:s') }}
            </div>
            <div>
                <strong>Last Updated:</strong> {{ $productVariant->updated_at->format('Y-m-d H:i:s') }}
            </div>
        </div>
    </div>

    <!-- Products in this Variant -->
    <div>
        <h3 class="text-lg font-semibold mb-4">Products in this Variant</h3>
        
        @if($productVariant->products->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full table-auto border">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="px-4 py-2 text-left">ID</th>
                            <th class="px-4 py-2 text-left">Product Name</th>
                            <th class="px-4 py-2 text-left">SKU</th>
                            <th class="px-4 py-2 text-left">Variant Name</th>
                            <th class="px-4 py-2 text-left">Price</th>
                            <th class="px-4 py-2 text-left">Stock</th>
                            <th class="px-4 py-2 text-left">Category</th>
                            <th class="px-4 py-2 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($productVariant->products as $product)
                            <tr class="border-b hover:bg-gray-50">
                                <td class="px-4 py-2">{{ $product->id }}</td>
                                <td class="px-4 py-2">
                                    <div class="font-medium">{{ $product->name }}</div>
                                    @if($product->short_description)
                                        <div class="text-sm text-gray-500">{{ Str::limit($product->short_description, 50) }}</div>
                                    @endif
                                </td>
                                <td class="px-4 py-2">{{ $product->sku }}</td>
                                <td class="px-4 py-2">
                                    @if($product->variant_name)
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                            {{ $product->variant_name }}
                                        </span>
                                    @else
                                        <span class="text-gray-400">Not set</span>
                                    @endif
                                </td>
                                <td class="px-4 py-2">${{ number_format($product->price, 2) }}</td>
                                <td class="px-4 py-2">
                                    @if($product->stock > 0)
                                        <span class="text-green-600">{{ $product->stock }}</span>
                                    @else
                                        <span class="text-red-600">Out of stock</span>
                                    @endif
                                </td>
                                <td class="px-4 py-2">
                                    {{ $product->category ? $product->category->name : 'N/A' }}
                                </td>
                                <td class="px-4 py-2">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('admin.products.edit', $product) }}" 
                                           class="text-blue-500 hover:text-blue-700 text-sm">Edit</a>
                                        @if($product->url)
                                            <a href="{{ $product->url }}" 
                                               target="_blank"
                                               class="text-green-500 hover:text-green-700 text-sm">View</a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-8 text-gray-500">
                <p class="mb-4">No products in this variant yet.</p>
                <a href="{{ route('admin.product-variants.edit', $productVariant) }}" 
                   class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    Add Products
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
