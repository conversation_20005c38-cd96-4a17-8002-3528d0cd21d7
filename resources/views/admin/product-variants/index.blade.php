@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-collection me-2"></i>Product Variants Management
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.product-variants.generate') }}" class="bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600">
                <i class="bi bi-magic me-1"></i>Generate Variants
            </a>
            <a href="{{ route('admin.product-variants.create') }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-plus-circle me-1"></i>Create Variant
            </a>
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
        </div>
    @endif

    <!-- Filters -->
    @include('admin.components.filter', ['filters' => $filters, 'sorts' => $sorts])

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'id', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            ID
                            @if(request('sort') === 'id')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Variant Name
                            @if(request('sort') === 'name')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'show_name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Display Title
                            @if(request('sort') === 'show_name')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Products Count</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Created
                            @if(request('sort') === 'created_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($variants as $variant)
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">
                            <div class="font-medium">#{{ $variant->id }}</div>
                        </td>
                        <td class="py-3 px-4 border-b">
                            <div class="font-medium">{{ $variant->name ?: 'Unnamed Variant' }}</div>
                            @if($variant->description)
                                <div class="text-sm text-gray-500 mt-1">{{ Str::limit($variant->description, 60) }}</div>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b">
                            @if($variant->show_name)
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="Custom display title">
                                    <i class="bi bi-tag me-1"></i>{{ $variant->show_name }}
                                </span>
                            @else
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800" title="Uses default display">
                                    <i class="bi bi-dash me-1"></i>Default
                                </span>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b text-center">
                            @php
                                $productCount = $variant->products()->count();
                            @endphp
                            @if($productCount > 0)
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" title="{{ $productCount }} products in this variant">
                                    <i class="bi bi-box-seam me-1"></i>{{ $productCount }} products
                                </span>
                            @else
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800" title="No products assigned">
                                    <i class="bi bi-exclamation-triangle me-1"></i>0 products
                                </span>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b text-sm text-gray-500">
                            {{ $variant->created_at->format('d.m.Y H:i') }}
                            @if($variant->updated_at != $variant->created_at)
                                <br><span class="text-xs">Updated: {{ $variant->updated_at->format('d.m.Y H:i') }}</span>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b text-center">
                            <div class="flex justify-center space-x-2">
                                <a href="{{ route('admin.product-variants.show', $variant) }}"
                                   class="text-blue-500 hover:text-blue-700 text-sm"
                                   title="View Variant Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ route('admin.product-variants.edit', $variant) }}"
                                   class="text-green-500 hover:text-green-700 text-sm"
                                   title="Edit Variant">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form action="{{ route('admin.product-variants.destroy', $variant) }}"
                                      method="POST"
                                      class="inline-block delete-form">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="text-red-500 hover:text-red-700 text-sm"
                                            title="Delete Variant">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="py-8 px-4 text-center text-gray-500">
                            No product variants found.
                            <a href="{{ route('admin.product-variants.create') }}" class="text-blue-500 hover:text-blue-700 ml-2">
                                <i class="bi bi-plus-circle me-1"></i>Create your first variant
                            </a>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    @if(method_exists($variants, 'links') && $variants->hasPages())
        <div class="mt-6">
            {{ $variants->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $variants->firstItem() ?? 0 }} to {{ $variants->lastItem() ?? 0 }}
            of {{ $variants->total() }} product variants
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Handle delete forms
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this variant? Products will be unlinked but not deleted. This action cannot be undone.')) {
                this.submit();
            }
        });
    });
});
</script>
@endpush
@endsection
