@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Create Product Variant</h2>
        <a href="{{ route('admin.product-variants.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Variants
        </a>
    </div>

    <form action="{{ route('admin.product-variants.store') }}" method="POST">
        @csrf
        
        <div class="mb-4">
            <label for="name" class="block text-gray-700 font-medium mb-2">Variant Group Name</label>
            <input type="text"
                   id="name"
                   name="name"
                   class="w-full border rounded p-2 @error('name') border-red-500 @enderror"
                   value="{{ old('name') }}"
                   placeholder="e.g., Dog Food Premium Sizes, Collar Colors, etc.">
            @error('name')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
            <p class="text-gray-500 text-sm mt-1">
                Optional: Give this variant group a descriptive name. You can leave this empty and add products first.
            </p>
        </div>

        <div class="mb-4">
            <label for="show_name" class="block text-gray-700 font-medium mb-2">Variant Section Title</label>
            <input type="text"
                   id="show_name"
                   name="show_name"
                   class="w-full border rounded p-2 @error('show_name') border-red-500 @enderror"
                   value="{{ old('show_name') }}"
                   placeholder="e.g., Choose Size, Select Color, Pick Weight, etc.">
            @error('show_name')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
            <p class="text-gray-500 text-sm mt-1">
                Optional: Custom title for the variant section on product pages. If empty, will show "Dostępne warianty:".
            </p>
        </div>

        <div class="flex justify-between">
            <a href="{{ route('admin.product-variants.index') }}" 
               class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                Create Variant
            </button>
        </div>
    </form>
</div>
@endsection
