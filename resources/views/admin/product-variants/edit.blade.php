@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Edit Product Variant</h2>
        <a href="{{ route('admin.product-variants.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Variants
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    <!-- Variant Details Form -->
    <form action="{{ route('admin.product-variants.update', $productVariant) }}" method="POST" class="mb-8">
        @csrf
        @method('PUT')
        
        <div class="mb-4">
            <label for="name" class="block text-gray-700 font-medium mb-2">Variant Group Name</label>
            <input type="text"
                   id="name"
                   name="name"
                   class="w-full border rounded p-2 @error('name') border-red-500 @enderror"
                   value="{{ old('name', $productVariant->name) }}"
                   placeholder="e.g., Dog Food Premium Sizes, Collar Colors, etc.">
            @error('name')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="show_name" class="block text-gray-700 font-medium mb-2">Variant Section Title</label>
            <input type="text"
                   id="show_name"
                   name="show_name"
                   class="w-full border rounded p-2 @error('show_name') border-red-500 @enderror"
                   value="{{ old('show_name', $productVariant->show_name) }}"
                   placeholder="e.g., Choose Size, Select Color, Pick Weight, etc.">
            @error('show_name')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
            <p class="text-gray-500 text-sm mt-1">
                Optional: Custom title for the variant section on product pages. If empty, will show "Dostępne warianty:".
            </p>
        </div>

        <div class="flex justify-between">
            <button type="submit" 
                    class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                Update Variant
            </button>
        </div>
    </form>

    <hr class="my-8">

    <!-- Products in this Variant -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">Products in this Variant ({{ $productVariant->products->count() }})</h3>
        
        @if($productVariant->products->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full table-auto border">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="px-4 py-2 text-left">ID</th>
                            <th class="px-4 py-2 text-left">Product Name</th>
                            <th class="px-4 py-2 text-left">Variant Name</th>
                            <th class="px-4 py-2 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="variant-products">
                        @foreach($productVariant->products as $product)
                            <tr class="border-b" data-product-id="{{ $product->id }}">
                                <td class="px-4 py-2">{{ $product->id }}</td>
                                <td class="px-4 py-2">{{ $product->name }}</td>
                                <td class="px-4 py-2">
                                    <input type="text" 
                                           class="variant-name-input border rounded px-2 py-1 w-full"
                                           value="{{ $product->variant_name }}"
                                           placeholder="e.g., 2kg, Red, Large"
                                           data-product-id="{{ $product->id }}">
                                </td>
                                <td class="px-4 py-2">
                                    <button type="button" 
                                            class="remove-product-btn text-red-500 hover:text-red-700"
                                            data-product-id="{{ $product->id }}">
                                        🗑️ Remove
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-gray-500">No products in this variant yet. Add products below.</p>
        @endif
    </div>

    <hr class="my-8">

    <!-- Add Products Section -->
    <div>
        <h3 class="text-lg font-semibold mb-4">Add Products to Variant</h3>
        
        <!-- Search Filter -->
        <div class="mb-4">
            <input type="text" 
                   id="product-search" 
                   class="w-full border rounded p-2"
                   placeholder="Search products by name or SKU...">
        </div>

        <!-- Available Products -->
        <div id="available-products" class="border rounded">
            <div class="bg-gray-50 px-4 py-2 border-b">
                <strong>Available Products</strong>
            </div>
            <div id="products-list" class="max-h-96 overflow-y-auto">
                <!-- Products will be loaded here via AJAX -->
            </div>
            <div id="products-pagination" class="px-4 py-2 border-t bg-gray-50">
                <!-- Pagination will be loaded here -->
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const variantId = {{ $productVariant->id }};
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    // Load initial products
    loadProducts();
    
    // Search functionality
    let searchTimeout;
    document.getElementById('product-search').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            loadProducts(1, this.value);
        }, 300);
    });
    
    // Load products function
    function loadProducts(page = 1, search = '') {
        const url = `/admin/product-variants/${variantId}/search-products?page=${page}&search=${encodeURIComponent(search)}`;
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                renderProducts(data.products);
                renderPagination(data.pagination, search);
            })
            .catch(error => {
                console.error('Error loading products:', error);
            });
    }
    
    // Render products
    function renderProducts(products) {
        const container = document.getElementById('products-list');
        
        if (products.length === 0) {
            container.innerHTML = '<div class="px-4 py-8 text-center text-gray-500">No products found.</div>';
            return;
        }
        
        container.innerHTML = products.map(product => `
            <div class="px-4 py-3 border-b hover:bg-gray-50 flex justify-between items-center">
                <div>
                    <strong>${product.name}</strong>
                    <div class="text-sm text-gray-500">
                        ID: ${product.id} | SKU: ${product.sku || 'N/A'} | 
                        Category: ${product.category ? product.category.name : 'N/A'}
                    </div>
                </div>
                <button type="button" 
                        class="add-product-btn bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
                        data-product-id="${product.id}">
                    + Add
                </button>
            </div>
        `).join('');
        
        // Add event listeners to add buttons
        container.querySelectorAll('.add-product-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                addProductToVariant(this.dataset.productId);
            });
        });
    }
    
    // Render pagination
    function renderPagination(pagination, search) {
        const container = document.getElementById('products-pagination');
        
        if (pagination.last_page <= 1) {
            container.innerHTML = `Showing ${pagination.total} products`;
            return;
        }
        
        let paginationHtml = `Page ${pagination.current_page} of ${pagination.last_page} (${pagination.total} total) `;
        
        if (pagination.current_page > 1) {
            paginationHtml += `<button class="pagination-btn bg-blue-500 text-white px-2 py-1 rounded text-sm ml-2" data-page="${pagination.current_page - 1}">Previous</button>`;
        }
        
        if (pagination.current_page < pagination.last_page) {
            paginationHtml += `<button class="pagination-btn bg-blue-500 text-white px-2 py-1 rounded text-sm ml-2" data-page="${pagination.current_page + 1}">Next</button>`;
        }
        
        container.innerHTML = paginationHtml;
        
        // Add pagination event listeners
        container.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                loadProducts(parseInt(this.dataset.page), search);
            });
        });
    }
    
    // Add product to variant
    function addProductToVariant(productId) {
        fetch(`/admin/product-variants/${variantId}/add-product`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({ product_id: productId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to show updated product list
            } else {
                alert('Error adding product to variant');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error adding product to variant');
        });
    }
    
    // Remove product from variant
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-product-btn')) {
            const productId = e.target.dataset.productId;
            
            if (confirm('Remove this product from the variant?')) {
                fetch(`/admin/product-variants/${variantId}/remove-product`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ product_id: productId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        e.target.closest('tr').remove();
                        loadProducts(); // Refresh available products
                    } else {
                        alert('Error removing product from variant');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing product from variant');
                });
            }
        }
    });
    
    // Update variant name
    document.addEventListener('blur', function(e) {
        if (e.target.classList.contains('variant-name-input')) {
            const productId = e.target.dataset.productId;
            const variantName = e.target.value;
            
            fetch(`/admin/product-variants/${variantId}/update-product-variant-name`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ 
                    product_id: productId,
                    variant_name: variantName 
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('Error updating variant name');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    }, true);
});
</script>
@endpush
@endsection
