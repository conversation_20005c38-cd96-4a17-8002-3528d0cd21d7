@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-tags me-2"></i>Properties Management
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.properties.create') }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-plus-circle me-1"></i>Add Property
            </a>
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    @include('admin.components.filter', ['filters' => $filters, 'sorts' => $sorts])

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Property Name
                            @if(request('sort') === 'name')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'type', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Type
                            @if(request('sort') === 'type')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Multiple Values</th>
                    <th class="py-3 px-4 border-b text-left">Categories</th>
                    <th class="py-3 px-4 border-b text-left">Options</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Created
                            @if(request('sort') === 'created_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
        <tbody>
            @forelse ($properties as $property)
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $property->name }}</div>
                    @if($property->description)
                        <div class="text-sm text-gray-500 mt-1">{{ Str::limit($property->description, 60) }}</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    <span class="px-2 py-1 rounded-full text-xs font-medium {{ $property->type === 'select' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}" title="Property type">
                        @if($property->type === 'select')
                            <i class="bi bi-list me-1"></i>Select
                        @else
                            <i class="bi bi-input-cursor-text me-1"></i>Text
                        @endif
                    </span>
                </td>
                <td class="py-3 px-4 border-b text-center">
                    @if($property->is_multiple)
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800" title="Allows multiple values">
                            <i class="bi bi-check-circle me-1"></i>Yes
                        </span>
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800" title="Single value only">
                            <i class="bi bi-dash-circle me-1"></i>No
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    @if($property->categories->count() > 0)
                        <div class="flex flex-wrap gap-1">
                            @foreach($property->categories->take(3) as $category)
                                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                                    {{ $category->name }}
                                </span>
                            @endforeach
                            @if($property->categories->count() > 3)
                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600" title="{{ $property->categories->count() }} total categories">
                                    +{{ $property->categories->count() - 3 }} more
                                </span>
                            @endif
                        </div>
                    @else
                        <span class="text-gray-500 text-sm">
                            <i class="bi bi-globe me-1"></i>All categories
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    @if($property->type === 'select' && $property->options->count() > 0)
                        <div class="text-sm">
                            <span class="font-medium">{{ $property->options->count() }} options</span>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ $property->options->take(2)->pluck('value')->implode(', ') }}
                                @if($property->options->count() > 2)
                                    ...
                                @endif
                            </div>
                        </div>
                    @else
                        <span class="text-gray-400 text-sm">
                            <i class="bi bi-dash me-1"></i>N/A
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-sm text-gray-500">
                    {{ $property->created_at->format('d.m.Y H:i') }}
                    @if($property->updated_at != $property->created_at)
                        <br><span class="text-xs">Updated: {{ $property->updated_at->format('d.m.Y H:i') }}</span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    <div class="flex justify-center space-x-2">
                        <a href="{{ route('admin.properties.edit', $property) }}"
                           class="text-green-500 hover:text-green-700 text-sm"
                           title="Edit Property">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <form action="{{ route('admin.properties.destroy', $property) }}"
                              method="POST"
                              class="inline-block delete-form">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="text-red-500 hover:text-red-700 text-sm"
                                    title="Delete Property">
                                <i class="bi bi-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="7" class="py-8 px-4 text-center text-gray-500">
                    No properties found matching your criteria.
                    <a href="{{ route('admin.properties.create') }}" class="text-blue-500 hover:text-blue-700 ml-2">
                        <i class="bi bi-plus-circle me-1"></i>Add first property
                    </a>
                </td>
            </tr>
            @endforelse
        </tbody>
        </table>
    </div>

    @if(method_exists($properties, 'links') && $properties->hasPages())
        <div class="mt-6">
            {{ $properties->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $properties->firstItem() ?? 0 }} to {{ $properties->lastItem() ?? 0 }}
            of {{ $properties->total() }} properties
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Handle delete forms
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this property? This action cannot be undone and will affect all products using this property.')) {
                this.submit();
            }
        });
    });
});
</script>
@endpush
@endsection
