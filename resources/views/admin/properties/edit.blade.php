@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <a href="{{ route('admin.properties.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Properties
        </a>
        <h2 class="text-xl font-bold">Edit Property</h2>
    </div>

    <form action="{{ route('admin.properties.update', $property->id) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="mb-4">
            <label for="name" class="block text-gray-700">Name</label>
            <input type="text" id="name" name="name" class="w-full border rounded p-2" value="{{ $property->name }}" required>
        </div>
        <div class="mb-4">
            <label for="type" class="block text-gray-700">Type</label>
            <select id="type" name="type" class="w-full border rounded p-2">
                <option value="text" {{ $property->type == 'text' ? 'selected' : '' }}>Text</option>
                <option value="select" {{ $property->type == 'select' ? 'selected' : '' }}>Select</option>
            </select>
        </div>
        <div class="mb-4">
            <label for="is_multiple" class="block text-gray-700">Allow Multiple Values</label>
            <input type="checkbox" id="is_multiple" name="is_multiple" value="1" class="form-checkbox" {{ $property->is_multiple ? 'checked' : '' }}>
        </div>
        <div class="mb-4">
            <label for="categories" class="block text-gray-700">Categories</label>
            <select id="categories" name="categories[]" class="w-full border rounded p-2" multiple size="15">
                @foreach ($categories as $category)
                    <option value="{{ $category->id }}"
                        {{ in_array($category->id, old('categories', $property->categories->pluck('id')->toArray())) ? 'selected' : '' }}>
                        {{ $category->name }}
                    </option>
                @endforeach
            </select>
            <small class="text-gray-500">Hold Ctrl (Cmd on Mac) to select multiple categories. Leave empty to show for all categories.</small>
        </div>
        <div class="mb-4">
            <label for="options" class="block text-gray-700">Options (for select type)</label>
            @foreach ($property->options as $option)
            <input type="text" name="options[]" class="w-full border rounded p-2 mb-2" value="{{ $option->value }}">
            @endforeach
            <input type="text" name="options[]" class="w-full border rounded p-2 mb-2" placeholder="Add new option">
        </div>
        <div class="flex justify-between">
            <a href="{{ route('admin.properties.index') }}" class="bg-gray-300 text-gray-700 py-2 px-4 rounded">Cancel</a>
            <button type="submit" name="action" value="update" class="bg-blue-500 text-white py-2 px-4 rounded">Update</button>
            <button type="submit" name="action" value="save" class="bg-green-500 text-white py-2 px-4 rounded">Save</button>
        </div>
    </form>
</div>
@endsection
