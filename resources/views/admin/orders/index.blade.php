@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-cart-check me-2"></i>Orders Management
        </h2>
        <div class="flex space-x-2">
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
        <form method="GET" action="{{ route('admin.orders.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Order ID or customer name..."
                           class="border rounded p-2 w-full">
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" class="border rounded p-2 w-full">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="processing" {{ request('status') === 'processing' ? 'selected' : '' }}>Processing</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>

                <!-- Date Range -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                    <input type="date"
                           name="date_from"
                           value="{{ request('date_from') }}"
                           class="border rounded p-2 w-full">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                    <input type="date"
                           name="date_to"
                           value="{{ request('date_to') }}"
                           class="border rounded p-2 w-full">
                </div>
            </div>

            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
                <a href="{{ route('admin.orders.index') }}" class="bg-gray-300 text-black py-2 px-4 rounded hover:bg-gray-400">
                    <i class="bi bi-x-circle me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'uuid', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Order ID
                            @if(request('sort') === 'uuid')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">Customer</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'total', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Total
                            @if(request('sort') === 'total')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Status</th>
                    <th class="py-3 px-4 border-b text-center">InPost Label</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Date
                            @if(request('sort') === 'created_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
        <tbody>
            @forelse ($orders as $order)
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $order->uuid }}</div>
                    @if($order->payment_method)
                        <div class="text-sm text-gray-500 mt-1">{{ ucfirst($order->payment_method) }}</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $order->user->first_name }} {{ $order->user->last_name }}</div>
                    @if($order->user->email)
                        <div class="text-sm text-gray-500 mt-1">{{ $order->user->email }}</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ number_format($order->total, 2) }} PLN</div>
                    @if($order->items_count ?? $order->items->count())
                        <div class="text-sm text-gray-500 mt-1">{{ $order->items_count ?? $order->items->count() }} items</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    <span class="px-2 py-1 rounded-full text-xs font-medium
                        @if($order->status === 'completed') bg-green-100 text-green-800
                        @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                        @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                        @else bg-gray-100 text-gray-800
                        @endif" title="Order status">
                        @if($order->status === 'completed')
                            <i class="bi bi-check-circle me-1"></i>
                        @elseif($order->status === 'cancelled')
                            <i class="bi bi-x-circle me-1"></i>
                        @elseif($order->status === 'processing')
                            <i class="bi bi-clock me-1"></i>
                        @else
                            <i class="bi bi-dash-circle me-1"></i>
                        @endif
                        {{ ucfirst($order->status) }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b text-center">
                    @if($order->deliveryAddress && $order->deliveryAddress->deliveryMethod && $order->deliveryAddress->deliveryMethod->api_provider === 'inpost')
                        @if($order->inpost_shipment_id)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="InPost label generated">
                                <i class="bi bi-check-circle me-1"></i>Generated
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800" title="InPost label pending">
                                <i class="bi bi-clock me-1"></i>Pending
                            </span>
                        @endif
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600" title="Not applicable">
                            <i class="bi bi-dash me-1"></i>N/A
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-sm text-gray-500">
                    {{ $order->created_at->format('d.m.Y H:i') }}
                    @if($order->updated_at != $order->created_at)
                        <br><span class="text-xs">Updated: {{ $order->updated_at->format('d.m.Y H:i') }}</span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    <div class="flex justify-center space-x-2">
                        <a href="{{ route('admin.orders.show', $order) }}"
                           class="text-blue-500 hover:text-blue-700 text-sm"
                           title="View Order Details">
                            <i class="bi bi-eye"></i>
                        </a>
                        @if($order->status !== 'completed' && $order->status !== 'cancelled')
                            <button type="button"
                                    class="text-green-500 hover:text-green-700 text-sm update-status-btn"
                                    data-order-id="{{ $order->id }}"
                                    title="Update Status">
                                <i class="bi bi-pencil"></i>
                            </button>
                        @endif
                        @if($order->deliveryAddress && $order->deliveryAddress->deliveryMethod && $order->deliveryAddress->deliveryMethod->api_provider === 'inpost' && !$order->inpost_shipment_id)
                            <button type="button"
                                    class="text-purple-500 hover:text-purple-700 text-sm generate-label-btn"
                                    data-order-id="{{ $order->id }}"
                                    title="Generate InPost Label">
                                <i class="bi bi-box-seam"></i>
                            </button>
                        @endif
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="7" class="py-8 px-4 text-center text-gray-500">
                    No orders found matching your criteria.
                </td>
            </tr>
            @endforelse
        </tbody>
        </table>
    </div>

    @if($orders->hasPages())
        <div class="mt-6">
            {{ $orders->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $orders->firstItem() ?? 0 }} to {{ $orders->lastItem() ?? 0 }}
            of {{ $orders->total() }} orders
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            // Simple page reload for now
            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Update status buttons (placeholder for future implementation)
    document.querySelectorAll('.update-status-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            alert('Status update functionality will be implemented in a future update.');
        });
    });

    // Generate label buttons (placeholder for future implementation)
    document.querySelectorAll('.generate-label-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            alert('InPost label generation functionality will be implemented in a future update.');
        });
    });
});
</script>
@endpush
@endsection