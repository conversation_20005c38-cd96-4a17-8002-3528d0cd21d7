@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Products With Invalid Dimensions</h2>
        <a href="{{ route('admin.product-dimensions.index') }}" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
            ← Back to Dimension Generation
        </a>
    </div>

    <!-- Search Form -->
    <div class="mb-6">
        <form method="GET" action="{{ route('admin.product-dimensions.invalid') }}" class="flex gap-4">
            <input type="text" 
                   name="search" 
                   value="{{ request('search') }}" 
                   placeholder="Search by name or SKU..." 
                   class="border rounded px-3 py-2 flex-1 max-w-md">
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Search
            </button>
            @if(request('search'))
                <a href="{{ route('admin.product-dimensions.invalid') }}" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Clear
                </a>
            @endif
        </form>
    </div>

    <!-- Info Box -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h4 class="font-semibold text-yellow-800 mb-2">Invalid Dimension Criteria</h4>
        <ul class="text-sm text-yellow-700 space-y-1">
            <li>• Length, width, or height ≤ 0.5cm or > 200cm</li>
            <li>• Weight ≤ 0kg or > 100kg</li>
            <li>• These products may need manual review or re-generation</li>
        </ul>
    </div>

    <!-- Products Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        SKU
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Current Dimensions
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Issues
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Updated
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($products as $product)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                {{ Str::limit($product->name, 50) }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $product->sku }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $product->category->name ?? 'No Category' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="text-xs">
                                <span class="{{ ($product->length <= 0.5 || $product->length > 200) ? 'text-red-600 font-bold' : '' }}">
                                    L: {{ $product->length }}cm
                                </span><br>
                                <span class="{{ ($product->width <= 0.5 || $product->width > 200) ? 'text-red-600 font-bold' : '' }}">
                                    W: {{ $product->width }}cm
                                </span><br>
                                <span class="{{ ($product->height <= 0.5 || $product->height > 200) ? 'text-red-600 font-bold' : '' }}">
                                    H: {{ $product->height }}cm
                                </span><br>
                                <span class="{{ ($product->weight <= 0 || $product->weight > 100) ? 'text-red-600 font-bold' : '' }}">
                                    Weight: {{ $product->weight }}kg
                                </span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                            <div class="text-xs">
                                @if($product->length <= 0.5 || $product->length > 200)
                                    • Length out of range<br>
                                @endif
                                @if($product->width <= 0.5 || $product->width > 200)
                                    • Width out of range<br>
                                @endif
                                @if($product->height <= 0.5 || $product->height > 200)
                                    • Height out of range<br>
                                @endif
                                @if($product->weight <= 0 || $product->weight > 100)
                                    • Weight out of range<br>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $product->updated_at->format('Y-m-d H:i') }}
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            @if(request('search'))
                                No products found matching your search.
                            @else
                                No products with invalid dimensions found.
                            @endif
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    @if($products->hasPages())
        <div class="mt-6">
            {{ $products->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $products->firstItem() ?? 0 }} to {{ $products->lastItem() ?? 0 }} 
            of {{ $products->total() }} products with invalid dimensions
        </p>
    </div>
</div>
@endsection
