@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">
            <i class="bi bi-rulers me-2"></i>Product Dimension Generation
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.product-dimensions.products') }}" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                <i class="bi bi-list-ul me-1"></i>View All Products
            </a>
            <button id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh Statistics
            </button>
        </div>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    @if(session('info'))
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
            {{ session('info') }}
        </div>
    @endif

    <!-- Statistics Dashboard -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 class="text-lg font-semibold text-blue-800 mb-2">Total Products</h3>
            <p class="text-3xl font-bold text-blue-600">{{ number_format($stats['total_products']) }}</p>
        </div>

        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
            <h3 class="text-lg font-semibold text-green-800 mb-2">With Dimensions</h3>
            <p class="text-3xl font-bold text-green-600">{{ number_format($stats['products_with_dimensions']) }}</p>
            <p class="text-sm text-green-600">
                {{ $stats['total_products'] > 0 ? round(($stats['products_with_dimensions'] / $stats['total_products']) * 100, 1) : 0 }}%
            </p>
        </div>

        <div class="bg-red-50 p-4 rounded-lg border border-red-200">
            <h3 class="text-lg font-semibold text-red-800 mb-2">Without Dimensions</h3>
            <p class="text-3xl font-bold text-red-600">{{ number_format($stats['products_without_dimensions']) }}</p>
            <p class="text-sm text-red-600">
                {{ $stats['total_products'] > 0 ? round(($stats['products_without_dimensions'] / $stats['total_products']) * 100, 1) : 0 }}%
            </p>
        </div>

        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h3 class="text-lg font-semibold text-yellow-800 mb-2">Valid Dimensions</h3>
            <p class="text-3xl font-bold text-yellow-600">{{ number_format($stats['products_with_reasonable_dimensions']) }}</p>
            <p class="text-sm text-yellow-600">
                {{ $stats['total_products'] > 0 ? round(($stats['products_with_reasonable_dimensions'] / $stats['total_products']) * 100, 1) : 0 }}%
            </p>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="bg-gray-50 p-4 rounded-lg mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Detailed Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-600">Products with invalid dimensions:</p>
                <p class="text-lg font-semibold text-gray-800">
                    {{ number_format($stats['products_with_invalid_dimensions']) }}
                    @if($stats['products_with_invalid_dimensions'] > 0)
                        <a href="{{ route('admin.product-dimensions.invalid') }}" class="text-blue-600 hover:text-blue-800 text-sm ml-2">View →</a>
                    @endif
                </p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Products needing processing:</p>
                <p class="text-lg font-semibold text-gray-800">
                    {{ number_format($stats['products_without_dimensions']) }}
                    @if($stats['products_without_dimensions'] > 0)
                        <a href="{{ route('admin.product-dimensions.missing') }}" class="text-blue-600 hover:text-blue-800 text-sm ml-2">View →</a>
                    @endif
                </p>
            </div>
        </div>
    </div>

    @if($stats['products_without_dimensions'] > 0)
        <!-- Generation Form -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Generate Product Dimensions</h3>
            
            <form id="generateForm" action="{{ route('admin.product-dimensions.generate') }}" method="POST">
                @csrf
                
                <div class="space-y-6">
                    <!-- Processing Mode -->
                    <div class="space-y-4">
                        <h4 class="font-semibold text-gray-700">Processing Mode</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="process_all" value="0" checked class="mr-2">
                                <span class="font-medium">Run Once</span>
                                <span class="text-gray-600 ml-2">- Process a single batch of products</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="process_all" value="1" class="mr-2">
                                <span class="font-medium">Run for All Products</span>
                                <span class="text-gray-600 ml-2">- Process all {{ number_format($stats['products_without_dimensions']) }} products needing dimensions</span>
                            </label>
                        </div>
                    </div>

                    <!-- Batch Size -->
                    <div class="space-y-4">
                        <h4 class="font-semibold text-gray-700">Batch Size</h4>
                        <div>
                            <select name="batch_size" id="batch_size" class="border rounded p-2 w-full max-w-xs">
                                <option value="15">15 products per batch (most conservative)</option>
                                <option value="25" selected>25 products per batch (recommended)</option>
                                <option value="35">35 products per batch (balanced)</option>
                                <option value="50">50 products per batch (faster, higher token usage)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">
                                Batch size is automatically optimized based on content length to stay within API token limits. Smaller batches are more reliable.
                            </p>
                        </div>
                    </div>

                    <!-- Warning Messages -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-800 mb-2">
                            <i class="bi bi-exclamation-triangle me-2"></i>Important Notes
                        </h4>
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li>• This process uses OpenAI API and will consume API credits</li>
                            <li>• Only products without dimensions will be processed</li>
                            <li>• Batches are automatically optimized to stay within API token limits</li>
                            <li>• The job runs in the background - check logs for progress</li>
                            <li>• Processing is idempotent - safe to run multiple times</li>
                            <li>• Includes automatic fallback for products with long descriptions</li>
                        </ul>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex space-x-4">
                        <button type="submit" id="generateBtn" class="bg-blue-500 text-white py-2 px-6 rounded hover:bg-blue-600 font-medium">
                            <i class="bi bi-play-fill me-1"></i>Start Generation
                        </button>
                        <button type="button" onclick="window.location.reload()" class="bg-gray-500 text-white py-2 px-6 rounded hover:bg-gray-600">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    @else
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <h3 class="text-xl font-semibold text-green-800 mb-2">
                <i class="bi bi-check-circle me-2"></i>All Done!
            </h3>
            <p class="text-green-700">All products already have dimensions. No processing needed.</p>
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('generateForm');
    const generateBtn = document.getElementById('generateBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    
    // Confirmation dialog
    if (form) {
        form.addEventListener('submit', function(e) {
            const processAll = document.querySelector('input[name="process_all"]:checked').value === '1';
            const batchSize = document.getElementById('batch_size').value;
            const productsWithoutDimensions = {{ $stats['products_without_dimensions'] }};
            
            let message;
            if (processAll) {
                message = `Start dimension generation for ALL ${productsWithoutDimensions.toLocaleString()} products?\n\nThis will process all products in batches of ${batchSize} and may take a long time. The job will run in the background.\n\nAre you sure?`;
            } else {
                message = `Start dimension generation for a single batch of up to ${batchSize} products?\n\nThis will process one batch and stop. You can run it again to process more products.\n\nContinue?`;
            }
            
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
            
            // Disable button and show loading state
            generateBtn.disabled = true;
            generateBtn.textContent = 'Starting Generation...';
        });
    }
    
    // Refresh statistics
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.textContent = 'Refreshing...';
            
            fetch('{{ route("admin.product-dimensions.status") }}')
                .then(response => response.json())
                .then(data => {
                    // Simple approach - reload the page with updated stats
                    location.reload();
                })
                .catch(error => {
                    console.error('Error refreshing stats:', error);
                    alert('Error refreshing statistics');
                })
                .finally(() => {
                    refreshBtn.disabled = false;
                    refreshBtn.textContent = 'Refresh Statistics';
                });
        });
    }
});
</script>
@endpush
@endsection
