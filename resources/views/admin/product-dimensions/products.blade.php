@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-rulers me-2"></i>Product Dimensions Management
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.product-dimensions.index') }}" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
            </a>
            <button type="button" id="bulkGenerateBtn" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600" disabled>
                <i class="bi bi-play-fill me-1"></i>Generate for Selected
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
        <form method="GET" action="{{ route('admin.product-dimensions.products') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}" 
                           placeholder="Product name or SKU..." 
                           class="border rounded p-2 w-full">
                </div>

                <!-- Dimension Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Dimension Status</label>
                    <select name="dimension_status" class="border rounded p-2 w-full">
                        <option value="all" {{ $dimensionStatus === 'all' ? 'selected' : '' }}>All Products</option>
                        <option value="with_dimensions" {{ $dimensionStatus === 'with_dimensions' ? 'selected' : '' }}>With Dimensions</option>
                        <option value="without_dimensions" {{ $dimensionStatus === 'without_dimensions' ? 'selected' : '' }}>Without Dimensions</option>
                        <option value="valid_dimensions" {{ $dimensionStatus === 'valid_dimensions' ? 'selected' : '' }}>Valid Dimensions</option>
                        <option value="invalid_dimensions" {{ $dimensionStatus === 'invalid_dimensions' ? 'selected' : '' }}>Invalid Dimensions</option>
                    </select>
                </div>

                <!-- Category Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select name="category_id" class="border rounded p-2 w-full">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Items per page -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Items per page</label>
                    <select name="per_page" class="border rounded p-2 w-full">
                        <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
            </div>

            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
                <a href="{{ route('admin.product-dimensions.products') }}" class="bg-gray-300 text-black py-2 px-4 rounded hover:bg-gray-400">
                    <i class="bi bi-x-circle me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Bulk Actions Form -->
    <form id="bulkForm" method="POST" action="{{ route('admin.product-dimensions.generate-selected') }}">
        @csrf
        <input type="hidden" name="batch_size" value="50">
        
        <!-- Products Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white mt-4">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="py-3 px-4 border-b text-left">
                            <input type="checkbox" id="selectAll" class="rounded">
                        </th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'name', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                               class="text-gray-700 hover:text-gray-900">
                                Product Name
                                @if(request('sort') === 'name')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'sku', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                               class="text-gray-700 hover:text-gray-900">
                                SKU
                                @if(request('sort') === 'sku')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'category_id', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                               class="text-gray-700 hover:text-gray-900">
                                Category
                                @if(request('sort') === 'category_id')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-center">Dimension Status</th>
                        <th class="py-3 px-4 border-b text-center">Dimensions</th>
                        <th class="py-3 px-4 border-b text-left">
                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'updated_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}" 
                               class="text-gray-700 hover:text-gray-900">
                                Last Updated
                                @if(request('sort') === 'updated_at')
                                    <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </a>
                        </th>
                        <th class="py-3 px-4 border-b text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($products as $product)
                    @php
                        $hasDimensions = !is_null($product->length) && !is_null($product->width) && !is_null($product->height);
                        $hasValidDimensions = $hasDimensions && 
                            $product->length > 0.5 && $product->length <= 200 &&
                            $product->width > 0.5 && $product->width <= 200 &&
                            $product->height > 0.5 && $product->height <= 200 &&
                            $product->weight > 0 && $product->weight <= 100;
                    @endphp
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">
                            <input type="checkbox" name="selected_products[]" value="{{ $product->id }}" class="product-checkbox rounded">
                        </td>
                        <td class="py-3 px-4 border-b">
                            <div class="font-medium">{{ Str::limit($product->name, 50) }}</div>
                            @if($product->short_description)
                                <div class="text-sm text-gray-500 mt-1">{{ Str::limit($product->short_description, 60) }}</div>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b">
                            <code class="bg-gray-100 px-2 py-1 rounded text-sm">{{ $product->sku }}</code>
                        </td>
                        <td class="py-3 px-4 border-b">
                            {{ $product->category->name ?? 'No Category' }}
                        </td>
                        <td class="py-3 px-4 border-b text-center">
                            @if(!$hasDimensions)
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800" title="Missing dimensions">
                                    <i class="bi bi-dash-circle me-1"></i>Missing
                                </span>
                            @elseif($hasValidDimensions)
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="Valid dimensions">
                                    <i class="bi bi-check-circle me-1"></i>Valid
                                </span>
                            @else
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" title="Invalid dimensions">
                                    <i class="bi bi-exclamation-triangle me-1"></i>Invalid
                                </span>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b text-center">
                            @if($hasDimensions)
                                <div class="text-xs" title="Length × Width × Height (Weight)">
                                    {{ $product->length }}×{{ $product->width }}×{{ $product->height }}cm
                                    <br><span class="text-gray-500">({{ $product->weight }}kg)</span>
                                </div>
                            @else
                                <span class="text-gray-400 text-xs">Not set</span>
                            @endif
                        </td>
                        <td class="py-3 px-4 border-b text-sm text-gray-500">
                            {{ $product->updated_at->format('d.m.Y H:i') }}
                        </td>
                        <td class="py-3 px-4 border-b text-center">
                            <div class="flex justify-center space-x-2">
                                <a href="{{ route('admin.products.show', $product) }}" 
                                   class="text-blue-500 hover:text-blue-700 text-sm" 
                                   title="View Product">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ route('admin.products.edit', $product) }}" 
                                   class="text-blue-500 hover:text-blue-700 text-sm"
                                   title="Edit Product">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @if(!$hasDimensions)
                                    <button type="button" 
                                            class="text-green-500 hover:text-green-700 text-sm generate-single-btn"
                                            data-product-id="{{ $product->id }}"
                                            title="Generate Dimensions">
                                        <i class="bi bi-rulers"></i>
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="py-8 px-4 text-center text-gray-500">
                            No products found matching your criteria.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </form>

    @if($products->hasPages())
        <div class="mt-6">
            {{ $products->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $products->firstItem() ?? 0 }} to {{ $products->lastItem() ?? 0 }} 
            of {{ $products->total() }} products
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    const bulkGenerateBtn = document.getElementById('bulkGenerateBtn');
    const bulkForm = document.getElementById('bulkForm');

    // Handle select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButton();
    });

    // Handle individual checkbox changes
    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkButton();
        });
    });

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        const totalCount = productCheckboxes.length;
        
        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkButton() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        bulkGenerateBtn.disabled = checkedCount === 0;
        
        if (checkedCount > 0) {
            bulkGenerateBtn.textContent = `Generate for ${checkedCount} Selected`;
            bulkGenerateBtn.innerHTML = '<i class="bi bi-play-fill me-1"></i>Generate for ' + checkedCount + ' Selected';
        } else {
            bulkGenerateBtn.innerHTML = '<i class="bi bi-play-fill me-1"></i>Generate for Selected';
        }
    }

    // Handle bulk generate button click
    bulkGenerateBtn.addEventListener('click', function() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        
        if (checkedCount === 0) {
            alert('Please select at least one product.');
            return;
        }

        if (confirm(`Generate dimensions for ${checkedCount} selected products?\n\nThis will start a background job to process the selected products.`)) {
            bulkForm.submit();
        }
    });

    // Handle single product generation (placeholder for future implementation)
    document.querySelectorAll('.generate-single-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.dataset.productId;
            alert('Single product dimension generation will be implemented in a future update.');
        });
    });

    // Initialize button state
    updateBulkButton();
});
</script>
@endpush
@endsection
