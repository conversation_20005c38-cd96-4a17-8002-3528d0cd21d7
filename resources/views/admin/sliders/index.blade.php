@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-images me-2"></i>Sliders Management
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.sliders.create') }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-plus-circle me-1"></i>Add Slider
            </a>
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    @include('admin.components.filter', ['filters' => $filters, 'sorts' => $sorts])

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'title', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Slider Title
                            @if(request('sort') === 'title')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Desktop Image</th>
                    <th class="py-3 px-4 border-b text-center">Mobile Image</th>
                    <th class="py-3 px-4 border-b text-center">Status</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Created
                            @if(request('sort') === 'created_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
        <tbody>
            @forelse ($sliders as $slider)
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b">
                    <div class="font-medium">{{ $slider->title }}</div>
                    @if($slider->link)
                        <div class="text-sm text-blue-500 mt-1">
                            <a href="{{ $slider->link }}" target="_blank" class="hover:text-blue-700">
                                <i class="bi bi-link-45deg me-1"></i>{{ Str::limit($slider->link, 50) }}
                            </a>
                        </div>
                    @endif
                    @if($slider->description)
                        <div class="text-sm text-gray-500 mt-1">{{ Str::limit($slider->description, 60) }}</div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    @if($slider->hasMedia('image'))
                        <div class="flex justify-center">
                            <img src="{{ $slider->getFirstMediaUrl('image', 'thumb') }}"
                                 alt="{{ $slider->title }}"
                                 class="h-16 w-24 object-cover rounded border">
                        </div>
                    @else
                        <div class="flex justify-center">
                            <div class="h-16 w-24 bg-gray-100 rounded border flex items-center justify-center">
                                <i class="bi bi-image text-gray-400"></i>
                            </div>
                        </div>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    @if($slider->hasMedia('mobile_image'))
                        <div class="flex justify-center">
                            <img src="{{ $slider->getFirstMediaUrl('mobile_image', 'thumb') }}"
                                 alt="{{ $slider->title }} Mobile"
                                 class="h-16 w-12 object-cover rounded border">
                        </div>
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800" title="No mobile image">
                            <i class="bi bi-dash me-1"></i>None
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    @if($slider->is_active ?? true)
                        @php
                            $now = now();
                            $isInDateRange = (!$slider->start_date || $now >= $slider->start_date) &&
                                           (!$slider->end_date || $now <= $slider->end_date);
                        @endphp
                        @if($isInDateRange)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="Active and visible">
                                <i class="bi bi-check-circle me-1"></i>Active
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" title="Active but outside date range">
                                <i class="bi bi-clock me-1"></i>Scheduled
                            </span>
                        @endif
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800" title="Inactive">
                            <i class="bi bi-x-circle me-1"></i>Inactive
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-sm text-gray-500">
                    {{ $slider->created_at->format('d.m.Y H:i') }}
                    @if($slider->start_date || $slider->end_date)
                        <br>
                        <span class="text-xs">
                            @if($slider->start_date && $slider->end_date)
                                {{ $slider->start_date->format('d.m.Y') }} - {{ $slider->end_date->format('d.m.Y') }}
                            @elseif($slider->start_date)
                                From: {{ $slider->start_date->format('d.m.Y') }}
                            @elseif($slider->end_date)
                                Until: {{ $slider->end_date->format('d.m.Y') }}
                            @endif
                        </span>
                    @endif
                </td>
                <td class="py-3 px-4 border-b text-center">
                    <div class="flex justify-center space-x-2">
                        @if($slider->link)
                            <a href="{{ $slider->link }}"
                               target="_blank"
                               class="text-blue-500 hover:text-blue-700 text-sm"
                               title="Visit Link">
                                <i class="bi bi-box-arrow-up-right"></i>
                            </a>
                        @endif
                        <a href="{{ route('admin.sliders.edit', $slider) }}"
                           class="text-green-500 hover:text-green-700 text-sm"
                           title="Edit Slider">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <form action="{{ route('admin.sliders.destroy', $slider) }}"
                              method="POST"
                              class="inline-block delete-form">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="text-red-500 hover:text-red-700 text-sm"
                                    title="Delete Slider">
                                <i class="bi bi-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="6" class="py-8 px-4 text-center text-gray-500">
                    No sliders found matching your criteria.
                    <a href="{{ route('admin.sliders.create') }}" class="text-blue-500 hover:text-blue-700 ml-2">
                        <i class="bi bi-plus-circle me-1"></i>Add first slider
                    </a>
                </td>
            </tr>
            @endforelse
        </tbody>
        </table>
    </div>

    @if($sliders->hasPages())
        <div class="mt-6">
            {{ $sliders->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $sliders->firstItem() ?? 0 }} to {{ $sliders->lastItem() ?? 0 }}
            of {{ $sliders->total() }} sliders
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Handle delete forms
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this slider? This action cannot be undone.')) {
                this.submit();
            }
        });
    });
});
</script>
@endpush
@endsection
