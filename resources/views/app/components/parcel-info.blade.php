{{-- Parcel Information Component --}}
@if(isset($deliveryInfo['parcel_count']) && $deliveryInfo['parcel_count'] > 0)
<div class="parcel-info bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
    <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
            </svg>
        </div>
        <div class="flex-1">
            <h4 class="text-sm font-medium text-blue-900 mb-2">
                Informacje o przesyłce
            </h4>
            
            {{-- Parcel Count with Free/Paid Breakdown --}}
            <div class="text-sm text-blue-800 mb-2">
                @if($deliveryInfo['parcel_count'] == 1)
                    <span class="font-medium">Twoje zamówienie zostanie wysłane w 1 paczce</span>
                @else
                    <span class="font-medium">Twoje zamówienie zostanie wysłane w {{ $deliveryInfo['parcel_count'] }} paczkach</span>
                @endif

                {{-- Show free/paid parcel breakdown if available --}}
                @if(isset($deliveryInfo['free_parcels']) && isset($deliveryInfo['paid_parcels']))
                    <div class="text-xs text-blue-600 mt-1">
                        @if($deliveryInfo['free_parcels'] > 0 && $deliveryInfo['paid_parcels'] > 0)
                            {{ $deliveryInfo['free_parcels'] }} darmowe + {{ $deliveryInfo['paid_parcels'] }} płatne
                        @elseif($deliveryInfo['free_parcels'] > 0)
                            Wszystkie {{ $deliveryInfo['free_parcels'] }} paczki darmowe
                        @else
                            Wszystkie {{ $deliveryInfo['paid_parcels'] }} paczki płatne
                        @endif
                    </div>
                @endif
            </div>

            {{-- Delivery Cost Breakdown --}}
            @if(isset($deliveryInfo['breakdown']) && !empty($deliveryInfo['breakdown']))
                <div class="text-sm text-blue-700 space-y-1">
                    @foreach($deliveryInfo['breakdown'] as $item)
                        <div>• {{ $item }}</div>
                    @endforeach
                </div>
            @endif

            {{-- Calculation Method Info --}}
            @if(isset($deliveryInfo['calculation_method']))
                <div class="mt-2 text-xs text-blue-600">
                    @if($deliveryInfo['calculation_method'] === 'parcel_based')
                        <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            Kalkulacja oparta na wymiarach produktów
                        </span>
                    @elseif($deliveryInfo['calculation_method'] === 'traditional_fallback')
                        <span class="inline-flex items-center px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                            </svg>
                            Kalkulacja standardowa (fallback)
                        </span>
                    @endif
                </div>
            @endif

            {{-- Free Shipping Progress --}}
            @if(isset($deliveryInfo['amount_left_for_free']) && $deliveryInfo['amount_left_for_free'] > 0)
                <div class="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                    @php
                        $currentFreeParcels = $deliveryInfo['free_parcels'] ?? 0;
                        $totalParcels = $deliveryInfo['parcel_count'] ?? 1;
                        $nextFreeParcelThreshold = ($currentFreeParcels + 1) * 149;
                        $currentOrderTotal = $nextFreeParcelThreshold - $deliveryInfo['amount_left_for_free'];
                    @endphp

                    <div class="text-sm text-green-800">
                        @if($currentFreeParcels < $totalParcels)
                            <strong>Dodaj produkty za {{ number_format($deliveryInfo['amount_left_for_free'], 2) }} PLN</strong>
                            aby otrzymać kolejną darmową paczkę!
                            @if($currentFreeParcels > 0)
                                <div class="text-xs mt-1">
                                    Masz już {{ $currentFreeParcels }} darmowe paczki
                                </div>
                            @endif
                        @else
                            <strong>Wszystkie paczki są darmowe!</strong>
                        @endif
                    </div>

                    @if($currentFreeParcels < $totalParcels)
                        <div class="mt-2">
                            <div class="bg-green-200 rounded-full h-2">
                                @php
                                    $progress = ($currentOrderTotal / $nextFreeParcelThreshold) * 100;
                                @endphp
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ min(100, $progress) }}%"></div>
                            </div>
                            <div class="text-xs text-green-600 mt-1">
                                {{ number_format(min(100, $progress), 1) }}% do kolejnej darmowej paczki
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            {{-- Multiple Parcels Information --}}
            @if($deliveryInfo['parcel_count'] > 1)
                <div class="mt-3 p-3 bg-amber-50 border border-amber-200 rounded">
                    <div class="flex items-start space-x-2">
                        <svg class="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <div class="text-sm text-amber-800">
                            <strong>Informacja:</strong> Zamówienie wymaga {{ $deliveryInfo['parcel_count'] }} paczek.
                            @if(isset($deliveryInfo['paid_parcels']) && $deliveryInfo['paid_parcels'] > 0)
                                @if(isset($deliveryInfo['free_parcels']) && $deliveryInfo['free_parcels'] > 0)
                                    {{ $deliveryInfo['free_parcels'] }} darmowe, {{ $deliveryInfo['paid_parcels'] }} płatne ({{ number_format($deliveryInfo['paid_parcels'] * 14.99, 2) }} PLN).
                                @else
                                    Wszystkie {{ $deliveryInfo['paid_parcels'] }} paczki płatne ({{ number_format($deliveryInfo['paid_parcels'] * 14.99, 2) }} PLN).
                                @endif
                            @elseif(isset($deliveryInfo['free_parcels']) && $deliveryInfo['free_parcels'] > 0)
                                Wszystkie {{ $deliveryInfo['free_parcels'] }} paczki darmowe!
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endif

{{-- Delivery Cost Summary --}}
<div class="delivery-cost-summary">
    @if(isset($deliveryInfo['is_free']) && $deliveryInfo['is_free'])
        <div class="flex justify-between items-center text-green-600 font-medium">
            <span>Dostawa:</span>
            <span>GRATIS</span>
        </div>
    @else
        <div class="flex justify-between items-center">
            <span>Dostawa:</span>
            <span class="font-medium">{{ number_format($deliveryInfo['cost'] ?? 0, 2) }} PLN</span>
        </div>
    @endif
    
    @if(isset($deliveryInfo['description']))
        <div class="text-sm text-gray-600 mt-1">
            {{ $deliveryInfo['description'] }}
        </div>
    @endif
</div>

{{-- Debug Information (only in development) --}}
@if(config('app.debug') && isset($deliveryInfo['calculation_method']))
    <details class="mt-4 text-xs text-gray-500">
        <summary class="cursor-pointer">Debug: Delivery Calculation Details</summary>
        <div class="mt-2 p-2 bg-gray-100 rounded text-xs">
            <div><strong>Method:</strong> {{ $deliveryInfo['calculation_method'] }}</div>
            <div><strong>Parcel Count:</strong> {{ $deliveryInfo['parcel_count'] ?? 'N/A' }}</div>
            <div><strong>Total Weight:</strong> {{ $deliveryInfo['total_weight'] ?? 'N/A' }} kg</div>
            @if(isset($deliveryInfo['first_parcel_cost']))
                <div><strong>First Parcel Cost:</strong> {{ $deliveryInfo['first_parcel_cost'] }} PLN</div>
            @endif
            @if(isset($deliveryInfo['additional_parcels_cost']))
                <div><strong>Additional Parcels Cost:</strong> {{ $deliveryInfo['additional_parcels_cost'] }} PLN</div>
            @endif
        </div>
    </details>
@endif
