@auth
    @if(auth()->user()->deliveryAddresses->isNotEmpty())
        <div class="existing-addresses mb-4">
            <h5>W<PERSON>bierz zapisany adres</h5>
            
            @foreach(auth()->user()->deliveryAddresses as $address)
                <div class="custom-control custom-radio address-option mb-2 align-items-center">
                    <input type="radio"
                        class="custom-control-input"
                        id="address_{{ $address->id }}"
                        name="delivery_address_choice"
                        value="{{ $address->id }}"
                        {{ old('delivery_address_choice', 'new') == $address->id ? 'checked' : ($address->is_default && !old('delivery_address_choice') ? 'checked' : '') }}
                        onchange="handleDeliveryAddressChange(this)">
                    <label class="custom-control-label" for="address_{{ $address->id }}">
                        <strong>{{ $address->deliveryMethod->name }}</strong>
                        {{ $address->full_address }}
                    </label>
                </div>
            @endforeach

            <div class="custom-control custom-radio address-option mb-2">
                <input type="radio"
                    class="custom-control-input"
                    id="address_new"
                    name="delivery_address_choice"
                    value="new"
                    {{ old('delivery_address_choice') == 'new' ? 'checked' : '' }}
                    onchange="handleDeliveryAddressChange(this)">
                <label class="custom-control-label" for="address_new">
                    <strong>Nowy adres dostawy</strong>
                </label>
            </div>
        </div>
    @endif
@endauth

<!-- New address form -->
<div id="new-address-form" class="{{ (auth()->check() && auth()->user()->deliveryAddresses->isNotEmpty() && old('delivery_address_choice') && old('delivery_address_choice') != 'new') ? 'd-none' : '' }}">
    @include('app.catalog.checkout.delivery-address-form')
</div>

<style>
.address-option {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin-bottom: 10px;
    background-color: #fff;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.address-option:hover {
    background-color: #f8f9fa;
}

.address-option.custom-control {
    padding-left: 2rem;
    min-height: auto;
    display: flex;
    align-items: center;
}

.address-option .custom-control-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 0;
    width: 100%;
    padding: 0;
    cursor: pointer;
}

.address-option .custom-control-label::before,
.address-option .custom-control-label::after {
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.address-option input:checked + label {
    font-weight: bold;
}

/* Add background color to the entire div when radio is checked */
.address-option:has(.custom-control-input:checked) {
    background-color: #f8f9fa;
}
</style>

@push('scripts')
<script>
function toggleNewAddressForm(radio) {
    $('#new-address-form').toggleClass('d-none', $(radio).val() !== 'new');
}

// Initialize form visibility on page load
$(document).ready(function() {
    const selectedAddress = $('input[name="delivery_address_choice"]:checked');
    if (selectedAddress.length) {
        toggleNewAddressForm(selectedAddress[0]);
    }

    // Make entire address-option div clickable
    $('.address-option').on('click', function(e) {
        if (!$(e.target).hasClass('custom-control-input')) {
            const $radio = $(this).find('input[type="radio"]');
            $radio.prop('checked', true).trigger('change');
        }
    });
});
</script>
@endpush 