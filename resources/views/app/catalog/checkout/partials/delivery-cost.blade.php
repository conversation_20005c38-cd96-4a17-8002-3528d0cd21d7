<span>Ko<PERSON>t dostawy</span>
<div class="text-end">
    <div><PERSON><PERSON><PERSON> przesyłki</div>
    @if($deliveryInfo['is_free'] ?? false)
        <strong class="text-success">Dostawa gratis</strong>
    @else
        <strong>{{ number_format($deliveryInfo['cost'] ?? $deliveryMethod->price, 2, ',', ' ') }} zł</strong>
    @endif

    {{-- Show parcel count if available --}}
    @if(isset($deliveryInfo['parcel_count']) && $deliveryInfo['parcel_count'] > 1)
        <small class="d-block text-muted">
            {{ $deliveryInfo['parcel_count'] }} paczek
        </small>
    @endif

    {{-- Free shipping progress --}}
    @if(isset($deliveryInfo['amount_left_for_free']) && $deliveryInfo['amount_left_for_free'] > 0)
        <small class="d-block text-muted">
            Dodaj produkty za {{ number_format($deliveryInfo['amount_left_for_free'], 2, ',', ' ') }} zł,
            aby o<PERSON><PERSON><PERSON><PERSON> darmową dostawę
        </small>
    @endif

    {{-- Delivery cost breakdown for multiple parcels --}}
    @if(isset($deliveryInfo['breakdown']) && !empty($deliveryInfo['breakdown']))
        <small class="d-block text-muted mt-1">
            @foreach($deliveryInfo['breakdown'] as $item)
                <div>{{ $item }}</div>
            @endforeach
        </small>
    @endif
</div>