<!-- Filter: {{ $filterItem['name'] }} Range Start -->
<div class="sidebar-widget">
    <h5 class="widget-title">{{ $filterItem['name'] }}</h5>
    <input 
        type="text" 
        class="js-range-slider" 
        name="filter[{{ $filterItem['code'] }}]" 
        value="" 
        data-type="double" 
        data-min="{{ $filterItem['min'] }}" 
        data-max="{{ $filterItem['max'] }}" 
        data-from="{{ $filterItem['from'] }}" 
        data-to="{{ $filterItem['to'] }}" 
        data-grid="true" />
</div>
<!-- Filter: {{ $filterItem['name'] }} Range End -->
