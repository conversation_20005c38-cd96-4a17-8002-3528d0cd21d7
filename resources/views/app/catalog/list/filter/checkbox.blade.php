<div class="sidebar-widget">
    <h5 class="widget-title"> {{ $filterItem['name'] }} </h5>
    <ul class="sidebar-widget-list" id="filter-block-{{ $filterItem['code']}}">
        @foreach ($filterItem['variables'] as $variable)
        <li @if($loop->index > 5) class="more-fields d-none" @endif>
            <div class="custom-control custom-checkbox ">
                <input 
                    name="filter[{{ $filterItem['code']}}][]" 
                    type="checkbox" 
                    class="custom-control-input" 
                    id="custom-check-{{ $filterItem['code']}}-{{$type}}-{{ $variable->id }}" 
                    value="{{ $variable->id }}"
                    @if (isset(request()->filter[$filterItem['code']]) && in_array($variable->id, request()->filter[$filterItem['code']]))
                        checked
                    @endif
                    >
                <label
                    class="custom-control-label"
                    for="custom-check-{{ $filterItem['code']}}-{{$type}}-{{ $variable->id }}">
                    {{ $variable->name ?? $variable->value }}
                </label>
            </div>
        </li>
        @endforeach
    </ul>
    @if (count($filterItem['variables'])>6)
    <a href="#" onclick="toogleFilterMoreAndLess('filter-block-{{ $filterItem['code']}}', this); return false;">więcej</a>
    @endif
</div>