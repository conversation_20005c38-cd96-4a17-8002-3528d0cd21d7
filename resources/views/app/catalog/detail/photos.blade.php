{{-- <div class="andro_product-single-thumb"> --}}
<div class="andro_product-single-slider">
    @if (count($product->getMedia('gallery')) > 1)
        @foreach ($product->getMedia('gallery') as $image)
        <a href="{{ $image->getUrl() }}" class="gallery-thumb">
            {{-- <i class="fa fa-search-plus" aria-hidden="true"></i> --}}
            <img src="{{ $image->getUrl('thumb') }}" alt="{{ $product->name }}">
        </a>
        @endforeach
    @else
        <a href="{{ $product->image_src }}" class="gallery-thumb">
            {{-- <i class="fa fa-search-plus" aria-hidden="true"></i> --}}
            <img src="{{ $product->thumb }}" alt="{{ $product->name }}">
        </a>
    @endif
</div>
@if (count($product->getMedia('gallery')) > 1)
<div class="andro_product-single-slider-nav">
    @foreach ($product->getMedia('gallery') as $image)
        <img src="{{ $image->getUrl('thumb') }}" alt="{{ $product->name }}-thumb">
    @endforeach
</div>
@endif
@push('additional_scripts')
<script>
    $('.andro_product-single-slider').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        fade: true,
        asNavFor: '.andro_product-single-slider-nav'
    });
    $('.andro_product-single-slider-nav').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        asNavFor: '.andro_product-single-slider',
        dots: false,
        centerMode: true,
        focusOnSelect: true
    });
</script>

@endpush
{{-- $('.slider-for').slick({
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    fade: true,
    asNavFor: '.slider-nav'
  });
  $('.slider-nav').slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    asNavFor: '.slider-for',
    dots: true,
    centerMode: true,
    focusOnSelect: true
  }); --}}