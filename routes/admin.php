<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\DiscountController;
use App\Http\Controllers\Admin\ImportController;
use App\Http\Controllers\Admin\PropertyController;
use App\Http\Controllers\Admin\ProducentController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductVariantController;
use App\Http\Controllers\Admin\ProductDimensionController;
use App\Http\Controllers\Admin\SliderController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\InvoiceController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\DeliveryMethodController;
use App\Http\Controllers\Admin\EmailPreviewController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Middleware\IsAdmin;

// Admin routes
Route::middleware(['auth', IsAdmin::class])->group(function () {
    Route::prefix('admin')->name('admin.')->group(function () {
        
        Route::get('/import', [ImportController::class, 'index'])->name('import');

        // Dashboard or home route for admin
        Route::get('/', function () {
            return view('admin.dashboard');
        })->name('dashboard');

        // Property routes
        Route::resource('properties', PropertyController::class);

        // Product routes
        
        Route::post('/products/mass', [ProductController::class, 'massMove'])
            ->name('products.mass-move');
        
            Route::get('products/search', [ProductController::class, 'search'])->name('products.search');
        Route::resource('products', ProductController::class);
        Route::delete('products/media/{media}', [ProductController::class, 'deleteMedia'])
            ->name('products.media.delete');

        // Product Variants routes
        Route::resource('product-variants', ProductVariantController::class);
        Route::post('product-variants/{productVariant}/add-product', [ProductVariantController::class, 'addProduct'])
            ->name('product-variants.add-product');
        Route::delete('product-variants/{productVariant}/remove-product', [ProductVariantController::class, 'removeProduct'])
            ->name('product-variants.remove-product');
        Route::put('product-variants/{productVariant}/update-product-variant-name', [ProductVariantController::class, 'updateProductVariantName'])
            ->name('product-variants.update-product-variant-name');
        Route::get('product-variants/{productVariant}/search-products', [ProductVariantController::class, 'searchProducts'])
            ->name('product-variants.search-products');

        // Variant Generation routes
        Route::get('product-variants-generate', [ProductVariantController::class, 'generatePage'])
            ->name('product-variants.generate');
        Route::post('product-variants-generate', [ProductVariantController::class, 'generateVariants'])
            ->name('product-variants.generate.start');
        Route::get('product-variants-generate/status', [ProductVariantController::class, 'generationStatus'])
            ->name('product-variants.generate.status');

        // Product Dimension Generation routes
        Route::get('product-dimensions', [ProductDimensionController::class, 'index'])
            ->name('product-dimensions.index');
        Route::post('product-dimensions/generate', [ProductDimensionController::class, 'generate'])
            ->name('product-dimensions.generate');
        Route::get('product-dimensions/status', [ProductDimensionController::class, 'status'])
            ->name('product-dimensions.status');
        Route::get('product-dimensions/missing', [ProductDimensionController::class, 'showProductsWithoutDimensions'])
            ->name('product-dimensions.missing');
        Route::get('product-dimensions/invalid', [ProductDimensionController::class, 'showProductsWithInvalidDimensions'])
            ->name('product-dimensions.invalid');

        // Explicit route for browsing categories by hierarchy
        Route::get('categories/parent/{parentId?}', [CategoryController::class, 'index'])
            ->name('categories.index');
        // Standard resource route, adjusted to avoid conflict with the custom index route
        Route::resource('categories', CategoryController::class)->except(['index']);

        Route::delete('categories/{category}/delete-with-children', [CategoryController::class, 'deleteWithChildren'])
            ->name('categories.delete-with-children');

        Route::resource('producents', ProducentController::class)->only(['index', 'edit', 'update']);
        Route::resource('sliders', SliderController::class)->except(['show']);

        Route::resource('discounts', DiscountController::class);

        // Pages routes
        Route::resource('pages', PageController::class);
        Route::patch('pages/{page}/toggle-published', [PageController::class, 'togglePublished'])->name('pages.toggle-published');

        Route::resource('orders', OrderController::class)->only(['index', 'show']);
        Route::post('orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::post('orders/{order}/inpost-label', [OrderController::class, 'generateInPostLabel'])->name('orders.generate-inpost-label');
        Route::get('orders/{order}/inpost-label/download', [OrderController::class, 'downloadInPostLabel'])->name('orders.download-inpost-label');
        Route::post('orders/{order}/inpost-label/purchase', [OrderController::class, 'purchaseLabel'])->name('orders.purchase-inpost-label');
        Route::post('orders/{order}/check-payment', [OrderController::class, 'checkPaymentStatus'])->name('orders.check-payment');

        // Invoice routes
        Route::post('orders/{order}/invoice/generate', [InvoiceController::class, 'generate'])->name('orders.generate-invoice');
        Route::get('orders/{order}/invoice/download', [InvoiceController::class, 'download'])->name('orders.download-invoice');
        Route::get('orders/{order}/invoice/generate-download', [InvoiceController::class, 'generateAndDownload'])->name('orders.generate-download-invoice');
        Route::get('orders/{order}/invoice', [InvoiceController::class, 'show'])->name('orders.show-invoice');
        Route::post('orders/{order}/invoice/regenerate-pdf', [InvoiceController::class, 'regeneratePdf'])->name('orders.regenerate-invoice-pdf');

        // User routes
        Route::resource('users', UserController::class)->only(['index', 'show']);

        Route::resource('delivery-methods', DeliveryMethodController::class);
        
        // Email Preview Routes
        Route::get('/email-preview', [EmailPreviewController::class, 'index'])
            ->name('email-preview.index');
        Route::get('/email-preview/preview', [EmailPreviewController::class, 'preview'])
            ->name('email-preview.preview');
    });
});
