<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TestController;
use App\Http\Controllers\Shop\CategoriesController;
use App\Http\Controllers\Shop\CheckoutController;
use App\Http\Controllers\Shop\OrderController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;

require __DIR__.'/admin.php';
require __DIR__.'/app.php';
require __DIR__.'/catalog.php';
require __DIR__.'/auth.php';
require __DIR__.'/profile.php';

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::view('/dev', 'dev')->name('dev');

Route::get('/import', [TestController::class, 'index']);

Route::get('/search', [CategoriesController::class, 'search'])->name('search');
Route::get('/search/ajax', [CategoriesController::class, 'ajaxSearch'])->name('search.ajax');
Route::get('/test', function () {
    return view('welcome');
});

Route::group(['prefix' => 'orders', 'as' => 'order.'], function () {
    // Route::get('/', [OrderController::class,     'index'])->name('index');
    Route::get('/{uuid}', [OrderController::class, 'show'])->name('show');
});

// Group all auth routes under custom prefix
Route::prefix('bioforce/849329fdsfdes')->group(function () {
    Route::get('/login', function () {
        return view('auth.login');
    })->name('login');
    
    // Other auth routes
    Route::post('/login', [AuthenticatedSessionController::class, 'store']);
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');
    Route::get('/register', [RegisteredUserController::class, 'create'])->name('register');
    Route::post('/register', [RegisteredUserController::class, 'store']);
    // ... other auth routes
});
// Protected routes
// Route::middleware(['auth'])->group(function () {
    // Your application routes here
    // Route::get('/', [HomeController::class, 'index'])->name('home');
    // ... other routes
// });

// Add route for continuing payment
Route::get('/profile/orders/{uuid}/pay', [App\Http\Controllers\Profile\OrdersController::class, 'continuePayment'])
    ->name('profile.order.pay')
    ->middleware('auth');

// Static pages routes
Route::get('/{slug}', [PageController::class, 'show'])
    ->name('pages.show')
    ->where('slug', '^(?!admin|api|catalog|cart|checkout|konto|orders|bioforce|dev|test|import|search|promocje|producent).*$');
