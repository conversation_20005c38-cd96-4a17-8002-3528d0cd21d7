/* Cart notification */
#cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

#cart-notification.show {
    transform: translateY(0);
    opacity: 1;
}

#cart-notification.success {
    background-color: #4CAF50;
    color: white;
}

#cart-notification.error {
    background-color: #F44336;
    color: white;
}

/* Cart icon animation */
.andro_header-cart.pulse {
    animation: cart-pulse 0.7s ease;
}

@keyframes cart-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Loading state for buttons */
.andro_btn-custom.loading {
    opacity: 0.7;
    cursor: not-allowed;
}