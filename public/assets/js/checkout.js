// Checkout-related functions

// Initialize checkout functionality
$(document).ready(function() {
    // Delivery method change
    $('input[name=delivery]').on('change', function() {
        getAdditionalDelivery();
    });

    // Coupon button click
    $('#coupon-button').on('click', function() {
        checkCoupon();
    });

    // Delivery address selection change
    $('select[name=delivery_address_id]').on('change', function() {
        updateSelectedDelivery();
        updateSummary();
    });

    // Delivery method radio buttons
    $('input[name="delivery_method"]').on('change', function() {
        handleDeliveryMethodChange(this);
    });

    // Saved delivery address selection
    $('input[name="delivery_address_choice"]').on('change', function() {
        handleDeliveryAddressChange(this);
    });
});

function updateCartHtml(data) {
    $('tr.razem td').text(data.cart.priceTotal + ' zł');
    updateHeaderCart(data.cart);
    
    updateTotalsAndDeliveriesInputs(data.cart);
    updateSummary();

    // Update delivery costs if available
    if (data.delivery_cost !== undefined) {
        $('#delivery-td td strong').text(data.delivery_cost + ' zł');
    }

    // Update total with delivery cost
    var total = parseFloat(data.cart.total);
    var deliveryCost = parseFloat($('#delivery-td td strong').text()) || 0;
    var finalTotal = (total + deliveryCost).toFixed(2);
    $('#total-td td strong').text(finalTotal + ' zł');
}

function updateTotalsAndDeliveriesInputs(cart) {
    var total = cart.total;
    updateDeliveryCosts(cart.deliveryPrices);
    $('input[name=total_sum]').val(total);
}

function updateDeliveryCosts(deliveryCosts) {
    for (pr in deliveryCosts) {
        $('#delivery-check-'+pr).data('cost', deliveryCosts[pr]);
        $("label[for='delivery-check-"+pr+"'] span").text('(+' + deliveryCosts[pr] + ' zł)');
    }
}

function getDeliveryCost() {
    var delivery_type = $('input[name=delivery_type_selected]').val();
    var delivery_cost = 0;
    if (delivery_type) {
        delivery_cost = $('#delivery-check-'+delivery_type).data('cost');
    }
    return delivery_cost*1;
}

function updateSummary() {
    var total = $('input[name=total_sum]').val();
    var delivery_cost = getDeliveryCost();
    var totalCost = prepareNumber(total*1 + delivery_cost*1);
    $('#delivery-td td strong').text(delivery_cost + ' zł');
    $('#total-td td strong').text(totalCost + ' zł');
}

function checkCoupon() {
    var code = $('input[name=coupon]').val();
    if (code.length <= 0) {
        return false;
    }
    $.get('/coupon/check', { code: code }, function(data) {
        if (data.error) {
            $('.coupon-error').removeClass('d-none');
            $('.coupon-error').text(data.error);
        } else {
            $('.coupon-error').addClass('d-none');
            updateCartHtml(data);
        }
    });
}

function handleDeliveryMethodChange(radio) {
    const paczkomatSection = document.getElementById('paczkomat-section');
    if (!paczkomatSection) return;
    
    const methodType = radio.getAttribute('data-type');
    
    if (methodType === 'point' || methodType === 'inpost_paczkomat') {
        showPaczkomatSection();
    } else {
        hidePaczkomatSection();
    }
    
    updateDeliveryMethod();
}

function updateDeliveryMethod() {
    const deliveryMethodId = $('input[name="delivery_method_id"]:checked').val();
    if (!deliveryMethodId) return;

    $.ajax({
        url: '/checkout/update-delivery-method',
        type: 'POST',
        data: {
            delivery_method_id: deliveryMethodId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                updateDeliveryDisplay(response);
            }
        },
        error: function() {
            console.error('Error updating delivery method');
        }
    });
}

function handleDeliveryAddressChange(radio) {
    const addressId = radio.value;

    // Toggle new address form visibility
    toggleNewAddressForm(radio);

    // If a saved address is selected, update delivery costs
    if (addressId !== 'new') {
        updateDeliveryAddress(addressId);
    }
}

function updateDeliveryAddress(addressId) {
    $.ajax({
        url: '/checkout/update-delivery-address',
        type: 'POST',
        data: {
            delivery_address_id: addressId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Update delivery method selection if needed
                if (response.delivery_method) {
                    updateDeliveryMethodSelection(response.delivery_method);
                }

                // Update delivery display
                updateDeliveryDisplay(response);
            }
        },
        error: function() {
            console.error('Error updating delivery address');
        }
    });
}

function updateDeliveryDisplay(response) {
    // Update delivery cost section
    if (response.totals && response.totals.deliveryHtml) {
        $('#delivery-cost-section').html(response.totals.deliveryHtml);
    }

    // Update parcel information if available
    if (response.totals && response.totals.parcelHtml) {
        let parcelSection = $('#parcel-info-section');
        if (parcelSection.length === 0) {
            // Create parcel section if it doesn't exist
            $('#delivery-cost-section').after('<div id="parcel-info-section"></div>');
            parcelSection = $('#parcel-info-section');
        }
        parcelSection.html(response.totals.parcelHtml);
    }

    // Update total price
    if (response.totals && response.totals.totalFormatted) {
        $('#total-price').text(response.totals.totalFormatted + ' zł');
    }

    // Update subtotal and delivery cost in summary
    if (response.totals) {
        if (response.totals.subTotal !== undefined) {
            $('#subtotal-price').text(number_format(response.totals.subTotal, 2, ',', ' ') + ' zł');
        }
        if (response.totals.deliveryCost !== undefined) {
            $('#delivery-price').text(number_format(response.totals.deliveryCost, 2, ',', ' ') + ' zł');
        }
    }
}

function updateDeliveryMethodSelection(deliveryMethod) {
    // Update the selected delivery method radio button
    $('input[name="delivery_method_id"][value="' + deliveryMethod.id + '"]').prop('checked', true);

    // Handle paczkomat section visibility
    const paczkomatSection = document.getElementById('paczkomat-section');
    if (paczkomatSection) {
        if (deliveryMethod.type === 'point' || deliveryMethod.type === 'inpost_paczkomat') {
            showPaczkomatSection();
        } else {
            hidePaczkomatSection();
        }
    }
}

function toggleNewAddressForm(radio) {
    const newAddressForm = document.getElementById('new-address-form');
    if (newAddressForm) {
        if (radio.value === 'new') {
            newAddressForm.classList.remove('d-none');
            newAddressForm.style.display = 'block';
        } else {
            newAddressForm.classList.add('d-none');
            newAddressForm.style.display = 'none';
        }
    }
}

function number_format(number, decimals, dec_point, thousands_sep) {
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function(n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);
}
