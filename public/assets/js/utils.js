/**
 * Utility functions used across the application
 */

// Show preloader
function preloaderShow() {
    $('.andro_preloader').removeClass('hidden');
}

// Hide preloader
function preloaderHide() {
    $('.andro_preloader').addClass('hidden');
}

// Format number with proper decimal places
function prepareNumber(number) {
    return number.toLocaleString(
        'en-US',
        { maximumFractionDigits: 2 }
    );
}

// Scroll to element
function scrollToElement(selector) {
    const element = $(selector);
    if (element.length) {
        $('html, body').animate({
            scrollTop: element.offset().top - 100
        }, 500);
    }
}

// Setup CSRF token for AJAX requests
function setupCSRFToken() {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
}

// Initialize form validation
function initializeFormValidation() {
    $('.validate-form').on('submit', function() {
        $(this).find('button[type=submit]').attr('disabled', 'disabled');
        $(this).find('button[type=submit]').addClass('loading');
        return true;
    });
}

// Initialize cookie consent
function initializeCookieConsent() {
    if (!Cookies.get('cookie-consent')) {
        setTimeout(function() {
            $('.cookie-consent-container').addClass('show');
        }, 1000);
    }
    
    $('.cookie-consent-accept').on('click', function() {
        Cookies.set('cookie-consent', 'accepted', { expires: 365 });
        $('.cookie-consent-container').removeClass('show');
    });
}

/**
 * Ensure we don't have duplicate event handlers for quantity controls
 */
// function ensureUniqueQtyHandlers() {
//     // First, remove all existing handlers
//     $('body').off('click', '.qty-add, .qty-subtract, .qty span');
//     $('.qty-add, .qty-subtract, .qty span').off('click');
    
//     // Then attach a single, namespaced handler
//     $('body').on('click.qtyControl', '.qty-add, .qty-subtract', function(e) {
//         alert('test1');
//         e.preventDefault();
//         e.stopPropagation();
        
//         var $button = $(this);
//         var $input = $button.siblings('input[name="qty"]');
//         var currentVal = parseInt($input.val());
        
//         if (isNaN(currentVal)) {
//             currentVal = 1;
//         }
        
//         if ($button.hasClass('qty-add')) {
//             $input.val(currentVal + 1);
//         } else if ($button.hasClass('qty-subtract') && currentVal > 1) {
//             $input.val(currentVal - 1);
//         }
        
//         // Trigger change event to update cart
//         // $input.trigger('change');
        
//         return false;
//     });
    
//     console.log('Quantity handlers initialized');
// }

// Call this function when the document is ready
// $(document).ready(function() {
//     // Wait a short time to ensure all other scripts have run
//     setTimeout(ensureUniqueQtyHandlers, 100);
// });
