# Laravel Testing Environment Setup

This document provides comprehensive instructions for setting up an isolated testing environment for your Laravel application.

## 🎯 Overview

The testing environment is configured to:
- Use SQLite in-memory database (`:memory:`) for fast, isolated tests
- Completely separate test data from production/development databases
- Use array drivers for cache, session, and mail to avoid external dependencies
- Disable external services and APIs during testing
- Provide automatic database cleanup between tests

## 📁 Files Modified/Created

### Configuration Files
- `.env.testing` - Testing environment variables
- `phpunit.xml` - PHPUnit configuration with test database enforcement
- `config/database.php` - Added `sqlite_testing` connection
- `tests/TestCase.php` - Enhanced base test case with safety checks

### Test Files
- `tests/Feature/DatabaseIsolationTest.php` - Verification tests for isolation
- `setup-testing.sh` - Automated setup script

## 🚀 Quick Setup

### 1. Run the Setup Script
```bash
./setup-testing.sh
```

### 2. Verify the Setup
```bash
php artisan test tests/Feature/DatabaseIsolationTest.php
```

## 📋 Manual Setup Steps

If you prefer manual setup or need to understand each step:

### 1. Environment Configuration

The `.env.testing` file is configured with:
- SQLite in-memory database for speed and isolation
- Array drivers for cache, session, and mail
- Disabled external services (APIs, search, etc.)
- Test-specific configurations

### 2. Database Configuration

Added `sqlite_testing` connection in `config/database.php`:
```php
'sqlite_testing' => [
    'driver' => 'sqlite',
    'database' => ':memory:',
    'prefix' => '',
    'foreign_key_constraints' => true,
],
```

### 3. PHPUnit Configuration

Updated `phpunit.xml` to enforce test database usage:
- Forces `DB_CONNECTION=sqlite_testing`
- Sets `DB_DATABASE=:memory:`
- Configures array drivers for performance
- Disables external services

### 4. Base TestCase Enhancements

The `tests/TestCase.php` includes:
- Database connection verification
- Automatic storage faking
- Safety checks to prevent production database access

## 🧪 Running Tests

### Basic Commands
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run specific test file
php artisan test tests/Feature/DatabaseIsolationTest.php

# Run with coverage (requires Xdebug)
php artisan test --coverage

# Run PHPUnit directly
vendor/bin/phpunit
```

### Advanced Options
```bash
# Run tests in parallel (Laravel 8+)
php artisan test --parallel

# Run tests with specific filter
php artisan test --filter=test_database_isolation

# Run tests with verbose output
php artisan test -v

# Stop on first failure
php artisan test --stop-on-failure
```

## 🔒 Database Isolation Strategies

### 1. RefreshDatabase Trait
Most feature tests should use the `RefreshDatabase` trait:
```php
use Illuminate\Foundation\Testing\RefreshDatabase;

class MyFeatureTest extends TestCase
{
    use RefreshDatabase;
    
    // Your tests here
}
```

### 2. DatabaseTransactions Trait
For tests that don't need full database refresh:
```php
use Illuminate\Foundation\Testing\DatabaseTransactions;

class MyTest extends TestCase
{
    use DatabaseTransactions;
    
    // Your tests here
}
```

### 3. Manual Transaction Control
For fine-grained control:
```php
public function test_something()
{
    DB::beginTransaction();
    
    // Your test logic
    
    DB::rollBack(); // In tearDown() method
}
```

## ✅ Verification Checklist

Run these commands to verify your setup:

1. **Check database connection:**
   ```bash
   php artisan test tests/Feature/DatabaseIsolationTest.php::test_using_test_database_connection
   ```

2. **Verify environment variables:**
   ```bash
   php artisan test tests/Feature/DatabaseIsolationTest.php::test_environment_variables_are_set_correctly
   ```

3. **Test database isolation:**
   ```bash
   php artisan test tests/Feature/DatabaseIsolationTest.php::test_database_isolation_between_tests
   ```

4. **Run all verification tests:**
   ```bash
   php artisan test tests/Feature/DatabaseIsolationTest.php
   ```

## 🛡️ Safety Features

### Database Connection Verification
The base `TestCase` includes safety checks that will throw an exception if:
- Tests try to use a non-test database connection
- The database name doesn't contain 'testing' or isn't ':memory:'

### Environment Enforcement
PHPUnit configuration enforces:
- `APP_ENV=testing`
- `DB_CONNECTION=sqlite_testing`
- `DB_DATABASE=:memory:`

### External Service Isolation
All external services are disabled or mocked:
- Mail: Uses array driver (no emails sent)
- Cache: Uses array driver (in-memory only)
- Queue: Uses sync driver (immediate processing)
- APIs: Set to test/sandbox modes

## 🎯 Best Practices

### 1. Test Structure
```php
class MyFeatureTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        // Additional setup
    }
    
    public function test_something(): void
    {
        // Arrange
        $user = User::factory()->create();
        
        // Act
        $response = $this->actingAs($user)->get('/dashboard');
        
        // Assert
        $response->assertStatus(200);
    }
}
```

### 2. Factory Usage
Always use factories for test data:
```php
$user = User::factory()->create(['email' => '<EMAIL>']);
$order = Order::factory()->for($user)->create();
```

### 3. Assertion Best Practices
```php
// Good: Specific assertions
$this->assertEquals('expected', $actual);
$this->assertDatabaseHas('users', ['email' => '<EMAIL>']);

// Better: Custom assertions when available
$response->assertJsonStructure(['data' => ['id', 'name']]);
```

## 🔧 Troubleshooting

### Common Issues

1. **SQLite not installed:**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install php-sqlite3
   
   # macOS with Homebrew
   brew install php
   ```

2. **Permission issues:**
   ```bash
   chmod +x setup-testing.sh
   chmod 755 database/
   ```

3. **Memory issues with large test suites:**
   - Consider using file-based SQLite for debugging
   - Uncomment alternative database config in `.env.testing`

4. **Tests still using production database:**
   - Check that `.env.testing` exists
   - Verify PHPUnit configuration
   - Run verification tests

### Debug Commands
```bash
# Check current environment
php artisan env

# Check database configuration
php artisan tinker
>>> config('database.default')
>>> config('database.connections.sqlite_testing')

# Check if SQLite is available
php -m | grep sqlite
```

## 📚 Additional Resources

- [Laravel Testing Documentation](https://laravel.com/docs/testing)
- [PHPUnit Documentation](https://phpunit.de/documentation.html)
- [Database Testing in Laravel](https://laravel.com/docs/database-testing)

## 🎉 Success!

If all verification tests pass, your testing environment is properly configured and isolated. You can now run tests with confidence that they won't affect your production or development data.

## 📋 Quick Verification Commands

To verify your setup is working correctly:

```bash
# 1. Run the database isolation verification tests
docker-compose exec app php artisan test tests/Feature/DatabaseIsolationTest.php --env=testing

# 2. Run a simple test to verify basic functionality
docker-compose exec app php artisan test tests/Feature/ExampleTest.php --env=testing

# 3. Check that we're using the correct database connection
docker-compose exec app php artisan tinker --env=testing
>>> config('database.default')  // Should return 'sqlite_testing'
>>> config('database.connections.sqlite_testing.database')  // Should return ':memory:'
```

## 🚨 Important Notes

1. **Database Isolation Confirmed**: Tests that fail because of missing data (like the home page test) actually prove that isolation is working correctly - the test database is completely separate from your production data.

2. **Factory Issues**: If you see errors about missing columns (like `name` column in users table), this indicates factory/migration mismatches that need to be fixed in your application, not issues with the testing setup.

3. **Test Data**: Always use factories and seeders to create test data within your tests. Never rely on production data being available in tests.

## 🔧 Next Steps

1. **Fix Factory Issues**: Update your model factories to match your actual database schema
2. **Create Test-Specific Routes**: Consider creating simple routes for testing that don't require complex data
3. **Write More Tests**: Start writing comprehensive tests for your application features
4. **Set up CI/CD**: Configure your continuous integration to use this testing environment

Your testing environment is now properly isolated and ready for use! 🎉
