<?php

namespace Tests\Unit;

use App\Models\Invoice;
use PHPUnit\Framework\TestCase;

class InvoiceFilenameTest extends TestCase
{
    /**
     * Test that invoice PDF filename is properly sanitized
     */
    public function test_pdf_filename_sanitizes_problematic_characters(): void
    {
        // Create a mock invoice with problematic characters in invoice number
        $invoice = new Invoice();
        $invoice->invoice_number = 'FV/2025/06/0001';

        // Get the PDF filename
        $filename = $invoice->pdf_file_name;

        // Assert filename doesn't contain problematic characters
        $this->assertStringNotContainsString('/', $filename, 'Filename should not contain forward slashes');
        $this->assertStringNotContainsString('\\', $filename, 'Filename should not contain backslashes');

        // Assert filename has expected format
        $this->assertStringStartsWith('faktura_', $filename);
        $this->assertStringEndsWith('.pdf', $filename);

        // Assert filename contains sanitized invoice number
        $this->assertEquals('faktura_FV_2025_06_0001.pdf', $filename);
    }

    /**
     * Test various problematic characters are sanitized
     */
    public function test_pdf_filename_sanitizes_all_problematic_characters(): void
    {
        $testCases = [
            'FV/2025/06/0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV\\2025\\06\\0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV:2025:06:0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV*2025*06*0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV?2025?06?0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV"2025"06"0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV<2025>06<0001' => 'faktura_FV_2025_06_0001.pdf',
            'FV|2025|06|0001' => 'faktura_FV_2025_06_0001.pdf',
        ];

        foreach ($testCases as $invoiceNumber => $expectedFilename) {
            $invoice = new Invoice();
            $invoice->invoice_number = $invoiceNumber;

            $filename = $invoice->pdf_file_name;

            $this->assertEquals($expectedFilename, $filename, 
                "Invoice number '{$invoiceNumber}' should generate filename '{$expectedFilename}', got '{$filename}'");
        }
    }

    /**
     * Test that normal characters are preserved
     */
    public function test_pdf_filename_preserves_normal_characters(): void
    {
        $invoice = new Invoice();
        $invoice->invoice_number = 'FV-2025-06-0001';

        $filename = $invoice->pdf_file_name;

        $this->assertEquals('faktura_FV-2025-06-0001.pdf', $filename);
    }
}
