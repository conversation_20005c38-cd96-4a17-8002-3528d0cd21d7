<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\Category;
use App\Models\Producent;
use App\Models\MolosCategory;
use App\Services\MolosProducts;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MolosProductUpdateTest extends TestCase
{
    use RefreshDatabase;

    protected $molosProductsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->molosProductsService = app(MolosProducts::class);
        Storage::fake('public');
    }

    /**
     * Test that existing products are updated with new data including VAT
     */
    public function test_existing_product_is_updated_with_vat(): void
    {
        // Create necessary dependencies
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_visible' => true,
            'price_percentage' => 25
        ]);

        $producent = Producent::create([
            'name' => 'Test Producent',
            'slug' => 'test-producent'
        ]);

        // Create existing product without VAT
        $existingProduct = Product::create([
            'sku' => 'TEST-SKU-001',
            'molos_id' => '12345',
            'name' => 'Old Product Name',
            'slug' => 'old-product-name',
            'description' => 'Old description',
            'price' => 100.00,
            'molos_price' => 80.00,
            'stock' => 5,
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'status' => true,
            'unit' => 'szt.',
            'vat' => null, // No VAT initially
        ]);

        // Mock product data from Molos API with VAT
        $productData = [
            'id' => '12345',
            'symbol' => 'TEST-SKU-001',
            'name' => 'Updated Product Name',
            'desc_long' => 'Updated long description',
            'desc_short' => 'Updated short description',
            'price_gross' => 120.00,
            'store' => 10,
            'weight' => 1.5,
            'category_id' => $category->molos_id ?? '1',
            'producer_id' => $producent->molos_id ?? '1',
            'ean' => '1234567890123',
            'status_id' => 1,
            'unit' => 'kg',
            'vat_value' => 23, // VAT percentage
            'image' => null,
            'gallery' => []
        ];

        // Update the existing product
        $updatedProduct = $this->molosProductsService->updateExistingProduct($existingProduct, $productData);

        // Assert the product was updated successfully
        $this->assertNotNull($updatedProduct);

        // Fields that SHOULD be updated (operational data)
        $this->assertEquals(10, $updatedProduct->stock);
        $this->assertEquals(120.00, $updatedProduct->molos_price);
        $this->assertEquals(23, $updatedProduct->vat); // VAT should be updated

        // Price should be recalculated based on new molos_price and existing percentage
        $expectedPrice = round(120.00 * (1 + (25 / 100)), 2); // 120 * 1.25 = 150.00
        $this->assertEquals($expectedPrice, $updatedProduct->fresh()->price);

        // Fields that should NOT be updated (preserved manually curated content)
        $this->assertEquals('Old Product Name', $updatedProduct->name); // Should remain unchanged
        $this->assertEquals('Old description', $updatedProduct->description); // Should remain unchanged
        $this->assertNull($updatedProduct->short_description); // Should remain unchanged
        $this->assertEquals('szt.', $updatedProduct->unit); // Should remain unchanged
        $this->assertTrue($updatedProduct->status); // Should remain unchanged
    }

    /**
     * Test that VAT field is properly handled during import
     */
    public function test_new_product_import_includes_vat(): void
    {
        // Create necessary dependencies with molos_id
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_visible' => true,
            'price_percentage' => 30,
            'molos_id' => '100'
        ]);

        $molosCategory = MolosCategory::create([
            'molos_id' => '100',
            'name' => 'Test Molos Category',
            'path' => 'test-category'
        ]);

        $producent = Producent::create([
            'name' => 'Test Producent',
            'slug' => 'test-producent',
            'molos_id' => '200'
        ]);

        // Mock product data from Molos API with VAT
        $productData = [
            'id' => '67890',
            'symbol' => 'NEW-SKU-001',
            'name' => 'New Product with VAT',
            'desc_long' => 'New product description',
            'desc_short' => 'Short description',
            'price_gross' => 100.00,
            'store' => 15,
            'weight' => 2.0,
            'category_id' => '100',
            'producer_id' => '200',
            'ean' => '9876543210987',
            'status_id' => 1,
            'unit' => 'szt.',
            'vat_value' => 8, // Different VAT rate
            'image' => null,
            'gallery' => []
        ];

        // Import new product
        $newProduct = $this->molosProductsService->importOneProduct($productData);

        // Assert the product was created successfully with VAT
        $this->assertNotNull($newProduct);
        $this->assertEquals('NEW-SKU-001', $newProduct->sku);
        $this->assertEquals('67890', $newProduct->molos_id);
        $this->assertEquals('New Product with VAT', $newProduct->name);
        $this->assertEquals(130.00, $newProduct->price); // 100 * 1.30 (30% markup)
        $this->assertEquals(100.00, $newProduct->molos_price);
        $this->assertEquals(8, $newProduct->vat); // VAT should be set
        $this->assertEquals('szt.', $newProduct->unit);
    }

    /**
     * Test that VAT field handles null values correctly
     */
    public function test_vat_field_handles_null_values(): void
    {
        // Create necessary dependencies with molos_id
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_visible' => true,
            'price_percentage' => 25,
            'molos_id' => '300'
        ]);

        $molosCategory = MolosCategory::create([
            'molos_id' => '300',
            'name' => 'Test Molos Category',
            'path' => 'test-category'
        ]);

        $producent = Producent::create([
            'name' => 'Test Producent',
            'slug' => 'test-producent',
            'molos_id' => '400'
        ]);

        // Mock product data without VAT value
        $productData = [
            'id' => '11111',
            'symbol' => 'NO-VAT-SKU',
            'name' => 'Product without VAT',
            'desc_long' => 'Description',
            'price_gross' => 50.00,
            'store' => 5,
            'category_id' => '300',
            'producer_id' => '400',
            'status_id' => 1,
            'unit' => 'szt.',
            // No vat_value field
        ];

        // Import product without VAT
        $product = $this->molosProductsService->importOneProduct($productData);

        // Assert the product was created with null VAT
        $this->assertNotNull($product);
        $this->assertNull($product->vat);
    }

    /**
     * Test that price calculation works correctly after molos_price update
     */
    public function test_price_calculation_after_molos_price_update(): void
    {
        // Create category with specific percentage
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_visible' => true,
            'price_percentage' => 50 // 50% markup
        ]);

        $producent = Producent::create([
            'name' => 'Test Producent',
            'slug' => 'test-producent'
        ]);

        // Create existing product with initial molos_price
        $existingProduct = Product::create([
            'sku' => 'PRICE-TEST-001',
            'molos_id' => '99999',
            'name' => 'Price Test Product',
            'slug' => 'price-test-product',
            'description' => 'Test description',
            'price' => 150.00, // Initial price
            'molos_price' => 100.00, // Initial molos price
            'stock' => 5,
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'status' => true,
            'unit' => 'szt.',
            'vat' => 23,
        ]);

        // Mock product data with updated molos_price
        $productData = [
            'id' => '99999',
            'symbol' => 'PRICE-TEST-001',
            'price_gross' => 200.00, // New molos price
            'store' => 8, // New stock
            'vat_value' => 8, // New VAT
        ];

        // Update the existing product
        $updatedProduct = $this->molosProductsService->updateExistingProduct($existingProduct, $productData);

        // Assert the product was updated successfully
        $this->assertNotNull($updatedProduct);

        // Check that molos_price was updated
        $this->assertEquals(200.00, $updatedProduct->molos_price);

        // Check that stock was updated
        $this->assertEquals(8, $updatedProduct->stock);

        // Check that VAT was updated
        $this->assertEquals(8, $updatedProduct->vat);

        // Check that price was recalculated correctly
        // Expected: 200.00 * (1 + 50/100) = 200.00 * 1.5 = 300.00
        $freshProduct = $updatedProduct->fresh();
        $this->assertEquals(300.00, $freshProduct->price);

        // Verify the calculation matches the model's method
        $expectedPrice = $freshProduct->calculatePriceFromMolos();
        $this->assertEquals($expectedPrice, $freshProduct->price);
    }
}
