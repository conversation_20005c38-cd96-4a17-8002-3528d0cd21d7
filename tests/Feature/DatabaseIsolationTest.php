<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class DatabaseIsolationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that we're using the correct test database connection.
     */
    public function test_using_test_database_connection(): void
    {
        $connection = DB::getDefaultConnection();
        $database = DB::connection()->getDatabaseName();
        
        // Assert we're using the test connection
        $this->assertEquals('sqlite_testing', $connection, 
            'Tests should use sqlite_testing connection');
        
        // Assert we're using in-memory database
        $this->assertEquals(':memory:', $database, 
            'Tests should use in-memory SQLite database');
    }

    /**
     * Test that the test database is isolated and clean.
     */
    public function test_database_is_clean_and_isolated(): void
    {
        // Check that we have a clean database with expected tables
        $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table'");
        $tableNames = collect($tables)->pluck('name')->toArray();
        
        // Should have migration table and other Laravel tables
        $this->assertContains('migrations', $tableNames);
        
        // Test that we can create and query data without affecting other tests
        DB::table('migrations')->insert([
            'migration' => 'test_migration_' . time(),
            'batch' => 999
        ]);
        
        $count = DB::table('migrations')->where('batch', 999)->count();
        $this->assertEquals(1, $count);
    }

    /**
     * Test that environment variables are correctly set for testing.
     */
    public function test_environment_variables_are_set_correctly(): void
    {
        // Check critical environment variables
        $this->assertEquals('testing', app()->environment());
        $this->assertEquals('sqlite_testing', config('database.default'));
        $this->assertEquals(':memory:', config('database.connections.sqlite_testing.database'));
        
        // Check that external services are disabled
        $this->assertEquals('array', config('mail.default'));
        $this->assertEquals('array', config('cache.default'));
        $this->assertEquals('array', config('session.driver'));
        $this->assertEquals('sync', config('queue.default'));
    }

    /**
     * Test that storage is properly faked for testing.
     */
    public function test_storage_is_faked(): void
    {
        // This test verifies that our TestCase properly sets up fake storage
        // The actual fake setup is done in the base TestCase setUp method
        
        $this->assertTrue(true, 'Storage faking is handled in base TestCase');
    }

    /**
     * Test database transactions work correctly.
     */
    public function test_database_transactions_work(): void
    {
        // Start a transaction
        DB::beginTransaction();
        
        // Insert test data
        DB::table('migrations')->insert([
            'migration' => 'transaction_test_' . time(),
            'batch' => 888
        ]);
        
        // Verify data exists within transaction
        $count = DB::table('migrations')->where('batch', 888)->count();
        $this->assertEquals(1, $count);
        
        // Rollback transaction
        DB::rollBack();
        
        // Verify data was rolled back
        $count = DB::table('migrations')->where('batch', 888)->count();
        $this->assertEquals(0, $count);
    }

    /**
     * Test that RefreshDatabase trait works correctly.
     */
    public function test_refresh_database_trait_works(): void
    {
        // This test verifies that the RefreshDatabase trait is working
        // by checking that we have a fresh database state
        
        // Insert some test data
        DB::table('migrations')->insert([
            'migration' => 'refresh_test_' . time(),
            'batch' => 777
        ]);
        
        $count = DB::table('migrations')->where('batch', 777)->count();
        $this->assertEquals(1, $count);
        
        // The RefreshDatabase trait should clean this up after the test
        // We can't test the cleanup in the same test, but we can verify
        // that the trait is being used
        $traits = class_uses_recursive(static::class);
        $this->assertContains(RefreshDatabase::class, $traits);
    }

    /**
     * Test that we can safely run migrations in tests.
     */
    public function test_can_run_migrations_safely(): void
    {
        // Verify that migrations table exists and has entries
        $this->assertTrue(Schema::hasTable('migrations'));
        
        $migrationCount = DB::table('migrations')->count();
        $this->assertGreaterThan(0, $migrationCount, 'Should have migration records');
    }

    /**
     * Test that external services are properly mocked.
     */
    public function test_external_services_are_mocked(): void
    {
        // Test mail configuration
        $this->assertEquals('array', config('mail.default'));
        
        // Test cache configuration
        $this->assertEquals('array', config('cache.default'));
        
        // Test queue configuration
        $this->assertEquals('sync', config('queue.default'));
        
        // Test that external API keys are set to test values
        $this->assertEquals('test', config('services.payu.pos_id'));
        $this->assertEquals('testing', config('services.payu.environment'));
    }

    /**
     * Test database isolation between tests.
     * This test should run independently and not be affected by other tests.
     */
    public function test_database_isolation_between_tests(): void
    {
        // Insert unique test data
        $uniqueId = 'isolation_test_' . uniqid();
        DB::table('migrations')->insert([
            'migration' => $uniqueId,
            'batch' => 666
        ]);
        
        // Verify our data exists
        $exists = DB::table('migrations')->where('migration', $uniqueId)->exists();
        $this->assertTrue($exists);
        
        // This data should not exist in other tests due to RefreshDatabase
    }
}
