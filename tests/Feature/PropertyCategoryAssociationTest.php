<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Property;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PropertyCategoryAssociationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_property_with_category_associations()
    {
        // Create test categories
        $category1 = Category::factory()->create(['name' => 'Electronics']);
        $category2 = Category::factory()->create(['name' => 'Clothing']);

        // Create property with category associations
        $propertyData = [
            'name' => 'Color',
            'type' => 'select',
            'is_multiple' => false,
            'categories' => [$category1->id, $category2->id],
            'options' => ['Red', 'Blue', 'Green']
        ];

        $response = $this->post(route('admin.properties.store'), $propertyData);

        // Assert property was created
        $this->assertDatabaseHas('properties', [
            'name' => 'Color',
            'type' => 'select',
            'is_multiple' => false
        ]);

        // Assert category associations were created
        $property = Property::where('name', 'Color')->first();
        $this->assertCount(2, $property->categories);
        $this->assertTrue($property->categories->contains($category1));
        $this->assertTrue($property->categories->contains($category2));

        // Assert options were created
        $this->assertCount(3, $property->options);
    }

    /** @test */
    public function it_can_update_property_category_associations()
    {
        // Create test categories
        $category1 = Category::factory()->create(['name' => 'Electronics']);
        $category2 = Category::factory()->create(['name' => 'Clothing']);
        $category3 = Category::factory()->create(['name' => 'Books']);

        // Create property with initial category association
        $property = Property::factory()->create(['name' => 'Size']);
        $property->categories()->attach([$category1->id]);

        // Update property with different categories
        $updateData = [
            'name' => 'Size Updated',
            'type' => 'text',
            'is_multiple' => true,
            'categories' => [$category2->id, $category3->id]
        ];

        $response = $this->put(route('admin.properties.update', $property), $updateData);

        // Refresh the property
        $property->refresh();

        // Assert property was updated
        $this->assertEquals('Size Updated', $property->name);
        $this->assertTrue($property->is_multiple);

        // Assert category associations were updated
        $this->assertCount(2, $property->categories);
        $this->assertFalse($property->categories->contains($category1));
        $this->assertTrue($property->categories->contains($category2));
        $this->assertTrue($property->categories->contains($category3));
    }

    /** @test */
    public function it_can_remove_all_category_associations()
    {
        // Create test category
        $category = Category::factory()->create(['name' => 'Electronics']);

        // Create property with category association
        $property = Property::factory()->create(['name' => 'Brand']);
        $property->categories()->attach([$category->id]);

        // Update property without categories
        $updateData = [
            'name' => 'Brand',
            'type' => 'text',
            'is_multiple' => false
            // No 'categories' key - should remove all associations
        ];

        $response = $this->put(route('admin.properties.update', $property), $updateData);

        // Refresh the property
        $property->refresh();

        // Assert all category associations were removed
        $this->assertCount(0, $property->categories);
    }

    /** @test */
    public function it_validates_category_ids_exist()
    {
        $propertyData = [
            'name' => 'Invalid Property',
            'type' => 'select',
            'is_multiple' => false,
            'categories' => [999, 1000], // Non-existent category IDs
            'options' => ['Option 1']
        ];

        $response = $this->post(route('admin.properties.store'), $propertyData);

        // Should fail validation
        $response->assertSessionHasErrors('categories.0');
        $response->assertSessionHasErrors('categories.1');
    }

    /** @test */
    public function property_appears_only_for_associated_categories_in_product_edit()
    {
        // Create categories
        $electronicsCategory = Category::factory()->create(['name' => 'Electronics']);
        $clothingCategory = Category::factory()->create(['name' => 'Clothing']);

        // Create properties
        $colorProperty = Property::factory()->create(['name' => 'Color']);
        $sizeProperty = Property::factory()->create(['name' => 'Size']);

        // Associate color with electronics, size with clothing
        $colorProperty->categories()->attach([$electronicsCategory->id]);
        $sizeProperty->categories()->attach([$clothingCategory->id]);

        // Create products in different categories
        $electronicsProduct = \App\Models\Product::factory()->create([
            'category_id' => $electronicsCategory->id
        ]);
        $clothingProduct = \App\Models\Product::factory()->create([
            'category_id' => $clothingCategory->id
        ]);

        // Test electronics product edit page
        $response = $this->get(route('admin.products.edit', $electronicsProduct));
        $response->assertStatus(200);
        
        // Should contain color property but not size property
        $properties = $response->viewData('properties');
        $this->assertTrue($properties->contains('name', 'Color'));
        $this->assertFalse($properties->contains('name', 'Size'));

        // Test clothing product edit page
        $response = $this->get(route('admin.products.edit', $clothingProduct));
        $response->assertStatus(200);
        
        // Should contain size property but not color property
        $properties = $response->viewData('properties');
        $this->assertTrue($properties->contains('name', 'Size'));
        $this->assertFalse($properties->contains('name', 'Color'));
    }
}
