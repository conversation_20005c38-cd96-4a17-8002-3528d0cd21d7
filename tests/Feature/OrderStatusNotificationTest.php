<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\User;
use App\Models\DeliveryAddress;
use App\Models\DeliveryMethod;
use App\Notifications\OrderStatusChanged;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class OrderStatusNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $order;
    protected $deliveryMethod;
    protected $deliveryAddress;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>'
        ]);

        // Create delivery method
        $this->deliveryMethod = DeliveryMethod::factory()->create([
            'name' => 'Test Delivery',
            'type' => 'courier',
            'api_provider' => 'inpost'
        ]);

        // Create delivery address
        $this->deliveryAddress = DeliveryAddress::factory()->create([
            'user_id' => $this->user->id,
            'delivery_method_id' => $this->deliveryMethod->id,
            'type' => 'address'
        ]);

        // Create test order
        $this->order = Order::factory()->create([
            'user_id' => $this->user->id,
            'delivery_address_id' => $this->deliveryAddress->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'status' => 'pending',
            'payment_status' => 'pending'
        ]);
    }

    /** @test */
    public function it_sends_notification_when_order_status_changes()
    {
        Notification::fake();

        // Update order status
        $this->order->update(['status' => 'processing']);

        // Assert notification was sent
        Notification::assertSentTo(
            $this->user,
            OrderStatusChanged::class,
            function ($notification) {
                return $notification->status === 'processing';
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_payment_status_changes()
    {
        Notification::fake();

        // Update payment status
        $this->order->update(['payment_status' => 'completed']);

        // Assert notification was sent
        Notification::assertSentTo(
            $this->user,
            OrderStatusChanged::class,
            function ($notification) {
                return $notification->status === 'completed';
            }
        );
    }

    /** @test */
    public function it_sends_shipped_notification_when_tracking_number_is_added()
    {
        Notification::fake();

        // Add tracking number
        $this->order->update(['inpost_tracking_number' => 'INP123456789']);

        // Assert shipped notification was sent
        Notification::assertSentTo(
            $this->user,
            OrderStatusChanged::class,
            function ($notification) {
                return $notification->status === 'shipped';
            }
        );
    }

    /** @test */
    public function it_does_not_send_notification_for_pending_status()
    {
        Notification::fake();

        // Create new order with pending status (should not trigger notification)
        $newOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'delivery_address_id' => $this->deliveryAddress->id,
            'status' => 'pending'
        ]);

        // Update to pending again (should not trigger notification)
        $newOrder->update(['status' => 'pending']);

        // Assert no notification was sent
        Notification::assertNotSentTo($this->user, OrderStatusChanged::class);
    }

    /** @test */
    public function it_sends_notification_to_guest_order_email()
    {
        Notification::fake();

        // Create guest order (no user_id)
        $guestOrder = Order::factory()->create([
            'user_id' => null,
            'delivery_address_id' => $this->deliveryAddress->id,
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'status' => 'pending'
        ]);

        // Update status
        $guestOrder->update(['status' => 'processing']);

        // Assert notification was sent to the anonymous notifiable
        Notification::assertSent(OrderStatusChanged::class);
    }

    /** @test */
    public function notification_contains_correct_order_data()
    {
        $notification = new OrderStatusChanged($this->order, 'shipped');

        $this->assertEquals('shipped', $notification->status);
        $this->assertEquals($this->order->id, $notification->order->id);
        $this->assertTrue($notification->shouldNotify());
    }

    /** @test */
    public function notification_array_contains_expected_data()
    {
        $notification = new OrderStatusChanged($this->order, 'delivered');
        $array = $notification->toArray($this->user);

        $this->assertEquals($this->order->id, $array['order_id']);
        $this->assertEquals($this->order->uuid, $array['order_uuid']);
        $this->assertEquals('delivered', $array['status']);
        $this->assertEquals('Dostarczone', $array['status_name']);
        $this->assertEquals($this->order->email, $array['customer_email']);
        $this->assertEquals('John Doe', $array['customer_name']);
    }

    /** @test */
    public function it_handles_all_status_types_correctly()
    {
        $notification = new OrderStatusChanged($this->order, 'processing');
        $this->assertEquals('W trakcie realizacji', $notification->getStatusName());

        $notification = new OrderStatusChanged($this->order, 'shipped');
        $this->assertEquals('Wysłane', $notification->getStatusName());

        $notification = new OrderStatusChanged($this->order, 'delivered');
        $this->assertEquals('Dostarczone', $notification->getStatusName());

        $notification = new OrderStatusChanged($this->order, 'cancelled');
        $this->assertEquals('Anulowane', $notification->getStatusName());

        $notification = new OrderStatusChanged($this->order, 'completed');
        $this->assertEquals('Opłacone', $notification->getStatusName());
    }

    /** @test */
    public function it_does_not_send_duplicate_notifications_for_same_status()
    {
        Notification::fake();

        // Update to processing
        $this->order->update(['status' => 'processing']);

        // Update to processing again (should not send another notification)
        $this->order->update(['status' => 'processing']);

        // Assert only one notification was sent
        Notification::assertSentTimes(OrderStatusChanged::class, 1);
    }

    /** @test */
    public function it_works_with_postmark_configuration()
    {
        // Temporarily set mail driver to postmark for this test
        config(['mail.default' => 'postmark']);
        config(['mail.mailers.postmark.token' => 'test-token']);

        Notification::fake();

        // Update order status
        $this->order->update(['status' => 'shipped']);

        // Assert notification was sent
        Notification::assertSentTo(
            $this->user,
            OrderStatusChanged::class,
            function ($notification) {
                return $notification->status === 'shipped';
            }
        );

        // Verify mail configuration
        $this->assertEquals('postmark', config('mail.default'));
    }

    /** @test */
    public function notification_mail_message_has_correct_structure()
    {
        $notification = new OrderStatusChanged($this->order, 'shipped');
        $mailMessage = $notification->toMail($this->user);

        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $mailMessage);
        $this->assertStringContains('Zamówienie #' . $this->order->id, $mailMessage->subject);
        $this->assertEquals('emails.orders.status-notification', $mailMessage->markdown);
    }
}
