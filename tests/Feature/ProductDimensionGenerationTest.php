<?php

namespace Tests\Feature;

use App\Jobs\GenerateProductDimensions;
use App\Models\Category;
use App\Models\Product;
use App\Models\Producent;
use App\Services\ProductService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ProductDimensionGenerationTest extends TestCase
{
    use RefreshDatabase;

    protected $productService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productService = new ProductService();
    }

    /** @test */
    public function it_can_identify_products_needing_dimensions()
    {
        $category = Category::factory()->create();
        $producent = Producent::factory()->create();

        // Product with dimensions
        $productWithDimensions = Product::factory()->create([
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'length' => 25.0,
            'width' => 15.0,
            'height' => 10.0,
            'weight' => 0.5,
        ]);

        // Product without dimensions
        $productWithoutDimensions = Product::factory()->create([
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'length' => null,
            'width' => null,
            'height' => null,
            'weight' => null,
        ]);

        $products = collect([$productWithDimensions, $productWithoutDimensions]);

        // Mock OpenAI API response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => json_encode([
                                $productWithoutDimensions->id => [
                                    'length' => 20.0,
                                    'width' => 12.0,
                                    'height' => 8.0,
                                    'weight' => 0.3
                                ]
                            ])
                        ]
                    ]
                ]
            ], 200)
        ]);

        $stats = $this->productService->generateProductsDimensions($products);

        $this->assertEquals(2, $stats['total_requested']);
        $this->assertEquals(1, $stats['processed']);
        $this->assertEquals(1, $stats['skipped']); // Product with dimensions was skipped
        $this->assertEquals(1, $stats['successful']);
        $this->assertEquals(0, $stats['failed']);

        // Verify the product without dimensions was updated
        $productWithoutDimensions->refresh();
        $this->assertNotNull($productWithoutDimensions->length);
        $this->assertNotNull($productWithoutDimensions->width);
        $this->assertNotNull($productWithoutDimensions->height);
        $this->assertNotNull($productWithoutDimensions->weight);

        // Verify the product with dimensions was not changed
        $productWithDimensions->refresh();
        $this->assertEquals(25.0, $productWithDimensions->length);
        $this->assertEquals(15.0, $productWithDimensions->width);
        $this->assertEquals(10.0, $productWithDimensions->height);
        $this->assertEquals(0.5, $productWithDimensions->weight);
    }

    /** @test */
    public function it_handles_invalid_api_responses_gracefully()
    {
        $category = Category::factory()->create();
        $producent = Producent::factory()->create();

        $product = Product::factory()->create([
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'length' => null,
            'width' => null,
            'height' => null,
        ]);

        // Mock invalid API response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Invalid JSON response'
                        ]
                    ]
                ]
            ], 200)
        ]);

        $stats = $this->productService->generateProductsDimensions(collect([$product]));

        $this->assertEquals(1, $stats['total_requested']);
        $this->assertEquals(1, $stats['processed']);
        $this->assertEquals(0, $stats['successful']);
        $this->assertEquals(1, $stats['failed']);

        // Verify product dimensions were not updated
        $product->refresh();
        $this->assertNull($product->length);
        $this->assertNull($product->width);
        $this->assertNull($product->height);
    }

    /** @test */
    public function it_validates_dimension_bounds()
    {
        $category = Category::factory()->create();
        $producent = Producent::factory()->create();

        $product = Product::factory()->create([
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'length' => null,
            'width' => null,
            'height' => null,
        ]);

        // Mock API response with out-of-bounds dimensions
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => json_encode([
                                $product->id => [
                                    'length' => 300.0, // Too large
                                    'width' => 0.1,    // Too small
                                    'height' => 50.0,
                                    'weight' => 150.0  // Too heavy
                                ]
                            ])
                        ]
                    ]
                ]
            ], 200)
        ]);

        $stats = $this->productService->generateProductsDimensions(collect([$product]));

        $this->assertEquals(1, $stats['failed']);
        $this->assertEquals(0, $stats['successful']);

        // Verify product dimensions were not updated due to validation failure
        $product->refresh();
        $this->assertNull($product->length);
        $this->assertNull($product->width);
        $this->assertNull($product->height);
    }

    /** @test */
    public function generate_dimensions_job_can_be_dispatched()
    {
        Queue::fake();

        $job = new GenerateProductDimensions(false, 50);

        GenerateProductDimensions::dispatch(false, 50);

        Queue::assertPushed(GenerateProductDimensions::class, function ($job) {
            return $job->batchSize === 50 && $job->processAll === false;
        });
    }

    /** @test */
    public function job_processes_single_batch_correctly()
    {
        $category = Category::factory()->create();
        $producent = Producent::factory()->create();

        // Create products without dimensions
        $products = Product::factory()->count(3)->create([
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'length' => null,
            'width' => null,
            'height' => null,
        ]);

        // Mock successful API response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => json_encode([
                                $products[0]->id => ['length' => 20.0, 'width' => 15.0, 'height' => 10.0, 'weight' => 0.5],
                                $products[1]->id => ['length' => 25.0, 'width' => 18.0, 'height' => 12.0, 'weight' => 0.8],
                                $products[2]->id => ['length' => 30.0, 'width' => 20.0, 'height' => 15.0, 'weight' => 1.2],
                            ])
                        ]
                    ]
                ]
            ], 200)
        ]);

        $job = new GenerateProductDimensions(false, 50);
        $job->handle($this->productService);

        // Verify all products were updated
        foreach ($products as $product) {
            $product->refresh();
            $this->assertNotNull($product->length);
            $this->assertNotNull($product->width);
            $this->assertNotNull($product->height);
            $this->assertNotNull($product->weight);
        }
    }

    /** @test */
    public function job_is_idempotent()
    {
        $category = Category::factory()->create();
        $producent = Producent::factory()->create();

        // Create product with existing dimensions
        $product = Product::factory()->create([
            'category_id' => $category->id,
            'producent_id' => $producent->id,
            'length' => 25.0,
            'width' => 15.0,
            'height' => 10.0,
            'weight' => 0.5,
        ]);

        $originalDimensions = [
            'length' => $product->length,
            'width' => $product->width,
            'height' => $product->height,
            'weight' => $product->weight,
        ];

        // Run job - should not process products that already have dimensions
        $job = new GenerateProductDimensions(false, 50);
        $job->handle($this->productService);

        // Verify dimensions were not changed
        $product->refresh();
        $this->assertEquals($originalDimensions['length'], $product->length);
        $this->assertEquals($originalDimensions['width'], $product->width);
        $this->assertEquals($originalDimensions['height'], $product->height);
        $this->assertEquals($originalDimensions['weight'], $product->weight);
    }
}
