<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\Cart;
use App\Models\CartItem;
use App\Services\ParcelCalculationService;
use App\Services\DeliveryPricingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

class ParcelCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected ParcelCalculationService $parcelService;
    protected DeliveryPricingService $deliveryService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->parcelService = new ParcelCalculationService();
        $this->deliveryService = new DeliveryPricingService($this->parcelService);
    }

    /** @test */
    public function it_calculates_single_parcel_for_small_items()
    {
        // Create small products that should fit in one parcel
        $products = collect([
            $this->createProductWithDimensions('Small Item 1', 5, 5, 5, 0.2),
            $this->createProductWithDimensions('Small Item 2', 6, 6, 6, 0.3),
        ]);

        $cartItems = $this->createCartItems($products, [2, 1]);
        $result = $this->parcelService->calculateParcelsForOrder($cartItems);

        $this->assertEquals(1, $result['parcel_count']);
        $this->assertEquals(3, $result['products_packed']); // 2 + 1
        $this->assertGreaterThan(0, $result['total_weight']);
    }

    /** @test */
    public function it_calculates_multiple_parcels_for_large_items()
    {
        // Create large products that should require multiple parcels
        $products = collect([
            $this->createProductWithDimensions('Large Item 1', 35, 35, 35, 5),
            $this->createProductWithDimensions('Large Item 2', 30, 30, 30, 4),
        ]);

        $cartItems = $this->createCartItems($products, [1, 1]);
        $result = $this->parcelService->calculateParcelsForOrder($cartItems);

        $this->assertGreaterThan(1, $result['parcel_count']);
        $this->assertEquals(2, $result['products_packed']);
    }

    /** @test */
    public function it_uses_default_dimensions_for_products_without_dimensions()
    {
        // Create product without dimensions
        $product = Product::factory()->create([
            'name' => 'Product Without Dimensions',
            'price' => 50,
            'length' => null,
            'width' => null,
            'height' => null,
            'weight' => null,
        ]);

        $cartItems = collect([
            ['product' => $product, 'quantity' => 1]
        ]);

        $result = $this->parcelService->calculateParcelsForOrder($cartItems);

        $this->assertEquals(1, $result['parcel_count']);
        $this->assertEquals(1, $result['products_packed']);
        
        // Should use default dimensions (10x10x10cm, 0.5kg) + packaging overhead
        $expectedWeight = 0.5 + 0.1; // default weight + packaging overhead
        $this->assertEquals($expectedWeight, $result['total_weight']);
    }

    /** @test */
    public function it_calculates_correct_delivery_pricing_for_single_parcel()
    {
        $products = collect([
            $this->createProductWithDimensions('Small Item', 5, 5, 5, 0.2),
        ]);

        $cartItems = $this->createCartItems($products, [1]);
        
        // Test with order total below free shipping threshold
        $result = $this->deliveryService->calculateDeliveryCost($cartItems, 100, 14.99);
        
        $this->assertEquals(14.99, $result['delivery_cost']);
        $this->assertEquals(1, $result['parcel_count']);
        $this->assertFalse($result['is_free_delivery']);
        $this->assertEquals(14.99, $result['first_parcel_cost']);
        $this->assertEquals(0, $result['additional_parcels_cost']);

        // Test with order total above free shipping threshold
        $result = $this->deliveryService->calculateDeliveryCost($cartItems, 200, 14.99);
        
        $this->assertEquals(0, $result['delivery_cost']);
        $this->assertEquals(1, $result['parcel_count']);
        $this->assertTrue($result['is_free_delivery']);
        $this->assertEquals(0, $result['first_parcel_cost']);
        $this->assertEquals(0, $result['additional_parcels_cost']);
    }

    /** @test */
    public function it_calculates_correct_delivery_pricing_for_multiple_parcels()
    {
        // Create products that require 3 parcels
        $products = collect([
            $this->createProductWithDimensions('Large Item 1', 35, 35, 35, 5),
            $this->createProductWithDimensions('Large Item 2', 35, 35, 35, 5),
            $this->createProductWithDimensions('Large Item 3', 35, 35, 35, 5),
        ]);

        $cartItems = $this->createCartItems($products, [1, 1, 1]);
        
        // Test with order total above free shipping threshold
        $result = $this->deliveryService->calculateDeliveryCost($cartItems, 200, 14.99);
        
        $expectedCost = 0 + (2 * 14.99); // First parcel free + 2 additional parcels
        $this->assertEquals($expectedCost, $result['delivery_cost']);
        $this->assertEquals(3, $result['parcel_count']);
        $this->assertFalse($result['is_free_delivery']); // Not free due to multiple parcels
        $this->assertEquals(0, $result['first_parcel_cost']);
        $this->assertEquals(29.98, $result['additional_parcels_cost']);

        // Test with order total below free shipping threshold
        $result = $this->deliveryService->calculateDeliveryCost($cartItems, 100, 14.99);
        
        $expectedCost = 14.99 + (2 * 14.99); // First parcel + 2 additional parcels
        $this->assertEquals($expectedCost, $result['delivery_cost']);
        $this->assertEquals(14.99, $result['first_parcel_cost']);
        $this->assertEquals(29.98, $result['additional_parcels_cost']);
    }

    /** @test */
    public function it_handles_oversized_products_gracefully()
    {
        // Create an oversized product that exceeds parcel limits
        $product = $this->createProductWithDimensions('Oversized Item', 50, 50, 50, 30);
        $cartItems = $this->createCartItems(collect([$product]), [1]);

        $result = $this->parcelService->calculateParcelsForOrder($cartItems);

        // Should still create a parcel (marked as oversized)
        $this->assertEquals(1, $result['parcel_count']);
        $this->assertEquals(1, $result['products_packed']);
        $this->assertTrue($result['parcels'][0]['oversized'] ?? false);
    }

    /** @test */
    public function it_applies_packaging_overhead_correctly()
    {
        $product = $this->createProductWithDimensions('Test Item', 10, 10, 10, 1);
        $cartItems = $this->createCartItems(collect([$product]), [1]);

        $result = $this->parcelService->calculateParcelsForOrder($cartItems);

        // Check that packaging overhead is applied
        $expectedWeight = 1 + 0.1; // original weight + packaging overhead
        $this->assertEquals($expectedWeight, $result['total_weight']);
    }

    /** @test */
    public function it_provides_delivery_info_with_descriptions()
    {
        $products = collect([
            $this->createProductWithDimensions('Item 1', 10, 10, 10, 1),
            $this->createProductWithDimensions('Item 2', 10, 10, 10, 1),
        ]);

        $cartItems = $this->createCartItems($products, [1, 1]);
        $result = $this->deliveryService->getDeliveryInfo($cartItems, 200);

        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('cost', $result);
        $this->assertArrayHasKey('is_free', $result);
        $this->assertArrayHasKey('parcel_count', $result);
        $this->assertArrayHasKey('method', $result);
    }

    /** @test */
    public function it_validates_parcel_configuration()
    {
        // Test with normal products
        $products = collect([
            $this->createProductWithDimensions('Normal Item', 10, 10, 10, 1),
        ]);
        $cartItems = $this->createCartItems($products, [1]);

        $validation = $this->deliveryService->validateParcelConfiguration($cartItems);
        $this->assertTrue($validation['valid']);
        $this->assertEmpty($validation['issues']);

        // Test with oversized product
        $oversizedProduct = $this->createProductWithDimensions('Oversized Item', 50, 50, 50, 30);
        $cartItems = $this->createCartItems(collect([$oversizedProduct]), [1]);

        $validation = $this->deliveryService->validateParcelConfiguration($cartItems);
        $this->assertFalse($validation['valid']);
        $this->assertNotEmpty($validation['issues']);
        $this->assertEquals('oversized_product', $validation['issues'][0]['type']);
    }

    /**
     * Helper method to create a product with specific dimensions
     */
    protected function createProductWithDimensions(string $name, float $length, float $width, float $height, float $weight): Product
    {
        return Product::factory()->create([
            'name' => $name,
            'price' => 50,
            'length' => $length,
            'width' => $width,
            'height' => $height,
            'weight' => $weight,
        ]);
    }

    /**
     * Helper method to create cart items collection
     */
    protected function createCartItems(Collection $products, array $quantities): Collection
    {
        $cartItems = collect();
        
        foreach ($products as $index => $product) {
            $quantity = $quantities[$index] ?? 1;
            $cartItems->push([
                'product' => $product,
                'quantity' => $quantity
            ]);
        }

        return $cartItems;
    }
}
