<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Category;
use App\Models\Product;
use App\Models\Property;
use App\Models\PropertyOption;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PropertyFiltersTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_displays_property_filters_on_category_page()
    {
        // Create category
        $category = Category::factory()->create([
            'name' => 'Electronics',
            'path' => 'electronics',
            'is_visible' => true
        ]);

        // Create property associated with category
        $colorProperty = Property::factory()->create([
            'name' => 'Color',
            'type' => 'select',
            'is_multiple' => false
        ]);
        $colorProperty->categories()->attach($category->id);

        // Create property options
        $redOption = PropertyOption::create(['property_id' => $colorProperty->id, 'value' => 'Red']);
        $blueOption = PropertyOption::create(['property_id' => $colorProperty->id, 'value' => 'Blue']);

        // Create products with properties
        $product1 = Product::factory()->create(['category_id' => $category->id, 'stock' => 10]);
        $product2 = Product::factory()->create(['category_id' => $category->id, 'stock' => 5]);

        // Associate products with property options
        $product1->properties()->attach($colorProperty->id, ['property_option_id' => $redOption->id]);
        $product2->properties()->attach($colorProperty->id, ['property_option_id' => $blueOption->id]);

        // Visit category page
        $response = $this->get('/category/electronics');

        $response->assertStatus(200);
        
        // Check that property filters are present in the view data
        $filters = $response->viewData('filter');
        $propertyFilter = collect($filters)->firstWhere('code', 'property_' . $colorProperty->id);
        
        $this->assertNotNull($propertyFilter);
        $this->assertEquals('Color', $propertyFilter['name']);
        $this->assertEquals('checkbox', $propertyFilter['type']);
        $this->assertCount(2, $propertyFilter['variables']);
    }

    /** @test */
    public function it_filters_products_by_property_options()
    {
        // Create category
        $category = Category::factory()->create([
            'name' => 'Electronics',
            'path' => 'electronics',
            'is_visible' => true
        ]);

        // Create property
        $colorProperty = Property::factory()->create([
            'name' => 'Color',
            'type' => 'select'
        ]);
        $colorProperty->categories()->attach($category->id);

        // Create property options
        $redOption = PropertyOption::create(['property_id' => $colorProperty->id, 'value' => 'Red']);
        $blueOption = PropertyOption::create(['property_id' => $colorProperty->id, 'value' => 'Blue']);

        // Create products
        $redProduct = Product::factory()->create(['category_id' => $category->id, 'stock' => 10]);
        $blueProduct = Product::factory()->create(['category_id' => $category->id, 'stock' => 5]);

        // Associate products with properties
        $redProduct->properties()->attach($colorProperty->id, ['property_option_id' => $redOption->id]);
        $blueProduct->properties()->attach($colorProperty->id, ['property_option_id' => $blueOption->id]);

        // Filter by red color
        $response = $this->get('/category/electronics?filter[property_' . $colorProperty->id . '][]=' . $redOption->id);

        $response->assertStatus(200);
        
        // Check that only red product is returned
        $products = $response->viewData('products');
        $this->assertCount(1, $products);
        $this->assertEquals($redProduct->id, $products->first()->id);
    }

    /** @test */
    public function it_displays_text_property_filters()
    {
        // Create category
        $category = Category::factory()->create([
            'name' => 'Books',
            'path' => 'books',
            'is_visible' => true
        ]);

        // Create text property
        $authorProperty = Property::factory()->create([
            'name' => 'Author',
            'type' => 'text'
        ]);
        $authorProperty->categories()->attach($category->id);

        // Create product with text property
        $product = Product::factory()->create(['category_id' => $category->id, 'stock' => 10]);
        $product->properties()->attach($authorProperty->id, ['value' => 'John Doe']);

        // Visit category page
        $response = $this->get('/category/books');

        $response->assertStatus(200);
        
        // Check that text property filter is present
        $filters = $response->viewData('filter');
        $propertyFilter = collect($filters)->firstWhere('code', 'property_' . $authorProperty->id);
        
        $this->assertNotNull($propertyFilter);
        $this->assertEquals('Author', $propertyFilter['name']);
        $this->assertEquals('text', $propertyFilter['type']);
    }

    /** @test */
    public function it_displays_numeric_range_filters_for_numeric_text_properties()
    {
        // Create category
        $category = Category::factory()->create([
            'name' => 'Products',
            'path' => 'products',
            'is_visible' => true
        ]);

        // Create numeric text property
        $weightProperty = Property::factory()->create([
            'name' => 'Weight',
            'type' => 'text'
        ]);
        $weightProperty->categories()->attach($category->id);

        // Create products with numeric values
        $product1 = Product::factory()->create(['category_id' => $category->id, 'stock' => 10]);
        $product2 = Product::factory()->create(['category_id' => $category->id, 'stock' => 5]);

        $product1->properties()->attach($weightProperty->id, ['value' => '10.5']);
        $product2->properties()->attach($weightProperty->id, ['value' => '25.0']);

        // Visit category page
        $response = $this->get('/category/products');

        $response->assertStatus(200);
        
        // Check that range filter is present
        $filters = $response->viewData('filter');
        $propertyFilter = collect($filters)->firstWhere('code', 'property_' . $weightProperty->id);
        
        $this->assertNotNull($propertyFilter);
        $this->assertEquals('Weight', $propertyFilter['name']);
        $this->assertEquals('range', $propertyFilter['type']);
        $this->assertEquals(10.5, $propertyFilter['min']);
        $this->assertEquals(25.0, $propertyFilter['max']);
    }

    /** @test */
    public function it_only_shows_properties_for_relevant_categories()
    {
        // Create categories
        $electronicsCategory = Category::factory()->create([
            'name' => 'Electronics',
            'path' => 'electronics',
            'is_visible' => true
        ]);
        
        $clothingCategory = Category::factory()->create([
            'name' => 'Clothing',
            'path' => 'clothing',
            'is_visible' => true
        ]);

        // Create properties
        $colorProperty = Property::factory()->create(['name' => 'Color', 'type' => 'select']);
        $sizeProperty = Property::factory()->create(['name' => 'Size', 'type' => 'select']);

        // Associate properties with specific categories
        $colorProperty->categories()->attach($electronicsCategory->id);
        $sizeProperty->categories()->attach($clothingCategory->id);

        // Create property options
        PropertyOption::create(['property_id' => $colorProperty->id, 'value' => 'Red']);
        PropertyOption::create(['property_id' => $sizeProperty->id, 'value' => 'Large']);

        // Create products
        $electronicsProduct = Product::factory()->create(['category_id' => $electronicsCategory->id, 'stock' => 10]);
        $clothingProduct = Product::factory()->create(['category_id' => $clothingCategory->id, 'stock' => 5]);

        // Associate products with properties
        $electronicsProduct->properties()->attach($colorProperty->id, ['property_option_id' => 1]);
        $clothingProduct->properties()->attach($sizeProperty->id, ['property_option_id' => 2]);

        // Visit electronics category
        $response = $this->get('/category/electronics');
        $filters = $response->viewData('filter');
        
        // Should have color filter but not size filter
        $this->assertTrue(collect($filters)->contains('code', 'property_' . $colorProperty->id));
        $this->assertFalse(collect($filters)->contains('code', 'property_' . $sizeProperty->id));

        // Visit clothing category
        $response = $this->get('/category/clothing');
        $filters = $response->viewData('filter');
        
        // Should have size filter but not color filter
        $this->assertTrue(collect($filters)->contains('code', 'property_' . $sizeProperty->id));
        $this->assertFalse(collect($filters)->contains('code', 'property_' . $colorProperty->id));
    }
}
