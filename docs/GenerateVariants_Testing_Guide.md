# GenerateVariants Testing Guide

## Overview
This guide provides step-by-step instructions for testing the GenerateVariants job functionality.

## ✅ Prerequisites

### 1. Queue Configuration
Ensure your Laravel application has queue workers configured:

```bash
# Check queue configuration
php artisan queue:work --help

# Start a queue worker (for testing)
php artisan queue:work --timeout=3600
```

### 2. Database Setup
Ensure migrations are run:

```bash
# Run all migrations including ProductVariant tables
php artisan migrate

# Check tables exist
php artisan tinker
>>> \Schema::hasTable('product_variants')
>>> \Schema::hasColumn('products', 'product_variant_id')
```

### 3. Sample Data
Ensure you have products in the database that can be used for variant detection.

## ✅ Testing Steps

### Step 1: Access the Generate Variants Interface

1. **Navigate to Admin Dashboard**
   - Go to `/admin/dashboard`
   - Look for the purple "Generate Variants" button

2. **Or Use Direct Navigation**
   - Go to `/admin/product-variants`
   - Click "Generate Variants" in the sidebar menu
   - Or directly visit `/admin/product-variants-generate`

### Step 2: Review Current Statistics

The interface should display:
- **Total Products**: All products in your database
- **Unorganized Products**: Products without `product_variant_id`
- **Already Organized**: Products already in variant groups
- **Existing Variant Groups**: Current ProductVariant count

### Step 3: Perform Dry Run Test

1. **Select Dry Run Mode**
   - Ensure "Dry Run" radio button is selected
   - Choose batch size (50 is recommended for testing)

2. **Start Dry Run**
   - Click "Start Generation"
   - Confirm in the dialog that appears
   - You should see a success message

3. **Check Logs**
   ```bash
   # Monitor Laravel logs
   tail -f storage/logs/laravel.log
   
   # Look for entries like:
   # [timestamp] local.INFO: GenerateVariants job started
   # [timestamp] local.INFO: Found variant group
   # [timestamp] local.INFO: GenerateVariants job completed successfully
   ```

### Step 4: Verify Dry Run Results

1. **Check Database (Should be Unchanged)**
   ```sql
   -- Count should be same as before
   SELECT COUNT(*) FROM product_variants;
   SELECT COUNT(*) FROM products WHERE product_variant_id IS NOT NULL;
   ```

2. **Review Log Output**
   - Look for "Found variant group" entries
   - Check statistics in completion log
   - Verify no actual database changes were made

### Step 5: Perform Live Run Test

1. **Select Live Run Mode**
   - Choose "Live Run" radio button
   - Keep same batch size

2. **Start Live Run**
   - Click "Start Generation"
   - Confirm the more serious warning dialog
   - Monitor for success message

3. **Check Progress**
   ```bash
   # Monitor logs for progress
   tail -f storage/logs/laravel.log | grep GenerateVariants
   ```

### Step 6: Verify Live Run Results

1. **Check Database Changes**
   ```sql
   -- Should show new variant groups
   SELECT COUNT(*) FROM product_variants;
   
   -- Should show products assigned to variants
   SELECT COUNT(*) FROM products WHERE product_variant_id IS NOT NULL;
   
   -- View created variants
   SELECT id, name, show_name, created_at FROM product_variants ORDER BY created_at DESC LIMIT 10;
   
   -- View products with variant names
   SELECT id, name, variant_name, product_variant_id FROM products 
   WHERE product_variant_id IS NOT NULL LIMIT 10;
   ```

2. **Test Frontend Display**
   - Visit product detail pages for products that were grouped
   - Verify variants display correctly
   - Check that variant names appear properly

## ✅ Manual Testing Commands

### Using Artisan Tinker

```php
// Start tinker
php artisan tinker

// Dispatch job manually
use App\Jobs\GenerateVariants;

// Dry run
GenerateVariants::dispatch(true, 25);

// Live run
GenerateVariants::dispatch(false, 25);

// Check job status (if using database queue)
use Illuminate\Support\Facades\DB;
DB::table('jobs')->count(); // Should be 0 when job completes
DB::table('failed_jobs')->count(); // Should be 0 if no failures
```

### Direct Job Execution (Synchronous)

```php
// In tinker - run job immediately without queue
use App\Jobs\GenerateVariants;
use App\Services\ProductService;

$job = new GenerateVariants(true, 10); // Dry run, small batch
$job->handle(app(ProductService::class));
```

## ✅ Expected Results

### Dry Run Results
- **No database changes**: ProductVariant and Product tables unchanged
- **Detailed logs**: Shows what would be created
- **Statistics**: Reports potential variants and products

### Live Run Results
- **New ProductVariant records**: Created based on similar products
- **Updated Product records**: `product_variant_id` and `variant_name` set
- **Preserved existing variants**: No changes to already organized products
- **Frontend compatibility**: Variant display works on product pages

## ✅ Common Issues and Solutions

### Issue: Job Not Processing
**Symptoms**: Job dispatched but no logs appear
**Solutions**:
- Check queue worker is running: `php artisan queue:work`
- Verify queue configuration in `.env`
- Check for failed jobs: `php artisan queue:failed`

### Issue: Memory Exhaustion
**Symptoms**: Job fails with memory limit errors
**Solutions**:
- Reduce batch size (try 25 or 10)
- Increase PHP memory limit
- Check for memory leaks in ProductService

### Issue: No Variants Created
**Symptoms**: Job completes but no variants found
**Solutions**:
- Verify ProductService::findSimilarProducts() works
- Check product data quality (names, categories)
- Review ProductService variant detection logic

### Issue: Database Errors
**Symptoms**: Constraint violation or database errors
**Solutions**:
- Check foreign key constraints
- Verify migrations ran correctly
- Check for duplicate SKUs or other conflicts

## ✅ Performance Testing

### Small Dataset Test (< 100 products)
```bash
# Expected completion time: < 1 minute
# Batch size: 50-100
# Memory usage: Low
```

### Medium Dataset Test (100-1000 products)
```bash
# Expected completion time: 1-10 minutes
# Batch size: 25-50
# Memory usage: Medium
```

### Large Dataset Test (> 1000 products)
```bash
# Expected completion time: 10+ minutes
# Batch size: 10-25
# Memory usage: High
# Consider running during off-peak hours
```

## ✅ Rollback Testing

### Manual Rollback (if needed)
```sql
-- Remove all variant assignments (CAUTION: This removes all variant data)
UPDATE products SET product_variant_id = NULL, variant_name = NULL 
WHERE product_variant_id IS NOT NULL;

-- Delete all ProductVariant records
DELETE FROM product_variants;

-- Reset auto-increment
ALTER TABLE product_variants AUTO_INCREMENT = 1;
```

### Selective Rollback
```sql
-- Remove variants created after specific time
DELETE FROM product_variants WHERE created_at > '2025-06-30 12:00:00';

-- Remove product assignments for deleted variants
UPDATE products SET product_variant_id = NULL, variant_name = NULL 
WHERE product_variant_id NOT IN (SELECT id FROM product_variants);
```

## ✅ Success Criteria

### ✅ Dry Run Success
- [ ] Job dispatches without errors
- [ ] Comprehensive logs generated
- [ ] No database changes made
- [ ] Statistics show potential variants
- [ ] Job completes successfully

### ✅ Live Run Success
- [ ] ProductVariant records created
- [ ] Products assigned to variants
- [ ] Variant names set appropriately
- [ ] No errors in processing
- [ ] Frontend display works correctly
- [ ] Existing variants preserved
- [ ] Job statistics accurate

### ✅ Performance Success
- [ ] Completes within reasonable time
- [ ] Memory usage acceptable
- [ ] No server performance issues
- [ ] Queue processing stable
- [ ] Logs manageable size

### ✅ Quality Success
- [ ] Created variants make logical sense
- [ ] Variant names are meaningful
- [ ] No duplicate or conflicting groups
- [ ] Product relationships accurate
- [ ] Frontend display intuitive

## ✅ Post-Testing Cleanup

### Log Management
```bash
# Archive or clean logs if they become too large
# Consider log rotation for production use
```

### Database Optimization
```sql
-- Analyze tables after large changes
ANALYZE TABLE products, product_variants;

-- Consider adding indexes if needed for performance
```

### Queue Cleanup
```bash
# Clear completed jobs if using database queue
php artisan queue:clear

# Restart queue workers if needed
php artisan queue:restart
```

This testing guide ensures the GenerateVariants job works correctly and safely in your environment.
