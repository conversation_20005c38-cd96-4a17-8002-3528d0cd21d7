<?php

/**
 * Test script to verify GenerateVariants collection type fix
 * Run this in <PERSON><PERSON> Tin<PERSON> to test the fix
 */

// Test the collection type handling logic
use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

// Simulate the fixed logic from GenerateVariants job
function testCollectionTypeHandling()
{
    echo "Testing GenerateVariants collection type handling...\n";
    
    // Get a sample product (adjust ID as needed)
    $product = Product::first();
    if (!$product) {
        echo "No products found in database. Please add some products first.\n";
        return;
    }
    
    echo "Testing with product: {$product->name}\n";
    
    // Test ProductService::findSimilarProducts (should return EloquentCollection)
    $productService = app(ProductService::class);
    $similarProducts = $productService->findSimilarProducts($product);
    
    echo "Similar products found: " . $similarProducts->count() . "\n";
    echo "Similar products type: " . get_class($similarProducts) . "\n";
    
    if ($similarProducts->isEmpty()) {
        echo "No similar products found. Test completed - no variant group would be created.\n";
        return;
    }
    
    // Test the fixed collection creation logic
    $allProducts = new EloquentCollection([$product]);
    $allProducts = $allProducts->merge($similarProducts);
    
    echo "All products count: " . $allProducts->count() . "\n";
    echo "All products type: " . get_class($allProducts) . "\n";
    
    // Test the fixed slice and conversion logic
    $slicedProducts = new EloquentCollection($allProducts->slice(1)->values()->all());
    
    echo "Sliced products count: " . $slicedProducts->count() . "\n";
    echo "Sliced products type: " . get_class($slicedProducts) . "\n";
    
    // Test the ProductService::getVariantDifferences method (this should not throw type error)
    try {
        $variantDifferences = $productService->getVariantDifferences($product, $slicedProducts);
        echo "✅ getVariantDifferences succeeded!\n";
        echo "Variant differences count: " . count($variantDifferences) . "\n";
        
        foreach ($variantDifferences as $index => $variant) {
            echo "  Variant {$index}: {$variant['product']->name} -> {$variant['difference']}\n";
        }
        
    } catch (TypeError $e) {
        echo "❌ Type error still occurs: " . $e->getMessage() . "\n";
        return false;
    } catch (Exception $e) {
        echo "❌ Other error: " . $e->getMessage() . "\n";
        return false;
    }
    
    echo "✅ All collection type handling tests passed!\n";
    return true;
}

// Test the actual job execution (dry run)
function testJobExecution()
{
    echo "\nTesting GenerateVariants job execution (dry run)...\n";
    
    try {
        $job = new \App\Jobs\GenerateVariants(true, 5); // Dry run, small batch
        $job->handle(app(\App\Services\ProductService::class));
        
        echo "✅ Job executed successfully!\n";
        return true;
        
    } catch (Exception $e) {
        echo "❌ Job execution failed: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        return false;
    }
}

// Run the tests
echo "=== GenerateVariants Collection Type Fix Verification ===\n\n";

$collectionTest = testCollectionTypeHandling();
$jobTest = testJobExecution();

echo "\n=== Test Results ===\n";
echo "Collection Type Handling: " . ($collectionTest ? "✅ PASSED" : "❌ FAILED") . "\n";
echo "Job Execution: " . ($jobTest ? "✅ PASSED" : "❌ FAILED") . "\n";

if ($collectionTest && $jobTest) {
    echo "\n🎉 All tests passed! The GenerateVariants fix is working correctly.\n";
} else {
    echo "\n⚠️  Some tests failed. Please review the errors above.\n";
}

/**
 * Usage Instructions:
 * 
 * 1. Open Laravel Tinker:
 *    php artisan tinker
 * 
 * 2. Copy and paste this entire script into Tinker
 * 
 * 3. The tests will run automatically and show results
 * 
 * 4. Look for "✅ All tests passed!" message
 * 
 * Alternative: Save this as a separate file and include it:
 * include 'path/to/this/file.php';
 */
