<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Property;
use Illuminate\Database\Eloquent\Collection;

/**
 * PROPOSAL: Enhanced ProductService with proper variant support
 * This is a conceptual implementation showing the recommended approach
 */
class ProductVariantService
{
    /**
     * Get all variants for a parent product
     */
    public function getProductVariants(Product $product): Collection
    {
        if ($product->is_parent) {
            return $product->variants()->with(['attributes.property', 'attributes.propertyOption'])->get();
        }
        
        if ($product->parent_id) {
            return $product->parent->variants()->with(['attributes.property', 'attributes.propertyOption'])->get();
        }
        
        return collect();
    }

    /**
     * Get variant-defining properties for a product category
     */
    public function getVariantProperties(Product $product): Collection
    {
        return Property::whereHas('categories', function($query) use ($product) {
            $query->where('categories.id', $product->category_id);
        })
        ->where('is_variant_defining', true)
        ->with('options')
        ->orderBy('sort_order')
        ->get();
    }

    /**
     * Create a new variant for a parent product
     */
    public function createVariant(Product $parentProduct, array $variantData, array $attributes = []): ProductVariant
    {
        $variant = $parentProduct->variants()->create([
            'sku' => $variantData['sku'],
            'name' => $variantData['name'] ?? $this->generateVariantName($parentProduct, $attributes),
            'price_modifier' => $variantData['price_modifier'] ?? 0,
            'stock' => $variantData['stock'] ?? 0,
            'weight' => $variantData['weight'] ?? $parentProduct->weight,
            'ean' => $variantData['ean'] ?? null,
            'is_default' => $variantData['is_default'] ?? false,
        ]);

        // Set variant attributes
        foreach ($attributes as $propertyId => $value) {
            $this->setVariantAttribute($variant, $propertyId, $value);
        }

        return $variant;
    }

    /**
     * Generate variant name based on parent product and attributes
     */
    protected function generateVariantName(Product $parentProduct, array $attributes): string
    {
        $baseName = $parentProduct->name;
        $attributeStrings = [];

        foreach ($attributes as $propertyId => $value) {
            $property = Property::find($propertyId);
            if ($property && $property->is_variant_defining) {
                if (is_array($value) && isset($value['option_id'])) {
                    $option = $property->options()->find($value['option_id']);
                    $attributeStrings[] = $option ? $option->value : $value['value'];
                } else {
                    $attributeStrings[] = $value;
                }
            }
        }

        return $baseName . (!empty($attributeStrings) ? ' - ' . implode(', ', $attributeStrings) : '');
    }

    /**
     * Set a variant attribute
     */
    public function setVariantAttribute(ProductVariant $variant, int $propertyId, $value): void
    {
        $variant->attributes()->updateOrCreate(
            ['property_id' => $propertyId],
            [
                'property_option_id' => is_array($value) ? $value['option_id'] : null,
                'value' => is_array($value) ? $value['value'] : $value,
            ]
        );
    }

    /**
     * Get variant matrix for admin interface
     */
    public function getVariantMatrix(Product $product): array
    {
        $variants = $this->getProductVariants($product);
        $properties = $this->getVariantProperties($product);
        
        $matrix = [];
        
        foreach ($variants as $variant) {
            $row = [
                'variant' => $variant,
                'attributes' => [],
                'price' => $product->base_price + $variant->price_modifier,
                'stock' => $variant->stock,
            ];
            
            foreach ($properties as $property) {
                $attribute = $variant->attributes->where('property_id', $property->id)->first();
                $row['attributes'][$property->id] = [
                    'property' => $property,
                    'value' => $attribute ? ($attribute->propertyOption ? $attribute->propertyOption->value : $attribute->value) : null,
                    'option_id' => $attribute ? $attribute->property_option_id : null,
                ];
            }
            
            $matrix[] = $row;
        }
        
        return $matrix;
    }

    /**
     * Convert existing similar products to proper variants
     */
    public function convertSimilarProductsToVariants(Product $parentProduct): int
    {
        $similarProducts = $this->findSimilarProducts($parentProduct);
        $converted = 0;
        
        foreach ($similarProducts as $similarProduct) {
            // Extract variant attributes from product name/properties
            $attributes = $this->extractVariantAttributesFromProduct($similarProduct);
            
            // Create variant
            $variant = $this->createVariant($parentProduct, [
                'sku' => $similarProduct->sku,
                'name' => $similarProduct->name,
                'price_modifier' => $similarProduct->price - $parentProduct->price,
                'stock' => $similarProduct->stock,
                'weight' => $similarProduct->weight,
                'ean' => $similarProduct->ean,
            ], $attributes);
            
            // Transfer media
            $this->transferProductMedia($similarProduct, $variant);
            
            // Mark original product as converted/inactive
            $similarProduct->update(['status' => 'converted', 'parent_id' => $parentProduct->id]);
            
            $converted++;
        }
        
        return $converted;
    }

    /**
     * Extract variant attributes from existing product
     */
    protected function extractVariantAttributesFromProduct(Product $product): array
    {
        // Use existing ProductService logic to extract differences
        $productService = app(ProductService::class);
        $baseName = $productService->extractBaseName($product->name);
        $difference = $productService->extractVariantDifference($product->name, $baseName);
        
        // Map differences to actual properties (this would need category-specific logic)
        return $this->mapDifferenceToAttributes($difference, $product);
    }

    /**
     * Map extracted differences to property attributes
     */
    protected function mapDifferenceToAttributes(string $difference, Product $product): array
    {
        $attributes = [];
        
        // Weight extraction
        if (preg_match('/(\d+[,.]?\d*)\s*(kg|g)/', $difference, $matches)) {
            $weightProperty = Property::where('name', 'Waga')->first();
            if ($weightProperty) {
                $attributes[$weightProperty->id] = $matches[0];
            }
        }
        
        // Color extraction
        $colors = ['czarny', 'biały', 'czerwony', 'niebieski', 'zielony', 'żółty'];
        foreach ($colors as $color) {
            if (stripos($difference, $color) !== false) {
                $colorProperty = Property::where('name', 'Kolor')->first();
                if ($colorProperty) {
                    $colorOption = $colorProperty->options()->where('value', $color)->first();
                    $attributes[$colorProperty->id] = [
                        'option_id' => $colorOption ? $colorOption->id : null,
                        'value' => $color
                    ];
                }
                break;
            }
        }
        
        return $attributes;
    }

    /**
     * Transfer media from product to variant
     */
    protected function transferProductMedia(Product $product, ProductVariant $variant): void
    {
        foreach ($product->getMedia() as $media) {
            $variant->addMediaFromUrl($media->getUrl())
                   ->toMediaCollection($media->collection_name);
        }
    }

    /**
     * Get available variant combinations for frontend
     */
    public function getAvailableVariantCombinations(Product $product): array
    {
        $variants = $this->getProductVariants($product);
        $properties = $this->getVariantProperties($product);
        
        $combinations = [];
        
        foreach ($variants as $variant) {
            if ($variant->status === 'active' && $variant->stock > 0) {
                $combination = [];
                foreach ($properties as $property) {
                    $attribute = $variant->attributes->where('property_id', $property->id)->first();
                    $combination[$property->id] = $attribute ? $attribute->property_option_id : null;
                }
                $combinations[] = [
                    'variant_id' => $variant->id,
                    'attributes' => $combination,
                    'price' => $product->base_price + $variant->price_modifier,
                    'stock' => $variant->stock,
                ];
            }
        }
        
        return $combinations;
    }
}
