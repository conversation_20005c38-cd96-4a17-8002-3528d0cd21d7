#!/bin/bash

# Laravel Testing Environment Setup Script
# This script sets up and verifies the testing environment

echo "🧪 Setting up Laravel Testing Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env.testing exists
if [ ! -f ".env.testing" ]; then
    print_error ".env.testing file not found!"
    echo "Please ensure .env.testing is created with proper test database configuration."
    exit 1
fi

print_success ".env.testing file found"

# Check if SQLite extension is available
print_status "Checking SQLite support..."
php -m | grep -i sqlite > /dev/null
if [ $? -eq 0 ]; then
    print_success "SQLite extension is available"
else
    print_error "SQLite extension is not available. Please install php-sqlite3"
    exit 1
fi

# Create database directory if it doesn't exist
if [ ! -d "database" ]; then
    mkdir -p database
    print_status "Created database directory"
fi

# Create testing.sqlite file (for file-based testing if needed)
if [ ! -f "database/testing.sqlite" ]; then
    touch database/testing.sqlite
    print_status "Created database/testing.sqlite file"
fi

# Run database migrations for testing
print_status "Running database migrations for testing environment..."
php artisan migrate --env=testing --force
if [ $? -eq 0 ]; then
    print_success "Database migrations completed successfully"
else
    print_error "Database migrations failed"
    exit 1
fi

# Run database seeders for testing (optional)
print_status "Running database seeders for testing environment..."
php artisan db:seed --env=testing --force
if [ $? -eq 0 ]; then
    print_success "Database seeders completed successfully"
else
    print_warning "Database seeders failed or no seeders found (this is optional)"
fi

# Run a quick test to verify the setup
print_status "Running verification tests..."
php artisan test tests/Feature/DatabaseIsolationTest.php --env=testing
if [ $? -eq 0 ]; then
    print_success "Verification tests passed!"
else
    print_warning "Verification tests failed - this might be expected if the test doesn't exist yet"
fi

echo ""
print_success "🎉 Testing environment setup completed!"
echo ""
echo "Next steps:"
echo "1. Run tests with: php artisan test"
echo "2. Run specific test suite: php artisan test --testsuite=Feature"
echo "3. Run tests with coverage: php artisan test --coverage"
echo "4. Run PHPUnit directly: vendor/bin/phpunit"
echo ""
echo "The testing environment is configured to:"
echo "- Use SQLite in-memory database (:memory:)"
echo "- Isolate tests from your production/development database"
echo "- Use array drivers for cache, session, and mail"
echo "- Disable external services during testing"
