# Invoice Filename Fix

## 🐛 Problem

When trying to download invoices, users encountered the error:
```
Failed to download invoice: The filename and the fallback cannot contain the "/" and "\" characters.
```

## 🔍 Root Cause

The issue was caused by the invoice number format containing forward slashes (`/`) which are not allowed in filenames:

- **Invoice Number Format**: `FV/2025/06/0001` (configured in `.env` as `INVOICE_NUMBER_FORMAT="{prefix}/{year}/{month}/{number}"`)
- **Generated Filename**: `faktura_FV/2025/06/0001.pdf` ❌ (contains invalid `/` characters)
- **<PERSON><PERSON>'s Response**: <PERSON><PERSON>'s `response()->download()` method validates filenames and rejects those containing `/` or `\` characters

## ✅ Solution

Updated the filename sanitization logic in two places to handle all problematic characters:

### 1. Invoice Model (`app/Models/Invoice.php`)

**Before:**
```php
public function getPdfFileNameAttribute(): string
{
    return "faktura_{$this->invoice_number}.pdf";
}
```

**After:**
```php
public function getPdfFileNameAttribute(): string
{
    // Sanitize invoice number for filename - replace problematic characters with underscores
    // Characters not allowed in filenames: / \ : * ? " < > |
    $cleanNumber = preg_replace('/[\/\\\\:*?"<>|]/', '_', $this->invoice_number);
    return "faktura_{$cleanNumber}.pdf";
}
```

### 2. InvoiceService (`app/Services/InvoiceService.php`)

**Before:**
```php
protected function generatePdfFilename(Invoice $invoice): string
{
    $cleanNumber = Str::slug($invoice->invoice_number, '_');
    return "faktura_{$cleanNumber}.pdf";
}
```

**After:**
```php
protected function generatePdfFilename(Invoice $invoice): string
{
    // Sanitize invoice number for filename
    // Replace problematic characters (/, \, :, *, ?, ", <, >, |) with underscores
    $cleanNumber = preg_replace('/[\/\\\\:*?"<>|]/', '_', $invoice->invoice_number);
    
    // Remove any remaining special characters and convert to lowercase
    $cleanNumber = Str::slug($cleanNumber, '_');
    
    return "faktura_{$cleanNumber}.pdf";
}
```

## 📋 Characters Sanitized

The fix handles all characters that are problematic in filenames:

| Character | Description | Replacement |
|-----------|-------------|-------------|
| `/` | Forward slash | `_` |
| `\` | Backslash | `_` |
| `:` | Colon | `_` |
| `*` | Asterisk | `_` |
| `?` | Question mark | `_` |
| `"` | Double quote | `_` |
| `<` | Less than | `_` |
| `>` | Greater than | `_` |
| `|` | Pipe | `_` |

## 🧪 Testing

Created comprehensive tests to verify the fix:

### Unit Tests (`tests/Unit/InvoiceFilenameTest.php`)
- Tests basic filename sanitization
- Tests all problematic characters
- Tests that normal characters are preserved

### Feature Tests (`tests/Feature/InvoiceDownloadTest.php`)
- Tests invoice download functionality with sanitized filenames
- Tests various invoice number formats
- Verifies no problematic characters remain in filenames

### Test Results
```bash
✓ pdf filename sanitizes problematic characters
✓ pdf filename sanitizes all problematic characters  
✓ pdf filename preserves normal characters
✓ invoice download works with sanitized filename
✓ various invoice numbers generate valid filenames
✓ filename contains no problematic characters
```

## 📝 Examples

| Invoice Number | Generated Filename |
|----------------|-------------------|
| `FV/2025/06/0001` | `faktura_FV_2025_06_0001.pdf` |
| `INV\2025\12\0001` | `faktura_INV_2025_12_0001.pdf` |
| `FACT:2025:01:0001` | `faktura_FACT_2025_01_0001.pdf` |
| `DOC*2025*02*0001` | `faktura_DOC_2025_02_0001.pdf` |

## 🚀 Deployment

The fix is backward compatible and doesn't require any database changes or configuration updates. Existing invoices will automatically use the sanitized filename when downloaded.

## ✅ Verification

To verify the fix works:

1. **Run Tests:**
   ```bash
   docker-compose exec app php artisan test tests/Unit/InvoiceFilenameTest.php
   docker-compose exec app php artisan test tests/Feature/InvoiceDownloadTest.php
   ```

2. **Test Invoice Download:**
   - Generate an invoice for any order
   - Try downloading the invoice PDF
   - The download should work without filename errors

3. **Check Filename:**
   - The downloaded file should be named like: `faktura_FV_2025_06_0001.pdf`
   - No forward slashes or other problematic characters should be present

## 🔧 Configuration

No configuration changes are required. The current invoice numbering format in `.env` can remain as:
```env
INVOICE_NUMBER_FORMAT="{prefix}/{year}/{month}/{number}"
```

The sanitization happens automatically when generating filenames, while preserving the original invoice number format for display and database storage.

## 📚 Related Files

- `app/Models/Invoice.php` - Invoice model with sanitized filename attribute
- `app/Services/InvoiceService.php` - Invoice service with filename generation
- `tests/Unit/InvoiceFilenameTest.php` - Unit tests for filename sanitization
- `tests/Feature/InvoiceDownloadTest.php` - Feature tests for invoice download
- `app/Http/Controllers/Admin/InvoiceController.php` - Invoice download controller
