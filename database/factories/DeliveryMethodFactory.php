<?php

namespace Database\Factories;

use App\Models\DeliveryMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeliveryMethod>
 */
class DeliveryMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true) . ' Delivery',
            'code' => fake()->unique()->slug(2),
            'type' => fake()->randomElement(['point', 'courier']),
            'is_active' => true,
            'sort_order' => fake()->numberBetween(0, 100),
            'price' => fake()->randomFloat(2, 5, 25),
            'min_cart_amount' => fake()->optional()->randomFloat(2, 100, 500),
            'api_provider' => fake()->randomElement(['inpost', 'orlen', null]),
            'api_config' => null,
            'map_config' => null,
            'description' => fake()->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the delivery method is for InPost.
     */
    public function inpost(): static
    {
        return $this->state(fn (array $attributes) => [
            'api_provider' => 'inpost',
            'name' => 'InPost ' . ($attributes['type'] === 'point' ? 'Paczkomat' : 'Courier'),
            'code' => 'inpost_' . $attributes['type'],
        ]);
    }

    /**
     * Indicate that the delivery method is for courier delivery.
     */
    public function courier(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'courier',
        ]);
    }

    /**
     * Indicate that the delivery method is for pickup points.
     */
    public function point(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'point',
        ]);
    }

    /**
     * Indicate that the delivery method is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
