<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use App\Models\DeliveryAddress;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'user_id' => User::factory(),
            'delivery_address_id' => DeliveryAddress::factory(),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'status' => 'pending',
            'payment_method' => 'payu',
            'payment_status' => 'pending',
            'delivery_method' => 'InPost Courier',
            'subtotal' => fake()->randomFloat(2, 50, 500),
            'delivery_cost' => fake()->randomFloat(2, 10, 30),
            'total' => function (array $attributes) {
                return $attributes['subtotal'] + $attributes['delivery_cost'];
            },
            'delivery_address' => [
                'type' => 'address',
                'method_name' => 'InPost Courier',
                'name' => fake()->name(),
                'street' => fake()->streetName(),
                'building_number' => fake()->buildingNumber(),
                'post_code' => fake()->postcode(),
                'city' => fake()->city(),
                'phone' => fake()->phoneNumber(),
            ],
            'billing_address' => null,
            'notes' => fake()->optional()->sentence(),
            'molos_order_id' => null,
            'molos_status' => null,
            'inpost_shipment_id' => null,
            'inpost_tracking_number' => null,
            'inpost_status' => null,
            'payu_order_id' => null,
            'payu_external_id' => null,
            'payment_checked_at' => null,
        ];
    }

    /**
     * Indicate that the order is for a guest (no user).
     */
    public function guest(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => null,
        ]);
    }

    /**
     * Indicate that the order is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'completed',
            'payment_checked_at' => now(),
        ]);
    }

    /**
     * Indicate that the order is shipped.
     */
    public function shipped(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'shipped',
            'inpost_tracking_number' => 'INP' . fake()->numerify('#########'),
            'inpost_status' => 'created',
        ]);
    }

    /**
     * Indicate that the order is delivered.
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'inpost_tracking_number' => 'INP' . fake()->numerify('#########'),
            'inpost_status' => 'delivered',
        ]);
    }

    /**
     * Indicate that the order is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }
}
