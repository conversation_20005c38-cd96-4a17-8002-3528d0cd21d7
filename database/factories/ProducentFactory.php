<?php

namespace Database\Factories;

use App\Models\Producent;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Producent>
 */
class ProducentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Producent::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->company();
        
        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->optional()->paragraph(),
            'molos_id' => $this->faker->optional()->unique()->numerify('###'),
            'image' => $this->faker->optional()->imageUrl(200, 200, 'business'),
            'priority' => $this->faker->boolean(70), // 70% chance of being priority
        ];
    }

    /**
     * Indicate that the producent has priority.
     */
    public function priority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => true,
        ]);
    }

    /**
     * Indicate that the producent does not have priority.
     */
    public function noPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => false,
        ]);
    }
}
