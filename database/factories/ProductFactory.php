<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use App\Models\Producent;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        
        return [
            'sku' => $this->faker->unique()->bothify('SKU-####-????'),
            'molos_id' => $this->faker->optional()->unique()->numerify('###'),
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->optional()->paragraphs(3, true),
            'short_description' => $this->faker->optional()->sentence(),
            'generated_description' => $this->faker->optional()->paragraph(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'price_percentage' => $this->faker->optional()->numberBetween(10, 50),
            'molos_price' => $this->faker->optional()->randomFloat(2, 8, 800),
            'stock' => $this->faker->numberBetween(0, 100),
            'image' => $this->faker->optional()->imageUrl(400, 400, 'products'),
            'weight' => $this->faker->optional()->randomFloat(2, 0.1, 50),
            'length' => $this->faker->optional()->randomFloat(2, 1, 100),
            'width' => $this->faker->optional()->randomFloat(2, 1, 100),
            'height' => $this->faker->optional()->randomFloat(2, 1, 100),
            'category_id' => Category::factory(),
            'producent_id' => Producent::factory(),
            'gallery' => $this->faker->optional()->json(),
            'ean' => $this->faker->optional()->ean13(),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft']),
            'unit' => $this->faker->optional()->randomElement(['szt', 'kg', 'l', 'm']),
            'molos_category_id' => $this->faker->optional()->numerify('###'),
            'vat' => $this->faker->randomElement([0, 8, 23]),
        ];
    }

    /**
     * Indicate that the product is in stock.
     */
    public function inStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => $this->faker->numberBetween(1, 100),
        ]);
    }

    /**
     * Indicate that the product is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => 0,
        ]);
    }

    /**
     * Indicate that the product is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the product belongs to a specific category.
     */
    public function inCategory(Category $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category_id' => $category->id,
        ]);
    }

    /**
     * Create a product with a specific name pattern for testing variants.
     */
    public function withNamePattern(string $baseName, string $variant = ''): static
    {
        $fullName = $baseName . ($variant ? ' ' . $variant : '');
        
        return $this->state(fn (array $attributes) => [
            'name' => $fullName,
            'slug' => Str::slug($fullName),
        ]);
    }
}
