<?php

namespace Database\Factories;

use App\Models\DeliveryAddress;
use App\Models\User;
use App\Models\DeliveryMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeliveryAddress>
 */
class DeliveryAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'delivery_method_id' => DeliveryMethod::factory(),
            'name' => fake()->name(),
            'type' => 'address',
            'street' => fake()->streetName(),
            'building_number' => fake()->buildingNumber(),
            'apartment_number' => fake()->optional()->numerify('##'),
            'post_code' => fake()->postcode(),
            'city' => fake()->city(),
            'phone' => fake()->phoneNumber(),
            'is_default' => false,
            'point_id' => null,
            'point_address' => null,
            'point_data' => null,
        ];
    }

    /**
     * Indicate that the delivery address is for a pickup point.
     */
    public function point(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'point',
            'point_id' => 'POZ' . fake()->numerify('####'),
            'point_address' => fake()->address(),
            'point_data' => [
                'point_name' => fake()->company() . ' - ' . fake()->streetAddress(),
                'point_id' => 'POZ' . fake()->numerify('####'),
                'point_address' => fake()->address(),
            ],
            // Clear address fields for point delivery
            'street' => null,
            'building_number' => null,
            'apartment_number' => null,
        ]);
    }

    /**
     * Indicate that the delivery address is the default.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Indicate that the delivery address is for a guest (no user).
     */
    public function guest(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => null,
        ]);
    }
}
