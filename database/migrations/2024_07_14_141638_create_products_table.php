<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('sku')->unique();
            $table->string('molos_id')->nullable();        
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->text('generated_description')->nullable();
            $table->decimal('price', 10, 2);
            $table->decimal('molos_price', 10, 2)->nullable();
            $table->integer('price_percentage')->nullable();
            $table->integer('stock')->default(0);
            $table->string('image')->nullable();
            $table->decimal('weight', 8, 2)->nullable();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('molos_category_id')->nullable();
            $table->foreignId('producent_id')->constrained()->onDelete('cascade');
            $table->json('gallery')->nullable();
            $table->string('ean')->nullable();
            $table->boolean('status')->default(true);
            $table->string('unit')->nullable();
            $table->index('molos_category_id');
            $table->index('molos_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
