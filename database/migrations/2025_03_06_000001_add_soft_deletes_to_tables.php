<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('delivery_addresses', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('delivery_methods', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('delivery_addresses', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('delivery_methods', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
}; 