<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->integer('parcel_count')->default(1)->after('delivery_cost');
            $table->json('parcel_data')->nullable()->after('parcel_count');
            $table->string('delivery_calculation_method')->default('traditional')->after('parcel_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['parcel_count', 'parcel_data', 'delivery_calculation_method']);
        });
    }
};
