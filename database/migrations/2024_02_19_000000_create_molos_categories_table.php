<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('molos_categories', function (Blueprint $table) {
            $table->id();
            $table->string('molos_id')->unique();
            $table->string('name');
            $table->string('path')->nullable();
            $table->string('parent_molos_id')->nullable();
            $table->timestamps();

            $table->index('parent_molos_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('molos_categories');
    }
}; 