<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('delivery_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->enum('type', ['point', 'courier']);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->decimal('price', 8, 2)->default(0);
            $table->decimal('min_cart_amount', 8, 2)->nullable();
            $table->string('api_provider')->nullable();
            $table->json('api_config')->nullable();
            $table->json('map_config')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Insert default delivery methods
        DB::table('delivery_methods')->insert([
            [
                'name' => 'InPost Courier',
                'code' => 'inpost_courier',
                'type' => 'courier',
                'is_active' => true,
                'price' => 14.99,
                'api_provider' => 'inpost',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'InPost Paczkomat',
                'code' => 'inpost_paczkomat',
                'type' => 'point',
                'is_active' => true,
                'price' => 11.99,
                'api_provider' => 'inpost',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    public function down()
    {
        Schema::dropIfExists('delivery_methods');
    }
}; 