<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->date('issue_date');
            $table->date('due_date');
            $table->date('sale_date'); // Data sprzedaży - required for Polish invoices

            // Seller information (snapshot at time of invoice generation)
            $table->json('seller_data');

            // Buyer information (snapshot at time of invoice generation)
            $table->json('buyer_data');

            // Invoice totals
            $table->decimal('subtotal', 10, 2);
            $table->decimal('vat_amount', 10, 2);
            $table->decimal('total', 10, 2);
            $table->string('currency', 3)->default('PLN');

            // Invoice items (snapshot of order items at time of generation)
            $table->json('items');

            // Additional invoice data
            $table->text('notes')->nullable();
            $table->string('payment_method')->nullable();
            $table->integer('payment_terms_days')->default(14); // Payment terms in days

            // File storage
            $table->string('pdf_path')->nullable(); // Path to generated PDF file
            $table->timestamp('pdf_generated_at')->nullable();

            // Status tracking
            $table->enum('status', ['draft', 'sent', 'paid', 'overdue', 'cancelled'])->default('draft');

            $table->timestamps();

            // Indexes
            $table->index(['order_id']);
            $table->index(['issue_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
