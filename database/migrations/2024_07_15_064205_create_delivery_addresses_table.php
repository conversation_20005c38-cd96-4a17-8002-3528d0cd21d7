<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('delivery_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('delivery_method_id')->constrained();
            $table->string('name')->nullable();
            $table->string('street')->nullable();
            $table->string('building_number')->nullable();
            $table->string('apartment_number')->nullable();
            $table->string('post_code')->nullable();
            $table->string('city')->nullable();
            $table->string('phone');
            $table->boolean('is_default')->default(false);
            $table->enum('type', ['address', 'point'])->default('address');
            $table->string('point_id')->nullable();
            $table->string('point_address')->nullable();
            $table->json('point_data')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('delivery_addresses');
    }
}; 