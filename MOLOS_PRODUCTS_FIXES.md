# MolosProducts Service Fixes

## 🐛 Issues Fixed

### Issue 1: Price Calculation Logic Bug
**Problem:** The `calculatePriceFromMolos()` method was called before updating the `molos_price` field, causing it to calculate using the old price and always return the same value.

**Root Cause:** In the `updateSingleProductStockAndPrice()` method, the sequence was:
1. `$newFinalPrice = $product->calculatePriceFromMolos()` (using old molos_price)
2. `$updateData['molos_price'] = $newMolosPrice` (updating molos_price)
3. `$product->update($updateData)` (saving changes)

**Solution:** 
- Removed the price calculation logic from the update data preparation
- Added `$product->updatePriceFromPercentage()` call after the update to recalculate price using the newly updated `molos_price`

### Issue 2: Scope Reduction for updateExistingProduct Method
**Problem:** The `updateExistingProduct()` method was updating too many fields, potentially overwriting manually curated content.

**Solution:** Reduced the scope to only update operational data:
- ✅ **Stock** (inventory levels)
- ✅ **Price and molos_price** (pricing information)
- ✅ **VAT** (tax information)

**Removed updates for:**
- ❌ Category and producent assignments
- ❌ Product descriptions (name, description, short_description)
- ❌ Other metadata fields (ean, status, unit, slug, etc.)
- ❌ Image and gallery updates

## ✅ Changes Implemented

### 1. Fixed Price Calculation in `updateSingleProductStockAndPrice()`

**Before:**
```php
// Update Molos price
$newMolosPrice = $productData['price_gross'] ?? $productData['price'] ?? 0;
if ($product->molos_price != $newMolosPrice) {
    $updateData['molos_price'] = $newMolosPrice;
    
    // BUG: This uses the OLD molos_price!
    $newFinalPrice = $product->calculatePriceFromMolos();
    if ($product->price != $newFinalPrice) {
        $updateData['price'] = $newFinalPrice;
        // ...
    }
}
```

**After:**
```php
// Update Molos price
$newMolosPrice = $productData['price_gross'] ?? $productData['price'] ?? 0;
if ($product->molos_price != $newMolosPrice) {
    $updateData['molos_price'] = $newMolosPrice;
    $hasChanges = true;
    // Price calculation moved to after update
}

// Later, after $product->update($updateData):
if (isset($updateData['molos_price'])) {
    $product->updatePriceFromPercentage(); // Uses NEW molos_price
}
```

### 2. Streamlined `updateExistingProduct()` Method

**Before:** 115 lines updating everything including categories, descriptions, images, etc.

**After:** 95 lines focusing only on operational data:

```php
public function updateExistingProduct(Product $product, array $productData)
{
    // Only update operational fields
    $updateData = [];
    $hasChanges = false;

    // Update stock
    $newStock = $productData['store'] ?? 0;
    if ($product->stock != $newStock) {
        $updateData['stock'] = $newStock;
        $hasChanges = true;
    }

    // Update Molos price
    $newMolosPrice = $productData['price_gross'] ?? 0;
    if ($product->molos_price != $newMolosPrice) {
        $updateData['molos_price'] = $newMolosPrice;
        $hasChanges = true;
    }

    // Update VAT
    $newVat = $productData['vat_value'] ?? null;
    if ($product->vat != $newVat) {
        $updateData['vat'] = $newVat;
        $hasChanges = true;
    }

    // Update and recalculate price if needed
    if ($hasChanges) {
        $product->update($updateData);
        
        if (isset($updateData['molos_price'])) {
            $product->updatePriceFromPercentage();
        }
    }
}
```

## 🧪 Testing

### Enhanced Test Coverage
Added comprehensive test: `test_price_calculation_after_molos_price_update()`

**Test Scenario:**
- Product with 50% markup category
- Initial molos_price: 100.00, price: 150.00
- Update molos_price to: 200.00
- Expected final price: 300.00 (200.00 × 1.5)

**Test Results:**
```bash
✓ existing product is updated with vat
✓ new product import includes vat  
✓ vat field handles null values
✓ price calculation after molos price update
```

### Updated Existing Tests
Modified `test_existing_product_is_updated_with_vat()` to verify:
- ✅ Operational fields ARE updated (stock, molos_price, vat, calculated price)
- ✅ Descriptive fields are NOT updated (name, description, unit, status)

## 📊 Impact Analysis

### Before Fixes:
1. **Price Bug**: Products with updated molos_price kept old final prices
2. **Scope Issue**: Manual content was overwritten during updates

### After Fixes:
1. **Accurate Pricing**: Final prices correctly reflect updated molos_price with proper markup
2. **Preserved Content**: Manual descriptions, categories, and metadata remain untouched
3. **Focused Updates**: Only operational data (stock, pricing, VAT) gets synchronized

## 🔧 Technical Details

### Price Calculation Flow:
1. **Update molos_price** in database
2. **Call updatePriceFromPercentage()** which:
   - Calls `calculatePriceFromMolos()` with NEW molos_price
   - Applies category/product percentage markup
   - Saves the calculated final price

### Logging Enhancements:
- **Stock changes**: Old vs new stock levels
- **Price changes**: Old vs new molos_price
- **VAT changes**: Old vs new VAT values
- **Price recalculation**: Final calculated price after update

## 🚀 Benefits

1. **Accurate Pricing**: Products now have correct final prices based on latest molos_price
2. **Content Preservation**: Manual product descriptions and categorization remain intact
3. **Focused Synchronization**: Only operational data gets updated from Molos API
4. **Better Performance**: Fewer fields updated means faster operations
5. **Reduced Risk**: Less chance of overwriting manually curated content
6. **Enhanced Logging**: Better visibility into what changes during updates

## 📋 Usage

### For Stock/Price Updates:
```php
// This method now correctly calculates final prices
$updated = $molosProducts->updateSingleProductStockAndPrice($productData);
```

### For Existing Product Updates:
```php
// This method now only updates operational data
$product = $molosProducts->updateExistingProduct($existingProduct, $productData);
```

## 🔍 Verification

To verify the fixes work correctly:

1. **Run Tests:**
   ```bash
   php artisan test tests/Feature/MolosProductUpdateTest.php
   ```

2. **Check Price Calculation:**
   - Update a product's molos_price via API
   - Verify final price = molos_price × (1 + percentage/100)

3. **Verify Scope Limitation:**
   - Update an existing product
   - Confirm descriptions and categories remain unchanged
   - Confirm stock, pricing, and VAT are updated

The fixes ensure reliable, focused product updates while preserving manually curated content and maintaining accurate pricing calculations.
