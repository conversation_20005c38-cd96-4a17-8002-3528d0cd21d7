FROM php:8.3-fpm-alpine

# Install system dependencies
RUN apk update && apk add --no-cache \
    git \
    curl \
    autoconf \
    build-base \
    libpng \
    libpng-dev \
    libjpeg-turbo \
    libjpeg-turbo-dev \
    freetype \
    freetype-dev \
    libwebp-dev \
    libxpm-dev \
    libzip-dev \
    libxml2 \
    libxml2-dev \
    oniguruma-dev \
    imagemagick \
    imagemagick-dev \
    libtool \
    bash \
    supervisor \
    nodejs \
    npm \
    nginx \
    jpegoptim \
    optipng \
    pngquant \
    gifsicle \
    unzip

# Configure and install PHP extensions
RUN docker-php-ext-configure gd \
        --with-freetype \
        --with-jpeg \
        --with-webp \
    && docker-php-ext-install \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
    && pecl install imagick \
    && docker-php-ext-enable imagick \
    && pecl install redis \
    && docker-php-ext-enable redis

# Clean up to reduce image size
# RUN apk del --no-cache \
#     autoconf \
#     build-base \
#     libtool \
#     libpng-dev \
#     libjpeg-turbo-dev \
#     freetype-dev \
#     libwebp-dev \
#     libxpm-dev \
#     libxml2-dev \
#     oniguruma-dev \
#     imagemagick-dev

# Install SVGO via npm
RUN npm install -g svgo

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy Nginx configuration
COPY docker/nginx.conf /etc/nginx/nginx.conf

# Copy Supervisor configuration
COPY docker/supervisord.conf /etc/supervisord.conf

# Copy entrypoint script
COPY docker/entrypoint.sh /entrypoint.sh

# Make entrypoint script executable
RUN chmod +x /entrypoint.sh

# Expose port 80
EXPOSE 80

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]
