[supervisord]
nodaemon=true
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/var/run/supervisord.pid

[program:php-fpm]
command=php-fpm
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:nginx]
command=nginx -g "daemon off;"
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

; Default Queue Worker
[program:queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:listen --queue=default --timeout=180 --sleep=3 --tries=3 -vv
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=2
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

; Import Queue Worker
[program:queue-import]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:listen --queue=import --timeout=180 --sleep=3 --tries=3 -vv
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

; Order Queue Worker
[program:queue-order]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:listen --queue=order --timeout=180 --sleep=3 --tries=3 -vv
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=2
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

; Mail Queue Worker
[program:queue-mail]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:listen --queue=mail --timeout=180 --sleep=3 --tries=3 -vv
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0