#!/bin/bash

# Wait for MySQL to be ready
echo "Waiting for MySQL..."
while ! nc -z mysql 3306; do
  sleep 1
done
echo "MySQL is up and running."

# Install Composer dependencies
if [ ! -d "vendor" ]; then
  echo "Installing Composer dependencies..."
  composer install
fi

# Generate application key
if [ ! -f ".env" ]; then
  echo "Copying .env.example to .env..."
  cp .env.example .env
  php artisan key:generate
fi

# Run migrations
echo "Running migrations..."
php artisan migrate

# Set permissions
echo "Setting permissions..."
chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Start Supervisor to manage processes
echo "Starting Supervisor..."
/usr/bin/supervisord -c /etc/supervisord.conf
