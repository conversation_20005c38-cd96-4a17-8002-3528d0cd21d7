<?php

namespace App\Observers;

use App\Models\PriceHistory;
use App\Models\Product;

class ProductObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(Product $product)
    {
        PriceHistory::create([
            'product_id' => $product->id,
            'price' => $product->price,
            'date' => now(),
        ]);
    }
    /**
     * Handle the Product "updated" event.
     */    
    public function updated(Product $product)
    {
        if ($product->isDirty('price')) {
            PriceHistory::create([
                'product_id' => $product->id,
                'price' => $product->price,
                'date' => now(),
            ]);
        }
    }


    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Product $product): void
    {
        //
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Product $product): void
    {
        //
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Product $product): void
    {
        //
    }
}
