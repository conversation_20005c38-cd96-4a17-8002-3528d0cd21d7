<?php

namespace App\Observers;

use App\Models\Order;
use App\Models\User;
use App\Notifications\OrderStatusChanged;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class OrderObserver
{
    /**
     * Handle the Order "updated" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function updated(Order $order)
    {
        // Check if status field was changed
        if ($order->isDirty('status')) {
            $this->handleStatusChange($order, 'status', $order->status);
        }

        // Check if payment_status field was changed
        if ($order->isDirty('payment_status')) {
            $this->handleStatusChange($order, 'payment_status', $order->payment_status);
        }

        // Check if inpost_tracking_number was added (shipment created)
        if ($order->isDirty('inpost_tracking_number') && $order->inpost_tracking_number) {
            $this->handleStatusChange($order, 'status', 'shipped');
        }
    }

    /**
     * Handle status change and send notification
     *
     * @param Order $order
     * @param string $field
     * @param string $newStatus
     * @return void
     */
    protected function handleStatusChange(Order $order, string $field, string $newStatus)
    {
        try {
            // Create notification instance
            $notification = new OrderStatusChanged($order, $newStatus);
            
            // Check if we should send this notification
            if (!$notification->shouldNotify()) {
                Log::info('Skipping notification for status', [
                    'order_id' => $order->id,
                    'field' => $field,
                    'status' => $newStatus
                ]);
                return;
            }

            // Determine who to notify
            $notifiable = $this->getNotifiable($order);
            
            if ($notifiable) {
                // Send notification
                $notifiable->notify($notification);
                
                Log::info('Order status notification sent', [
                    'order_id' => $order->id,
                    'field' => $field,
                    'old_value' => $order->getOriginal($field),
                    'new_value' => $newStatus,
                    'recipient' => $notifiable->email ?? $order->email
                ]);
            } else {
                Log::warning('Could not determine notification recipient', [
                    'order_id' => $order->id,
                    'order_email' => $order->email,
                    'user_id' => $order->user_id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send order status notification', [
                'order_id' => $order->id,
                'field' => $field,
                'status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get the notifiable entity (User or anonymous notification)
     *
     * @param Order $order
     * @return mixed
     */
    protected function getNotifiable(Order $order)
    {
        // If order has a user, notify the user
        if ($order->user_id && $order->user) {
            return $order->user;
        }

        // For guest orders, create an anonymous notifiable
        if ($order->email) {
            return new class($order->email, $order->first_name) {
                public $email;
                public $first_name;

                public function __construct($email, $first_name)
                {
                    $this->email = $email;
                    $this->first_name = $first_name;
                }

                public function routeNotificationForMail()
                {
                    return $this->email;
                }
            };
        }

        return null;
    }
}
