<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PayuService
{
    protected $baseUrl;
    protected $clientId;
    protected $clientSecret;
    protected $posId;
    protected $secondKey;

    public function __construct()
    {
        $this->baseUrl = config('services.payu.environment') === 'sandbox' 
            ? 'https://secure.snd.payu.com' 
            : 'https://secure.payu.com';
        $this->clientId = config('services.payu.oauth_client_id');
        $this->clientSecret = config('services.payu.oauth_client_secret');
        $this->posId = config('services.payu.pos_id');
        $this->secondKey = config('services.payu.signature_key');
    }

    public function createOrder(array $data)
    {
        try {
            $token = $this->getAccessToken();

            $payload = $this->generateOrderPayload($data);

            // dd($data, $payload);
            $response = Http::withToken($token)
                ->withoutRedirecting()
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ])
                ->post($this->baseUrl . '/api/v2_1/orders', $payload);

            // Check for 302 redirect response
            if ($response->found()) {
                $responseData = $response->json();
                // dd($responseData);
                return [
                    'status' => 'success',
                    'redirectUri' => $responseData['redirectUri'] ?? null,
                    'payuOrderId' => $responseData['orderId'] ?? null,
                    'extOrderId' => $responseData['extOrderId'] ?? null
                ];
            }

            throw new \Exception('PayU API error: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('PayU order creation error', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    protected function generateOrderPayload(array $data): array
    {
        return [
            'continueUrl' => route('payment.continue', ['order' => $data['orderId']]),
            'notifyUrl' => route('payment.notify'),
            'customerIp' => request()->ip(),
            'merchantPosId' => $this->posId,
            'description' => $data['description'],
            'currencyCode' => 'PLN',
            'totalAmount' => (int)$data['amount'],
            'extOrderId' => $data['orderId'],
            'buyer' => [
                'email' => $data['customer']['email'],
                'firstName' => $data['customer']['firstName'],
                'lastName' => $data['customer']['lastName'],
                'phone' => $data['customer']['phone'],
                'language' => 'pl'
            ],
            'products' => [
                [
                    'name' => $data['description'],
                    'unitPrice' => (int)$data['amount'],
                    'quantity' => 1
                ]
            ],
            // 'payMethod' => [
            //     'type' => 'PBL', // Payment By Link
            //     'value' => $data['payMethod']['value']
            // ],
            'settings' => [
                'invoiceDisabled' => true
            ]
        ];
    }

    public function handleNotification(Request $request)
    {
        try {
            $payload = $request->getContent();
            $headers = $request->header();
            
            // Verify signature
            $signature = $headers['openpayu-signature'][0] ?? '';
            if (!$this->verifySignature($payload, $signature)) {
                throw new \Exception('Invalid PayU signature');
            }

            $notification = json_decode($payload, true);
            
            if ($notification['order']['status'] === 'COMPLETED') {
                return [
                    'status' => 'completed',
                    'order_id' => $notification['order']['extOrderId'],
                    'payu_order_id' => $notification['order']['orderId']
                ];
            }

            return [
                'status' => strtolower($notification['order']['status']),
                'order_id' => $notification['order']['extOrderId'],
                'payu_order_id' => $notification['order']['orderId']
            ];
        } catch (\Exception $e) {
            Log::error('PayU notification handling error', [
                'error' => $e->getMessage(),
                'payload' => $payload ?? null
            ]);
            throw $e;
        }
    }

    protected function getAccessToken()
    {
        return Cache::remember('payu_access_token', 3500, function () {
            $response = Http::asForm()
                ->post($this->baseUrl . '/pl/standard/user/oauth/authorize', [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret
                ]);

            if ($response->successful()) {
                return $response->json()['access_token'];
            }

            throw new \Exception('Failed to get PayU access token: ' . $response->body());
        });
    }

    protected function verifySignature(string $payload, string $signature): bool
    {
        $algorithm = "sha256";
        if (empty($signature)) {
            return false;
        }

        // Extract signature value
        preg_match('/signature=([0-9a-z]{64})/', $signature, $matches);
        if (empty($matches[1])) {
            return false;
        }

        $signatureValue = $matches[1];
        $concatPayload = $payload . $this->secondKey;
        $hash = hash($algorithm, $concatPayload);

        return $hash === $signatureValue;
    }

    public function getOrderStatus(string $orderId)
    {
        try {
            $token = $this->getAccessToken();

            $response = Http::withToken($token)
                ->get($this->baseUrl . '/api/v2_1/orders/' . $orderId);

            if ($response->successful()) {
                return $response->json();
            }

            throw new \Exception('Failed to get order status: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('PayU order status check error', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);
            throw $e;
        }
    }

    public function getPaymentMethods()
    {
        try {
            $token = $this->getAccessToken();

            $response = Http::withToken($token)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ])
                ->get($this->baseUrl . '/api/v2_1/paymethods');

            if (!$response->successful()) {
                throw new \Exception('PayU API error: ' . $response->body());
            }

            $responseData = $response->json();
            // dd($responseData);
            return [
                'status' => 'success',
                'paymentMethods' => $responseData['payByLinks'] ?? []
            ];

        } catch (\Exception $e) {
            Log::error('PayU payment methods retrieval error', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get payment URL for an existing order or create a new payment
     * 
     * @param string $payuOrderId
     * @param Order $order
     * @return string|null
     */
    public function getPaymentUrl(string $payuOrderId, $order = null)
    {
        try {
            $token = $this->getAccessToken();
            
            // First, check the existing order status
            $response = Http::withToken($token)
                ->get($this->baseUrl . '/api/v2_1/orders/' . $payuOrderId);
            
            if ($response->successful()) {
                $data = $response->json();
                $status = $data['orders'][0]['status'] ?? null;
                
                // If the order is COMPLETED or PENDING, no need for a new payment
                if (in_array($status, ['COMPLETED', 'PENDING'])) {
                    return null;
                }
                
                // If we have a redirectUri, use it
                if (isset($data['orders'][0]['redirectUri'])) {
                    return $data['orders'][0]['redirectUri'];
                }
                
                // For orders in NEW or CANCELED status, we need to use the order retrieval endpoint
                // to get the payment URL instead of creating a new order
                try {
                    $paymentResponse = Http::withToken($token)
                        ->withHeaders([
                            'Content-Type' => 'application/json',
                            'Accept' => 'application/json'
                        ])
                        ->post($this->baseUrl . '/api/v2_1/orders/' . $payuOrderId . '/transactions', [
                            'customerIp' => request()->ip(),
                            'continueUrl' => route('payment.continue', ['order' => $order->uuid]),
                        ]);
                    
                    if ($paymentResponse->successful()) {
                        $paymentData = $paymentResponse->json();
                        if (isset($paymentData['redirectUri'])) {
                            return $paymentData['redirectUri'];
                        }
                    }
                } catch (\Exception $e) {
                    // If this fails, we'll try the next approach
                    Log::warning('Failed to get payment URL via transaction endpoint', [
                        'order_id' => $payuOrderId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // If we reach here and have no order object, we can't proceed
            if (!$order) {
                return null;
            }
            
            // As a last resort, try to create a new order with a slightly modified extOrderId
            // by appending a timestamp to make it unique
            $uniqueExtOrderId = $order->uuid . '-' . time();
            
            $payuResponse = $this->createOrder([
                'orderId' => $uniqueExtOrderId, // Use a unique external order ID
                'amount' => $order->total * 100,
                'description' => "Zamówienie {$order->uuid} (ponowna płatność)",
                'customer' => [
                    'email' => $order->email,
                    'firstName' => $order->first_name,
                    'lastName' => $order->last_name,
                    'phone' => $order->phone,
                ],
            ]);
            
            // Update the order with the new PayU order ID
            $order->update([
                'payu_order_id' => $payuResponse['payuOrderId'] ?? $order->payu_order_id,
            ]);
            
            return $payuResponse['redirectUri'] ?? null;
            
        } catch (\Exception $e) {
            Log::error('PayU get payment URL error', [
                'error' => $e->getMessage(),
                'order_id' => $payuOrderId
            ]);
            throw $e;
        }
    }
}
