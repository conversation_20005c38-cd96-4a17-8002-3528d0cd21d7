<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Product;
use Illuminate\Support\Facades\Log;

class ParcelCalculatorService
{
    protected $molosProducts;

    // InPost Paczkomat size limits (in cm)
    const INPOST_SIZES = [
        'A' => ['length' => 8, 'width' => 38, 'height' => 64, 'weight' => 25], // Small
        'B' => ['length' => 19, 'width' => 38, 'height' => 64, 'weight' => 25], // Medium  
        'C' => ['length' => 41, 'width' => 38, 'height' => 64, 'weight' => 25], // Large
    ];

    // Courier limits are more flexible
    const COURIER_MAX = ['length' => 200, 'width' => 200, 'height' => 200, 'weight' => 30];

    public function __construct()
    {
        $this->molosProducts = app(MolosProducts::class);
    }

    /**
     * Calculate parcel dimensions for an order
     */
    public function calculateParcelDimensions(Order $order)
    {
        $isInPostPaczkomat = $order->deliveryAddress && 
                           $order->deliveryAddress->deliveryMethod && 
                           $order->deliveryAddress->deliveryMethod->api_provider === 'inpost' &&
                           $order->deliveryAddress->deliveryMethod->type === 'point';

        // Get all products with their dimensions
        $productDimensions = [];
        $totalWeight = 0;

        foreach ($order->items as $item) {
            $product = $item->product;
            
            // Ensure product has dimensions
            $dimensions = $this->molosProducts->ensureProductDimensions($product);
            
            for ($i = 0; $i < $item->quantity; $i++) {
                $productDimensions[] = array_merge($dimensions, [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'item_index' => count($productDimensions)
                ]);
                $totalWeight += $dimensions['weight'];
            }
        }

        if (empty($productDimensions)) {
            return [$this->getDefaultDimensions($isInPostPaczkomat)];
        }

        // Check if we need multiple parcels
        if ($isInPostPaczkomat) {
            return $this->calculateMultiParcelInPost($productDimensions, $totalWeight);
        } else {
            return $this->calculateMultiParcelCourier($productDimensions, $totalWeight);
        }
    }

    /**
     * Calculate multiple parcels for InPost Paczkomat
     */
    protected function calculateMultiParcelInPost($products, $totalWeight)
    {
        // Sort products by volume (largest first) for better packing
        usort($products, function($a, $b) {
            $volumeA = $a['length'] * $a['width'] * $a['height'];
            $volumeB = $b['length'] * $b['width'] * $b['height'];
            return $volumeB <=> $volumeA;
        });

        $parcels = [];
        $remainingProducts = $products;

        while (!empty($remainingProducts)) {
            $parcel = $this->packSingleInPostParcel($remainingProducts);
            $parcels[] = $parcel;
            
            // Remove packed products from remaining list
            foreach ($parcel['packed_products'] as $packedProduct) {
                $remainingProducts = array_filter($remainingProducts, function($product) use ($packedProduct) {
                    return $product['item_index'] !== $packedProduct['item_index'];
                });
                $remainingProducts = array_values($remainingProducts); // Re-index array
            }

            // Safety check to prevent infinite loop
            if (count($parcels) > 20) {
                Log::error('Too many parcels needed, stopping calculation', [
                    'parcel_count' => count($parcels),
                    'remaining_products' => count($remainingProducts)
                ]);
                break;
            }
        }

        Log::info('InPost multi-parcel calculation completed', [
            'total_parcels' => count($parcels),
            'total_products' => count($products)
        ]);

        return $parcels;
    }

    /**
     * Pack products into a single InPost parcel
     */
    protected function packSingleInPostParcel($products)
    {
        $packedProducts = [];
        $currentWeight = 0;

        foreach ($products as $product) {
            $productWeight = $product['weight'] + 0.1; // Add some packaging per item

            // Check if this product can fit in current parcel
            $wouldExceedWeight = ($currentWeight + $productWeight) > 24; // Leave 1kg margin for packaging
            
            // Try to fit within largest InPost size (C) with packaging - be more conservative
            $testPacking = $this->packProductsForInPost(array_merge($packedProducts, [$product]));
            
            $wouldExceedSize = ($testPacking['length'] > self::INPOST_SIZES['C']['length'] ||
                               $testPacking['width'] > self::INPOST_SIZES['C']['width'] ||
                               $testPacking['height'] > self::INPOST_SIZES['C']['height']);

            if (!$wouldExceedWeight && !$wouldExceedSize) {
                $packedProducts[] = $product;
                $currentWeight += $productWeight;
            } else {
                // If we can't fit this product and we have no products yet, 
                // we must include it anyway (oversized single item)
                if (empty($packedProducts)) {
                    $packedProducts[] = $product;
                    $currentWeight += $productWeight;
                    Log::warning('Single product exceeds InPost limits', [
                        'product' => $product,
                        'weight' => $productWeight,
                        'test_dimensions' => $testPacking
                    ]);
                }
                break; // Stop adding more products to this parcel
            }
        }

        // Calculate final parcel dimensions with InPost-specific packing
        $packedDimensions = $this->packProductsForInPost($packedProducts);
        $packedDimensions['weight'] = round($currentWeight + 0.3, 2);

        // Determine InPost size
        $inpostSize = 'C'; // Default to largest
        $oversized = false;

        foreach (self::INPOST_SIZES as $sizeName => $limits) {
            if ($packedDimensions['length'] <= $limits['length'] &&
                $packedDimensions['width'] <= $limits['width'] &&
                $packedDimensions['height'] <= $limits['height'] &&
                $packedDimensions['weight'] <= $limits['weight']) {
                
                $inpostSize = $sizeName;
                break;
            }
        }

        // Check if oversized
        if ($packedDimensions['length'] > self::INPOST_SIZES['C']['length'] ||
            $packedDimensions['width'] > self::INPOST_SIZES['C']['width'] ||
            $packedDimensions['height'] > self::INPOST_SIZES['C']['height'] ||
            $packedDimensions['weight'] > self::INPOST_SIZES['C']['weight']) {
            $oversized = true;
        }

        return array_merge($packedDimensions, [
            'inpost_size' => $inpostSize,
            'oversized' => $oversized,
            'packed_products' => $packedProducts,
            'product_count' => count($packedProducts)
        ]);
    }

    /**
     * InPost-specific packing that respects size constraints
     */
    protected function packProductsForInPost($products)
    {
        if (empty($products)) {
            return ['length' => 20, 'width' => 15, 'height' => 10];
        }

        // Calculate bounding box for products
        $maxLength = 0;
        $maxWidth = 0;
        $totalHeight = 0;
        $totalVolume = 0;

        foreach ($products as $product) {
            $maxLength = max($maxLength, $product['length']);
            $maxWidth = max($maxWidth, $product['width']); 
            $totalHeight += $product['height'];
            $totalVolume += ($product['length'] * $product['width'] * $product['height']);
        }

        // Try different packing strategies and pick the best one that fits
        $strategies = [];

        // Strategy 1: Stack all products (height = sum of heights)
        $stackedHeight = $totalHeight;
        $strategies[] = [
            'length' => $maxLength,
            'width' => $maxWidth,
            'height' => $stackedHeight,
            'name' => 'stacked'
        ];

        // Strategy 2: Try to optimize by spreading in length direction
        if (count($products) > 1) {
            $totalLength = 0;
            foreach ($products as $product) {
                $totalLength += $product['length'];
            }
            $strategies[] = [
                'length' => $totalLength,
                'width' => $maxWidth,
                'height' => max(array_column($products, 'height')),
                'name' => 'length_spread'
            ];
        }

        // Strategy 3: Try to optimize by spreading in width direction  
        if (count($products) > 1) {
            $totalWidth = 0;
            foreach ($products as $product) {
                $totalWidth += $product['width'];
            }
            $strategies[] = [
                'length' => $maxLength,
                'width' => $totalWidth, 
                'height' => max(array_column($products, 'height')),
                'name' => 'width_spread'
            ];
        }

        // Strategy 4: Volume-based calculation with height constraint
        $maxPossibleHeight = self::INPOST_SIZES['C']['height'] - 2; // Leave 2cm for packaging
        $baseArea = $maxLength * $maxWidth;
        $volumeHeight = $totalVolume / $baseArea;
        
        if ($volumeHeight <= $maxPossibleHeight) {
            $strategies[] = [
                'length' => $maxLength,
                'width' => $maxWidth,
                'height' => $volumeHeight,
                'name' => 'volume_optimized'
            ];
        }

        // Add packaging and evaluate each strategy
        $bestStrategy = null;
        $bestScore = PHP_FLOAT_MAX;

        foreach ($strategies as $strategy) {
            // Add conservative packaging (1-3% + 1cm max)
            $withPackaging = [
                'length' => ceil($strategy['length'] * 1.02 + 1),
                'width' => ceil($strategy['width'] * 1.02 + 1), 
                'height' => ceil($strategy['height'] * 1.02 + 1),
            ];

            // Check if it fits in any InPost size
            $fits = false;
            $fitSize = null;
            foreach (self::INPOST_SIZES as $sizeName => $limits) {
                if ($withPackaging['length'] <= $limits['length'] &&
                    $withPackaging['width'] <= $limits['width'] &&
                    $withPackaging['height'] <= $limits['height']) {
                    $fits = true;
                    $fitSize = $sizeName;
                    break;
                }
            }

            if ($fits) {
                // Score based on volume efficiency (smaller is better)
                $volume = $withPackaging['length'] * $withPackaging['width'] * $withPackaging['height'];
                if ($volume < $bestScore) {
                    $bestScore = $volume;
                    $bestStrategy = $withPackaging;
                }
            }
        }

        // If no strategy fits, use the most conservative one and mark as oversized
        if (!$bestStrategy) {
            Log::warning('No packing strategy fits InPost limits, using conservative approach', [
                'products' => count($products),
                'strategies_tried' => array_column($strategies, 'name')
            ]);
            
            // Use minimum dimensions but constrained to InPost C size
            $bestStrategy = [
                'length' => min($maxLength + 2, self::INPOST_SIZES['C']['length']),
                'width' => min($maxWidth + 2, self::INPOST_SIZES['C']['width']),
                'height' => min($stackedHeight + 1, self::INPOST_SIZES['C']['height']),
            ];
        }

        return [
            'length' => max($bestStrategy['length'], 10), // Minimum 10cm
            'width' => max($bestStrategy['width'], 10),   // Minimum 10cm  
            'height' => max($bestStrategy['height'], 5),  // Minimum 5cm
        ];
    }

    /**
     * Calculate multiple parcels for courier delivery
     */
    protected function calculateMultiParcelCourier($products, $totalWeight)
    {
        // For courier, we're more flexible but still need to respect weight limits
        $parcels = [];
        $remainingProducts = $products;

        while (!empty($remainingProducts)) {
            $parcel = $this->packSingleCourierParcel($remainingProducts);
            $parcels[] = $parcel;
            
            // Remove packed products from remaining list
            foreach ($parcel['packed_products'] as $packedProduct) {
                $remainingProducts = array_filter($remainingProducts, function($product) use ($packedProduct) {
                    return $product['item_index'] !== $packedProduct['item_index'];
                });
                $remainingProducts = array_values($remainingProducts);
            }

            // Safety check
            if (count($parcels) > 10) {
                Log::error('Too many courier parcels needed, stopping calculation', [
                    'parcel_count' => count($parcels),
                    'remaining_products' => count($remainingProducts)
                ]);
                break;
            }
        }

        Log::info('Courier multi-parcel calculation completed', [
            'total_parcels' => count($parcels),
            'total_products' => count($products)
        ]);

        return $parcels;
    }

    /**
     * Pack products into a single courier parcel
     */
    protected function packSingleCourierParcel($products)
    {
        $packedProducts = [];
        $currentWeight = 0;

        foreach ($products as $product) {
            $productWeight = $product['weight'] + 0.2; // Add packaging per item

            // Check weight limit (leave 2kg margin for packaging)
            if (($currentWeight + $productWeight) <= 28) {
                $packedProducts[] = $product;
                $currentWeight += $productWeight;
            } else {
                // If no products packed yet, include this one anyway
                if (empty($packedProducts)) {
                    $packedProducts[] = $product;
                    $currentWeight += $productWeight;
                }
                break;
            }
        }

        $packedDimensions = $this->packProducts($packedProducts);
        
        // Add packaging material for courier
        $result = [
            'length' => ceil($packedDimensions['length'] * 1.1 + 3),
            'width' => ceil($packedDimensions['width'] * 1.1 + 3),
            'height' => ceil($packedDimensions['height'] * 1.1 + 3),
            'weight' => round($currentWeight + 0.5, 2),
            'packed_products' => $packedProducts,
            'product_count' => count($packedProducts)
        ];

        // Check if oversized
        if ($result['length'] > self::COURIER_MAX['length'] ||
            $result['width'] > self::COURIER_MAX['width'] ||
            $result['height'] > self::COURIER_MAX['height'] ||
            $result['weight'] > self::COURIER_MAX['weight']) {
            $result['oversized'] = true;
        }

        return $result;
    }

    /**
     * Simple box packing algorithm (for courier deliveries)
     */
    protected function packProducts($products)
    {
        if (empty($products)) {
            return ['length' => 20, 'width' => 15, 'height' => 10, 'weight' => 0.5];
        }

        // Calculate bounding box for products
        $maxLength = 0;
        $maxWidth = 0;
        $totalHeight = 0;
        $totalVolume = 0;

        foreach ($products as $product) {
            $maxLength = max($maxLength, $product['length']);
            $maxWidth = max($maxWidth, $product['width']);
            $totalHeight += $product['height'];
            $totalVolume += ($product['length'] * $product['width'] * $product['height']);
        }

        // For courier delivery, we're more flexible with dimensions
        // Try to pack efficiently but don't worry about strict size constraints
        
        // Strategy 1: Simple stacking
        $stackedHeight = $totalHeight;
        
        // Strategy 2: Try to distribute products to reduce height
        $optimizedHeight = $stackedHeight;
        if (count($products) > 1) {
            // Try to calculate a more reasonable height based on volume distribution
            $baseArea = $maxLength * $maxWidth;
            if ($baseArea > 0) {
                $volumeHeight = $totalVolume / $baseArea;
                // Use a compromise between stacked and volume-optimized height
                $optimizedHeight = min($stackedHeight, $volumeHeight * 1.3);
            }
        }

        return [
            'length' => max($maxLength, 10), // Minimum 10cm
            'width' => max($maxWidth, 10),   // Minimum 10cm
            'height' => max($optimizedHeight, 5), // Minimum 5cm
        ];
    }

    /**
     * Get default dimensions based on delivery method
     */
    protected function getDefaultDimensions($isInPostPaczkomat)
    {
        if ($isInPostPaczkomat) {
            return array_merge(self::INPOST_SIZES['A'], ['inpost_size' => 'A']);
        }
        
        return ['length' => 30, 'width' => 20, 'height' => 15, 'weight' => 1];
    }

    /**
     * Convert dimensions from cm to mm for InPost API
     */
    public function convertToMillimeters($dimensions)
    {
        return [
            'length' => round($dimensions['length'] * 10), // cm to mm
            'width' => round($dimensions['width'] * 10),
            'height' => round($dimensions['height'] * 10),
            'weight' => round($dimensions['weight'], 2), // weight stays in kg
        ];
    }
} 