<?php

namespace App\Services\App;

use App\Models\DeliveryAddress;
use App\Models\DeliveryMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DeliveryAddressService
{
    /**
     * Handle delivery address processing
     * 
     * @param Request $request
     * @return DeliveryAddress
     */
    public function handleDeliveryAddress(Request $request)
    {
        if ($request->input('delivery_address_choice') && $request->input('delivery_address_choice') !== 'new') {
            // Use existing address
            return DeliveryAddress::findOrFail($request->input('delivery_address_choice'));
        }

        // Validate delivery method
        $deliveryMethod = DeliveryMethod::findOrFail($request->input('delivery_method_id'));

        // Create new address
        $addressData = [
            'user_id' => Auth::id(), // Will be null for guests
            'delivery_method_id' => $deliveryMethod->id,
            'name' => $request->input('new_address.name'),
            'street' => $request->input('new_address.street'),
            'building_number' => $request->input('new_address.building_number'),
            'apartment_number' => $request->input('new_address.apartment_number'),
            'post_code' => $request->input('new_address.post_code'),
            'city' => $request->input('new_address.city'),
            'phone' => $request->input('phone'),
            'is_default' => $request->input('make_default', false),
        ];

        if ($deliveryMethod->type === 'point') {
            $addressData = array_merge($addressData, [
                'type' => 'point',
                'point_id' => $request->input('point_id'),
                'point_address' => $request->input('point_address'),
                'point_data' => $request->input('point_data', []),
            ]);
        } else {
            $addressData['type'] = 'address';
        }

        // Create the address
        $address = DeliveryAddress::create($addressData);

        // If user is logged in and wants to save the address
        if (Auth::check() && $request->input('save_address')) {
            // If this is set as default, remove default from other addresses
            if ($request->input('make_default')) {
                DeliveryAddress::where('user_id', Auth::id())
                    ->where('id', '!=', $address->id)
                    ->update(['is_default' => false]);
            }
        } else {
            // For guests or when not saving, don't associate with user
            $address->user_id = null;
            $address->save();
        }

        return $address;
    }

    /**
     * Get user's saved delivery addresses
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserAddresses()
    {
        if (Auth::check()) {
            return Auth::user()->deliveryAddresses()->orderBy('is_default', 'desc')->get();
        }
        
        return collect();
    }

    /**
     * Get default delivery address for user
     * 
     * @return DeliveryAddress|null
     */
    public function getDefaultAddress()
    {
        if (Auth::check()) {
            return Auth::user()->deliveryAddresses()->where('is_default', true)->first();
        }
        
        return null;
    }
} 