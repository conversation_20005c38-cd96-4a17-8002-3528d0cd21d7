<?php

namespace App\Services\App;

use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ProductRecommendationService
{
    protected $cachePrefix;
    protected $cacheTtl;
    protected $config;

    public function __construct()
    {
        $this->config = config('recommendations');
        $this->cachePrefix = $this->config['cache']['prefix'];
        $this->cacheTtl = $this->config['cache']['ttl'];
    }

    /**
     * Get product recommendations for cart upsells
     *
     * @param Cart $cart
     * @param int $limit
     * @return Collection
     */
    public function getCartUpsells(Cart $cart, int $limit = null): Collection
    {
        $limit = $limit ?? $this->config['limits']['cart_upsells'];

        if (!$this->config['cache']['enabled']) {
            return $this->generateCartUpsells($cart, $limit);
        }

        $cacheKey = $this->getCacheKey('cart_upsells', $cart->id, $limit);

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($cart, $limit) {
            return $this->generateCartUpsells($cart, $limit);
        });
    }

    /**
     * Generate cart upsells without caching
     *
     * @param Cart $cart
     * @param int $limit
     * @return Collection
     */
    protected function generateCartUpsells(Cart $cart, int $limit): Collection
    {
        $recommendations = collect();

        // Get cart items with products and ensure comprehensive exclusion
        $cartItems = $cart->items()->with(['product.category'])->get();
        $cartProductIds = $this->getComprehensiveCartExclusions($cart);

        if ($cartItems->isEmpty()) {
            return $this->getFallbackRecommendations($limit, $cartProductIds);
        }

        // Strategy 1: Cross-sell related products (hierarchical categories)
        if ($this->config['strategies']['cross_sell']['enabled']) {
            $crossSellProducts = $this->getCrossSellProducts($cartItems, $cartProductIds, $limit);
            $recommendations = $recommendations->merge($crossSellProducts);
        }

        // Strategy 2: User purchase history based recommendations
        if ($this->config['strategies']['user_history']['enabled'] && Auth::check()) {
            $historyProducts = $this->getUserHistoryRecommendations(Auth::user(), $cartProductIds, $limit);
            $recommendations = $recommendations->merge($historyProducts);
        }

        // Strategy 3: Popular products in same categories
        if ($this->config['strategies']['popular_products']['enabled']) {
            $popularProducts = $this->getPopularProductsInCategories($cartItems, $cartProductIds, $limit);
            $recommendations = $recommendations->merge($popularProducts);
        }

        // Strategy 4: Frequently bought together
        if ($this->config['strategies']['frequently_bought_together']['enabled']) {
            $frequentlyBoughtTogether = $this->getFrequentlyBoughtTogether($cartProductIds, $limit);
            $recommendations = $recommendations->merge($frequentlyBoughtTogether);
        }

        // Remove duplicates and limit results
        $recommendations = $recommendations->unique('id')->take($limit);

        // If we don't have enough recommendations, fill with fallback
        if ($recommendations->count() < $limit) {
            $fallback = $this->getFallbackRecommendations($limit - $recommendations->count(), $recommendations->pluck('id')->toArray());
            $recommendations = $recommendations->merge($fallback);
        }

        return $recommendations->take($limit);
    }

    /**
     * Get cross-sell products from same categories as cart items with hierarchical expansion
     *
     * @param Collection $cartItems
     * @param array $excludeProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getCrossSellProducts(Collection $cartItems, array $excludeProductIds, int $limit): Collection
    {
        $categoryIds = $cartItems->pluck('product.category_id')->unique()->filter();

        if ($categoryIds->isEmpty()) {
            return collect();
        }

        // Get hierarchical category IDs with weights (current + parent categories)
        $categoryWeights = $this->getWeightedHierarchicalCategories($categoryIds);

        if ($categoryWeights->isEmpty()) {
            return collect();
        }

        // Build query with category preference weighting
        $query = Product::inStock()
            ->whereIn('products.category_id', $categoryWeights->keys())
            ->whereNotIn('products.id', $excludeProductIds)
            ->with(['category', 'media']);

        // Add weighted ordering - prefer exact category matches, then parent categories
        $caseStatement = 'CASE products.category_id ';
        foreach ($categoryWeights as $categoryId => $weight) {
            $caseStatement .= "WHEN {$categoryId} THEN {$weight} ";
        }
        $caseStatement .= 'ELSE 0 END';

        return $query->select('products.*', DB::raw("({$caseStatement}) as category_weight"))
            ->orderBy('category_weight', 'desc')
            ->orderBy('products.stock', 'desc')
            ->orderBy('products.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get hierarchical category IDs including parent categories
     *
     * @param Collection $categoryIds
     * @return Collection
     */
    protected function getHierarchicalCategoryIds(Collection $categoryIds): Collection
    {
        $allCategoryIds = collect($categoryIds);

        // Load categories with their parent relationships
        $categories = Category::whereIn('id', $categoryIds)
            ->with('parent')
            ->get();

        foreach ($categories as $category) {
            // Add parent category IDs
            $ancestorIds = $category->getAncestorIds();
            $allCategoryIds = $allCategoryIds->merge($ancestorIds);

            // Also add sibling categories (same parent)
            if ($category->parent_id) {
                $siblingIds = Category::where('parent_id', $category->parent_id)
                    ->where('is_visible', true)
                    ->pluck('id');
                $allCategoryIds = $allCategoryIds->merge($siblingIds);
            }
        }

        return $allCategoryIds->unique()->filter();
    }

    /**
     * Get hierarchical category IDs with weights for better recommendation ordering
     *
     * @param Collection $categoryIds
     * @return Collection
     */
    protected function getWeightedHierarchicalCategories(Collection $categoryIds): Collection
    {
        $categoryWeights = collect();

        // Load categories with their parent relationships
        $categories = Category::whereIn('id', $categoryIds)
            ->with('parent')
            ->get();

        foreach ($categories as $category) {
            // Exact category match gets highest weight
            $categoryWeights->put($category->id, 100);

            // Parent categories get decreasing weights
            $currentCategory = $category;
            $weight = 80;

            while ($currentCategory->parent && $weight > 20) {
                $currentCategory = $currentCategory->parent;
                if (!$categoryWeights->has($currentCategory->id)) {
                    $categoryWeights->put($currentCategory->id, $weight);
                }
                $weight -= 20;
            }

            // Sibling categories get medium weight
            if ($category->parent_id) {
                $siblings = Category::where('parent_id', $category->parent_id)
                    ->where('is_visible', true)
                    ->where('id', '!=', $category->id)
                    ->pluck('id');

                foreach ($siblings as $siblingId) {
                    if (!$categoryWeights->has($siblingId)) {
                        $categoryWeights->put($siblingId, 60);
                    }
                }
            }
        }

        return $categoryWeights;
    }

    /**
     * Get comprehensive list of product IDs to exclude from recommendations
     * This includes current cart items and any persistent cart data
     *
     * @param Cart $cart
     * @return array
     */
    protected function getComprehensiveCartExclusions(Cart $cart): array
    {
        $excludeIds = collect();

        // Add current cart items
        $currentCartItems = $cart->items()->pluck('product_id');
        $excludeIds = $excludeIds->merge($currentCartItems);

        // If user is logged in, also exclude items from any other active carts
        if (Auth::check()) {
            $userCartItems = Cart::where('user_id', Auth::id())
                ->where('status', 'active')
                ->with('items')
                ->get()
                ->pluck('items')
                ->flatten()
                ->pluck('product_id');

            $excludeIds = $excludeIds->merge($userCartItems);
        }

        return $excludeIds->unique()->filter()->toArray();
    }

    /**
     * Get recommendations based on user's purchase history with hierarchical categories
     *
     * @param User $user
     * @param array $excludeProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getUserHistoryRecommendations(User $user, array $excludeProductIds, int $limit): Collection
    {
        // Get categories from user's previous orders
        $purchasedCategoryIds = OrderItem::whereHas('order', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->where('status', 'completed');
        })
        ->with('product.category')
        ->get()
        ->pluck('product.category_id')
        ->unique()
        ->filter();

        if ($purchasedCategoryIds->isEmpty()) {
            return collect();
        }

        // Expand to include hierarchical categories
        $hierarchicalCategoryIds = $this->getHierarchicalCategoryIds($purchasedCategoryIds);

        // Also exclude products the user has already purchased recently
        $recentlyPurchasedIds = OrderItem::whereHas('order', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->where('status', 'completed')
                  ->where('created_at', '>=', now()->subDays($this->config['strategies']['user_history']['max_days_back'] ?? 365));
        })
        ->pluck('product_id')
        ->toArray();

        $allExcludeIds = array_merge($excludeProductIds, $recentlyPurchasedIds);

        return Product::inStock()
            ->whereIn('products.category_id', $hierarchicalCategoryIds)
            ->whereNotIn('products.id', $allExcludeIds)
            ->with(['category', 'media'])
            ->orderBy('products.stock', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get popular products in the same categories as cart items
     *
     * @param Collection $cartItems
     * @param array $excludeProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getPopularProductsInCategories(Collection $cartItems, array $excludeProductIds, int $limit): Collection
    {
        $categoryIds = $cartItems->pluck('product.category_id')->unique()->filter();
        
        if ($categoryIds->isEmpty()) {
            return collect();
        }
        
        // Get products ordered by how many times they've been ordered
        return Product::inStock()
            ->whereIn('products.category_id', $categoryIds)
            ->whereNotIn('products.id', $excludeProductIds)
            ->with(['category', 'media'])
            ->leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->where(function ($query) {
                $query->whereNull('orders.status')
                      ->orWhere('orders.status', 'completed');
            })
            ->select('products.*', DB::raw('COUNT(order_items.id) as order_count'))
            ->groupBy('products.id')
            ->orderBy('order_count', 'desc')
            ->orderBy('products.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get products frequently bought together with cart items
     *
     * @param array $cartProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getFrequentlyBoughtTogether(array $cartProductIds, int $limit): Collection
    {
        if (empty($cartProductIds)) {
            return collect();
        }
        
        // Find orders that contain any of the cart products
        $relatedOrderIds = OrderItem::whereIn('product_id', $cartProductIds)
            ->whereHas('order', function ($query) {
                $query->where('status', 'completed');
            })
            ->pluck('order_id')
            ->unique();
        
        if ($relatedOrderIds->isEmpty()) {
            return collect();
        }
        
        // Find other products in those orders
        return Product::inStock()
            ->whereNotIn('products.id', $cartProductIds)
            ->whereHas('orderItems', function ($query) use ($relatedOrderIds) {
                $query->whereIn('order_id', $relatedOrderIds);
            })
            ->with(['category', 'media'])
            ->withCount(['orderItems' => function ($query) use ($relatedOrderIds) {
                $query->whereIn('order_id', $relatedOrderIds);
            }])
            ->orderBy('order_items_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get fallback recommendations when other strategies don't provide enough results
     *
     * @param int $limit
     * @param array $excludeProductIds
     * @return Collection
     */
    protected function getFallbackRecommendations(int $limit, array $excludeProductIds = []): Collection
    {
        $strategy = $this->config['fallback']['strategy'] ?? 'newest';

        $query = Product::inStock()
            ->whereNotIn('products.id', $excludeProductIds)
            ->with(['category', 'media']);

        // Apply fallback strategy
        switch ($strategy) {
            case 'popular':
                $query->leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
                      ->select('products.*', DB::raw('COUNT(order_items.id) as order_count'))
                      ->groupBy('products.id')
                      ->orderBy('order_count', 'desc');
                break;
            case 'random':
                $query->inRandomOrder();
                break;
            case 'newest':
            default:
                $query->orderBy('products.created_at', 'desc');
                break;
        }

        return $query->limit($limit)->get();
    }

    /**
     * Generate cache key for recommendations
     *
     * @param string $type
     * @param mixed $identifier
     * @param int $limit
     * @return string
     */
    protected function getCacheKey(string $type, $identifier, int $limit): string
    {
        $userId = Auth::id() ?? 'guest';
        return "{$this->cachePrefix}:{$type}:{$identifier}:{$userId}:{$limit}";
    }

    /**
     * Clear recommendation cache
     *
     * @param string|null $type
     * @return void
     */
    public function clearCache(string $type = null): void
    {
        if ($type) {
            Cache::forget($this->getCacheKey($type, '*', '*'));
        } else {
            // Clear all recommendation caches (this is a simplified approach)
            // In production, you might want to use cache tags for better cache management
            Cache::flush();
        }
    }
}
