<?php

namespace App\Services\App;

use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\InPostService;
use App\Services\MolosOrderService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\DeliveryAddress;

class OrderService
{
    protected $molosOrderService;
    protected $inPostService;

    public function __construct(
        MolosOrderService $molosOrderService,
        InPostService $inPostService
    ) {
        $this->molosOrderService = $molosOrderService;
        $this->inPostService = $inPostService;
    }

    public function createOrder(Cart $cart, array $orderData, DeliveryAddress $deliveryAddress)
    {
        try {
            DB::beginTransaction();

            // Create local order
            $order = $this->createLocalOrder($cart, $orderData, $deliveryAddress);

            // Create order items
            $this->createOrderItems($order, $cart);

            // Mark cart as completed
            // $cart->update(['status' => 'completed']);

            DB::commit();

            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create order', [
                'error' => $e->getMessage(),
                'cart_id' => $cart->id,
                'order_data' => $orderData
            ]);
            throw $e;
        }
    }

    public function completeOrder(Order $order)
    {
        try {
            DB::beginTransaction();

            // Send to Molos
            $molosResponse = $this->molosOrderService->createOrder($order);

            // Update order with Molos reference
            $order->update([
                'molos_order_id' => $molosResponse['id'] ?? null,
                'molos_status' => $molosResponse['status'] ?? 'pending',
                'payment_status' => 'completed'
            ]);

            // Generate InPost shipping label if needed
            if ($order->deliveryAddress && 
                $order->deliveryAddress->deliveryMethod && 
                $order->deliveryAddress->deliveryMethod->api_provider === 'inpost') {
                try {
                    $this->inPostService->generateLabel($order);
                } catch (\Exception $e) {
                    // Log error but don't fail the order
                    Log::error('Failed to generate InPost label', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            DB::commit();

            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to complete order', [
                'error' => $e->getMessage(),
                'order_id' => $order->id
            ]);
            throw $e;
        }
    }

    protected function createLocalOrder(Cart $cart, array $orderData, DeliveryAddress $deliveryAddress)
    {
        $deliveryMethod = $deliveryAddress->deliveryMethod;

        // Prepare delivery_address array for backward compatibility
        $deliveryAddressArray = $this->prepareDeliveryAddressArray($deliveryAddress, $orderData);

        // Calculate totals including delivery
        $subtotal = $this->calculateSubtotal($cart);
        $deliveryCost = $this->calculateDeliveryCost($deliveryMethod, $subtotal);
        $total = $subtotal + $deliveryCost;

        // Create the order data array with all required fields
        $orderCreateData = [
            'user_id' => $orderData['user_id'] ?? null,
            'delivery_address_id' => $deliveryAddress->id, // New relationship
            'delivery_method' => $deliveryMethod->name, // Old field - method name
            'delivery_address' => $deliveryAddressArray, // Old field - address array
            'first_name' => $orderData['first_name'],
            'last_name' => $orderData['last_name'],
            'email' => $orderData['email'],
            'phone' => $orderData['phone'],
            'status' => 'pending',
            'payment_method' => $orderData['payment_method'],
            'payment_status' => 'pending',
            'subtotal' => $subtotal,
            'delivery_cost' => $deliveryCost,
            'total' => $total,
            'notes' => $orderData['notes'] ?? null,
        ];

        // Only add billing_address if it exists in orderData
        if (isset($orderData['billing_address'])) {
            $orderCreateData['billing_address'] = $orderData['billing_address'];
        }

        return Order::create($orderCreateData);
    }

    /**
     * Calculate subtotal (cart items only)
     */
    protected function calculateSubtotal(Cart $cart)
    {
        return $cart->items->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });
    }

    /**
     * Calculate delivery cost based on delivery method and cart amount
     */
    protected function calculateDeliveryCost($deliveryMethod, $cartAmount)
    {
        // If delivery method doesn't have a price, it's free
        if (!$deliveryMethod->price || $deliveryMethod->price <= 0) {
            return 0;
        }

        // If cart amount meets minimum for free shipping
        if ($deliveryMethod->min_cart_amount && $cartAmount >= $deliveryMethod->min_cart_amount) {
            return 0;
        }

        // Otherwise, charge the delivery price
        return $deliveryMethod->price;
    }

    /**
     * Calculate total (subtotal + delivery) - kept for backward compatibility
     */
    protected function calculateTotal(Cart $cart)
    {
        return $this->calculateSubtotal($cart);
    }

    protected function prepareDeliveryAddressArray(DeliveryAddress $deliveryAddress, array $orderData)
    {
        $deliveryMethod = $deliveryAddress->deliveryMethod;

        if ($deliveryMethod->type === 'point') {
            // For paczkomat/point delivery
            return [
                'type' => 'point',
                'method_name' => $deliveryMethod->name,
                'point_id' => $deliveryAddress->point_id,
                'point_name' => $orderData['point_name'] ?? null,
                'point_address' => $deliveryAddress->point_address,
                'point_data' => $deliveryAddress->point_data,
                // Keep some basic address info for fallback
                'city' => $deliveryAddress->city,
                'post_code' => $deliveryAddress->post_code,
            ];
        } else {
            // For courier delivery
            return [
                'type' => 'address',
                'method_name' => $deliveryMethod->name,
                'name' => $deliveryAddress->name,
                'street' => $deliveryAddress->street,
                'building_number' => $deliveryAddress->building_number,
                'apartment_number' => $deliveryAddress->apartment_number,
                'post_code' => $deliveryAddress->post_code,
                'city' => $deliveryAddress->city,
                'phone' => $deliveryAddress->phone,
            ];
        }
    }

    protected function createOrderItems(Order $order, Cart $cart)
    {
        foreach ($cart->items as $item) {
            $order->items()->create([
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->product->price,
                'name' => $item->product->name
            ]);
        }
    }

    protected function prepareDeliveryAddress(array $orderData)
    {
        // If using a saved delivery address
        if (isset($orderData['delivery_address_choice']) && $orderData['delivery_address_choice'] !== 'new' && auth()->check()) {
            $savedAddress = auth()->user()->deliveryAddresses()
                ->findOrFail($orderData['delivery_address_choice']);
            
            return $savedAddress;
        }


        // Get the delivery method to determine type
        $deliveryMethodId = $orderData['delivery_method_id'] ?? null;
        if (!$deliveryMethodId) {
            throw new \Exception('Nie wybrano metody dostawy');
        }

        $deliveryMethod = \App\Models\DeliveryMethod::findOrFail($deliveryMethodId);

        // Prepare delivery address data
        $addressData = [
            'user_id' => auth()->id(),
            'delivery_method_id' => $deliveryMethodId,
            'name' => $orderData['new_address']['name'] ?? "{$orderData['first_name']} {$orderData['last_name']}",
        ];

        // For point delivery (paczkomat)
        if ($deliveryMethod->type === 'point') {
            // Check if point data exists
            if (empty($orderData['point_id'])) {
                throw new \Exception('Nie wybrano paczkomatu');
            }

            $addressData = array_merge($addressData, [
                'type' => 'point',
                'point_id' => $orderData['point_id'],
                'point_address' => $orderData['point_address'] ?? null,
                'point_data' => [
                    'point_name' => $orderData['point_name'] ?? null,
                    'point_id' => $orderData['point_id'],
                    'point_address' => $orderData['point_address'] ?? null,
                ],
                // For point delivery, we still need some address fields for fallback
                'city' => $this->extractCityFromPointAddress($orderData['point_address'] ?? ''),
                'post_code' => $this->extractPostCodeFromPointAddress($orderData['point_address'] ?? ''),
            ]);
        } else {
            // For courier delivery
            if (!isset($orderData['new_address'])) {
                throw new \Exception('Nie podano adresu dostawy');
            }

            $newAddress = $orderData['new_address'];
            $addressData = array_merge($addressData, [
                'type' => 'address',
                'street' => $newAddress['street'],
                'building_number' => $newAddress['building_number'],
                'apartment_number' => $newAddress['apartment_number'] ?? null,
                'post_code' => $newAddress['post_code'],
                'city' => $newAddress['city'],
                'phone' => $newAddress['phone'] ?? $orderData['phone'],
            ]);
        }

        // Create and return the delivery address
        $deliveryAddress = \App\Models\DeliveryAddress::create($addressData);

        // Set as default if requested and user is authenticated
        if (auth()->check() && ($orderData['make_default'] ?? false)) {
            auth()->user()->deliveryAddresses()
                ->where('id', '!=', $deliveryAddress->id)
                ->update(['is_default' => false]);
            
            $deliveryAddress->update(['is_default' => true]);
        }

        return $deliveryAddress;
    }

    // Helper methods to extract location data from point address
    protected function extractCityFromPointAddress($pointAddress)
    {
        // Try to extract city from point address string
        // Example: "Warszawa, ul. Marszałkowska 1" -> "Warszawa"
        if (preg_match('/^([^,]+)/', $pointAddress, $matches)) {
            return trim($matches[1]);
        }
        return 'Unknown';
    }

    protected function extractPostCodeFromPointAddress($pointAddress)
    {
        // Try to extract postal code from point address
        // Example: "00-001 Warszawa, ul. Marszałkowska 1" -> "00-001"
        if (preg_match('/(\d{2}-\d{3})/', $pointAddress, $matches)) {
            return $matches[1];
        }
        return '00-000';
    }

    public function regenerateShippingLabel(Order $order)
    {
        if (!$order->deliveryAddress || 
            !$order->deliveryAddress->deliveryMethod || 
            $order->deliveryAddress->deliveryMethod->api_provider !== 'inpost') {
            throw new \Exception('This order does not use InPost shipping');
        }

        $result = $this->inPostService->generateLabel($order);
        
        // Log the result for debugging
        Log::info('Regenerated InPost shipping label', [
            'order_id' => $order->id,
            'result' => $result,
            'tracking_number' => $order->inpost_tracking_number
        ]);
        
        return $result;
    }

    public function getShipmentStatus(Order $order)
    {
        if (!$order->inpost_shipment_id) {
            throw new \Exception('Order does not have an InPost shipment');
        }

        return $this->inPostService->getShipmentStatus($order->inpost_shipment_id);
    }

    public function cancelShipment(Order $order)
    {
        if (!$order->inpost_shipment_id) {
            throw new \Exception('Order does not have an InPost shipment');
        }

        return $this->inPostService->cancelShipment($order->inpost_shipment_id);
    }
}
