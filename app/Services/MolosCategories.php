<?php

namespace App\Services;

use App\Models\Category;
use App\Models\MolosCategory;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class MolosCategories extends MolosApiService
{
    public function importCategories()
    {
        $page = 1;
        $limit = 1000;
        $allCategories = [];

        do {
            $response = $this->getHttpClient()->get($this->apiUrl . '/products/categories', [
                'page' => $page,
                'limit' => $limit,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $categories = $result['categories'];
                $allCategories = array_merge($allCategories, $categories);
                $page++;
                $next = ($page * $limit) < $result['total'];
            } else {
                Log::error($this->apiUrl . '/products/categories');
                Log::error(serialize($response->json()));
                Log::error('Failed to fetch categories from Molos API', ['page' => $page, 'limit' => $limit]);
                $next = false;
            }
        } while ($next);

        foreach ($allCategories as $categoryData) {
            $this->importOneCategory($categoryData);
            $this->importOneMolosCategory($categoryData);
        }
    }

    protected function importOneCategory($categoryData)
    {
        $parentCategory = null;
        if (!empty($categoryData['parent_id'])) {
            $parentCategory = Category::where('molos_id', $categoryData['parent_id'])->first();
        }

        Category::updateOrCreate(
            ['molos_id' => $categoryData['id']],
            [
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'parent_id' => $parentCategory ? $parentCategory->id : null,
                'path' => $categoryData['path'],
            ]
        );
    }

    protected function importOneMolosCategory($categoryData)
    {
        MolosCategory::updateOrCreate(
            ['molos_id' => $categoryData['id']],
            [
                'name' => $categoryData['name'],
                'path' => $categoryData['path'],
                'parent_molos_id' => $categoryData['parent_id'] ?? null,
            ]
        );
    }

    public function handleCategory($molosId)
    {
        return Category::where('molos_id', $molosId)->first() ?? $this->getUncategorizedCategory();
    }

    public function handleMolosCategory($molosId)
    {
        return MolosCategory::where('molos_id', $molosId)->first();
    }

    protected function getUncategorizedCategory()
    {
        return Category::firstOrCreate(
            ['slug' => 'uncategorized'],
            [
                'name' => 'Uncategorized',
                'slug' => 'uncategorized',
                'is_visible' => false,
            ]
        );
    }
}
