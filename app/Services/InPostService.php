<?php

namespace App\Services;

use App\Models\Order;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class InPostService
{
    protected $client;
    protected $baseUrl;
    protected $organizationId;

    public function __construct()
    {
        $this->baseUrl = config('services.inpost.environment') === 'sandbox' 
            ? 'https://sandbox-api-shipx-pl.easypack24.net'
            : 'https://api-shipx-pl.easypack24.net';
            
        $this->organizationId = config('services.inpost.organization_id');
        
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'headers' => [
                'Authorization' => 'Bearer ' . config('services.inpost.token'),
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    public function generateLabel(Order $order)
    {
        try {
            $shipmentData = $this->prepareShipmentData($order);
            
            // Check if we have multiple parcels
            if (isset($shipmentData['multiple_parcels']) && $shipmentData['multiple_parcels']) {
                return $this->generateMultipleLabels($order, $shipmentData['parcels']);
            }
            
            // Single parcel shipment
            return $this->generateSingleLabel($order, $shipmentData);
            
        } catch (\Exception $e) {
            // Log detailed error information
            $errorDetails = [
                'order_id' => $order->id,
                'error_message' => $e->getMessage(),
                'error_class' => get_class($e)
            ];
            
            // If it's a Guzzle HTTP exception, get the response body
            if (method_exists($e, 'getResponse') && $e->getResponse()) {
                $responseBody = $e->getResponse()->getBody()->getContents();
                $errorDetails['api_response'] = $responseBody;
                $errorDetails['status_code'] = $e->getResponse()->getStatusCode();
                
                // Try to decode JSON response
                $decodedResponse = json_decode($responseBody, true);
                if ($decodedResponse) {
                    $errorDetails['decoded_response'] = $decodedResponse;
                }
            }
            
            Log::error('InPost label generation failed', $errorDetails);
            
            throw $e;
        }
    }

    protected function generateSingleLabel(Order $order, $shipmentData)
    {
        // Log the data being sent for debugging
        Log::info('InPost API Request Data (Single Parcel)', [
            'order_id' => $order->id,
            'shipment_data' => $shipmentData
        ]);
        
        $response = $this->client->post('/v1/organizations/' . $this->organizationId . '/shipments', [
            'json' => $shipmentData
        ]);

        $shipment = json_decode($response->getBody(), true);

        // Extract tracking number
        $trackingNumber = $shipment['tracking_number'] ?? null;

        // Update order with shipment details
        $order->update([
            'inpost_shipment_id' => $shipment['id'],
            'inpost_status' => $shipment['status'],
            'inpost_tracking_number' => $trackingNumber
        ]);

        return $shipment;
    }

    protected function generateMultipleLabels(Order $order, $parcels)
    {
        // Determine if this is a courier delivery (supports single shipment with multiple parcels)
        // or parcel locker delivery (requires separate shipments)
        if ($this->isCourierDelivery($order)) {
            return $this->generateSingleShipmentMultipleParcels($order, $parcels);
        } else {
            return $this->generateMultipleSeparateShipments($order, $parcels);
        }
    }

    /**
     * Determine if the order is for courier delivery (vs parcel locker)
     */
    protected function isCourierDelivery(Order $order)
    {
        // Check if delivery method type is 'point' (paczkomat) or 'courier'
        if ($order->deliveryAddress &&
            $order->deliveryAddress->deliveryMethod &&
            $order->deliveryAddress->deliveryMethod->type === 'point') {
            return false; // This is parcel locker delivery
        }

        return true; // This is courier delivery
    }

    /**
     * Generate multiple separate shipments (for parcel locker deliveries)
     * This is the original implementation
     */
    protected function generateMultipleSeparateShipments(Order $order, $parcels)
    {
        $shipments = [];
        $shipmentIds = [];
        $trackingNumbers = [];
        $baseReference = $order->uuid;

        Log::info('Generating multiple separate InPost shipments (parcel locker)', [
            'order_id' => $order->id,
            'parcel_count' => count($parcels)
        ]);

        foreach ($parcels as $index => $parcel) {
            try {
                $parcelShipmentData = $this->prepareSingleParcelData($order, $parcel, $index + 1, count($parcels));

                Log::info('InPost API Request Data (Multi-Parcel Separate)', [
                    'order_id' => $order->id,
                    'parcel_index' => $index + 1,
                    'shipment_data' => $parcelShipmentData
                ]);

                $response = $this->client->post('/v1/organizations/' . $this->organizationId . '/shipments', [
                    'json' => $parcelShipmentData
                ]);

                $shipment = json_decode($response->getBody(), true);
                $shipments[] = $shipment;
                $shipmentIds[] = $shipment['id'];

                // Extract tracking number
                if (isset($shipment['tracking_number'])) {
                    $trackingNumbers[] = $shipment['tracking_number'];
                }

                // Small delay between requests to avoid rate limiting
                if ($index < count($parcels) - 1) {
                    usleep(500000); // 0.5 second delay
                }

            } catch (\Exception $e) {
                Log::error('Failed to create shipment for parcel', [
                    'order_id' => $order->id,
                    'parcel_index' => $index + 1,
                    'error' => $e->getMessage()
                ]);

                // If we fail on subsequent parcels, we still want to save what we have
                if (empty($shipments)) {
                    throw $e; // Re-throw if we couldn't create any shipments
                }
                break; // Stop creating more shipments, but save what we have
            }
        }

        // Store tracking numbers as comma-separated string
        $trackingNumbersString = !empty($trackingNumbers) ? implode(',', $trackingNumbers) : null;

        // Update order with multiple shipment IDs and tracking numbers
        $order->update([
            'inpost_shipment_id' => implode(',', $shipmentIds), // Store comma-separated IDs
            'inpost_status' => $shipments[0]['status'] ?? 'created',
            'inpost_tracking_number' => $trackingNumbersString,
            'notes' => ($order->notes ?? '') . "\nMultiple parcels: " . count($shipments) . " separate shipments created."
        ]);

        return [
            'multiple_parcels' => true,
            'shipments' => $shipments,
            'total_parcels' => count($shipments),
            'primary_shipment_id' => $shipments[0]['id'] ?? null,
            'tracking_numbers' => $trackingNumbers
        ];
    }

    /**
     * Generate a single shipment with multiple parcels (for courier deliveries)
     * This is the optimized approach for courier services
     */
    protected function generateSingleShipmentMultipleParcels(Order $order, $parcels)
    {
        Log::info('Generating single InPost shipment with multiple parcels (courier)', [
            'order_id' => $order->id,
            'parcel_count' => count($parcels)
        ]);

        // Prepare shipment data with multiple parcels
        $shipmentData = $this->prepareMultiParcelShipmentData($order, $parcels);

        Log::info('InPost API Request Data (Single Multi-Parcel)', [
            'order_id' => $order->id,
            'parcel_count' => count($parcels),
            'shipment_data' => $shipmentData
        ]);

        try {
            $response = $this->client->post('/v1/organizations/' . $this->organizationId . '/shipments', [
                'json' => $shipmentData
            ]);

            $shipment = json_decode($response->getBody(), true);

            // Extract tracking numbers from parcels
            $trackingNumbers = [];
            if (isset($shipment['parcels']) && is_array($shipment['parcels'])) {
                foreach ($shipment['parcels'] as $parcel) {
                    if (isset($parcel['tracking_number'])) {
                        $trackingNumbers[] = $parcel['tracking_number'];
                    }
                }
            }

            // If no parcel-level tracking numbers, use the main tracking number
            if (empty($trackingNumbers) && isset($shipment['tracking_number'])) {
                $trackingNumbers[] = $shipment['tracking_number'];
            }

            $trackingNumbersString = !empty($trackingNumbers) ? implode(',', $trackingNumbers) : null;

            // Update order with single shipment ID but multiple tracking numbers
            $order->update([
                'inpost_shipment_id' => $shipment['id'],
                'inpost_status' => $shipment['status'],
                'inpost_tracking_number' => $trackingNumbersString,
                'notes' => ($order->notes ?? '') . "\nMultiple parcels: 1 shipment with " . count($parcels) . " parcels created."
            ]);

            return [
                'multiple_parcels' => true,
                'shipments' => [$shipment], // Single shipment in array for consistency
                'total_parcels' => count($parcels),
                'primary_shipment_id' => $shipment['id'],
                'tracking_numbers' => $trackingNumbers,
                'single_shipment' => true // Flag to indicate this is a single shipment approach
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create single multi-parcel shipment', [
                'order_id' => $order->id,
                'parcel_count' => count($parcels),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Prepare shipment data for a single shipment with multiple parcels
     */
    protected function prepareMultiParcelShipmentData(Order $order, $parcels)
    {
        $address = $order->delivery_address;
        $fullName = trim(($order->user->first_name ?? '') . ' ' . ($order->user->last_name ?? ''));

        if (empty($fullName)) {
            $fullName = $order->first_name . ' ' . $order->last_name;
        }

        $phone = $order->user->phone ?? $order->phone ?? '';
        $phone = preg_replace('/\s+/', '', $phone);
        if (!empty($phone) && !str_starts_with($phone, '+')) {
            if (strlen($phone) === 9) {
                $phone = '+48' . $phone;
            }
        }

        $parcelCalculator = app(ParcelCalculatorService::class);

        // Prepare parcels array for the API
        $apiParcels = [];
        foreach ($parcels as $index => $parcel) {
            $dimensionsInMm = $parcelCalculator->convertToMillimeters($parcel);

            $apiParcels[] = [
                'dimensions' => [
                    'length' => $dimensionsInMm['length'],
                    'width' => $dimensionsInMm['width'],
                    'height' => $dimensionsInMm['height'],
                    'unit' => 'mm'
                ],
                'weight' => [
                    'amount' => $dimensionsInMm['weight'],
                    'unit' => 'kg'
                ]
            ];
        }

        // Create shipment reference
        $reference = $order->uuid . '-MP' . count($parcels); // MP = Multiple Parcels

        // Create comments
        $comments = ($order->notes ?? '') . " [Multiple parcels: " . count($parcels) . " parcels in 1 shipment]";

        $baseData = [
            'receiver' => [
                'name' => $fullName,
                'email' => $order->user->email ?? $order->email,
                'phone' => $phone,
            ],
            'parcels' => $apiParcels,
            'reference' => $reference,
            'external_customer_id' => (string)$order->user_id,
            'comments' => $comments,
            'insurance' => [
                'amount' => (float)$order->total, // Full order value for single shipment
                'currency' => 'PLN'
            ]
        ];

        // Remove empty values
        $baseData['receiver'] = array_filter($baseData['receiver'], function($value) {
            return !empty($value);
        });

        // This method is only called for courier deliveries, so we always use courier service
        if (!is_array($address)) {
            throw new \Exception('Delivery address data is missing or invalid');
        }

        $requiredFields = ['street', 'building_number', 'post_code', 'city'];
        foreach ($requiredFields as $field) {
            if (empty($address[$field])) {
                throw new \Exception("Missing required address field: {$field}");
            }
        }

        $postCode = preg_replace('/\s+/', '', $address['post_code']);
        if (!preg_match('/^\d{2}-\d{3}$/', $postCode)) {
            throw new \Exception('Invalid postal code format. Expected format: XX-XXX');
        }

        return array_merge($baseData, [
            'service' => 'inpost_courier_standard',
            'receiver' => array_merge($baseData['receiver'], [
                'address' => [
                    'street' => trim($address['street']),
                    'building_number' => trim($address['building_number']),
                    'apartment_number' => !empty($address['apartment_number']) ? trim($address['apartment_number']) : null,
                    'post_code' => $postCode,
                    'city' => trim($address['city']),
                    'country_code' => 'PL'
                ]
            ])
        ]);
    }

    protected function prepareSingleParcelData(Order $order, $parcel, $parcelNumber, $totalParcels)
    {
        $address = $order->delivery_address;
        $fullName = trim(($order->user->first_name ?? '') . ' ' . ($order->user->last_name ?? ''));
        
        if (empty($fullName)) {
            $fullName = $order->first_name . ' ' . $order->last_name;
        }

        $phone = $order->user->phone ?? $order->phone ?? '';
        $phone = preg_replace('/\s+/', '', $phone);
        if (!empty($phone) && !str_starts_with($phone, '+')) {
            if (strlen($phone) === 9) {
                $phone = '+48' . $phone;
            }
        }

        $parcelCalculator = app(ParcelCalculatorService::class);
        $dimensionsInMm = $parcelCalculator->convertToMillimeters($parcel);

        // Create parcel-specific reference
        $reference = $order->uuid . '-P' . $parcelNumber;
        
        // Create parcel-specific comments
        $comments = ($order->notes ?? '') . " [Parcel {$parcelNumber}/{$totalParcels}]";
        if (isset($parcel['inpost_size'])) {
            $comments .= " Size: {$parcel['inpost_size']}";
        }
        if (isset($parcel['product_count'])) {
            $comments .= " Items: {$parcel['product_count']}";
        }

        $baseData = [
            'receiver' => [
                'name' => $fullName,
                'email' => $order->user->email ?? $order->email,
                'phone' => $phone,
            ],
            'parcels' => [
                [
                    'dimensions' => [
                        'length' => $dimensionsInMm['length'],
                        'width' => $dimensionsInMm['width'],  
                        'height' => $dimensionsInMm['height'],
                        'unit' => 'mm'
                    ],
                    'weight' => [
                        'amount' => $dimensionsInMm['weight'],
                        'unit' => 'kg'
                    ]
                ]
            ],
            'reference' => $reference,
            'external_customer_id' => (string)$order->user_id,
            'comments' => $comments,
            'insurance' => [
                'amount' => round((float)$order->total / $totalParcels, 2), // Split insurance across parcels
                'currency' => 'PLN'
            ]
        ];

        // Remove empty values
        $baseData['receiver'] = array_filter($baseData['receiver'], function($value) {
            return !empty($value);
        });

        // Check delivery method type
        if ($order->deliveryAddress && 
            $order->deliveryAddress->deliveryMethod && 
            $order->deliveryAddress->deliveryMethod->type === 'point') {
            
            $pointId = $order->deliveryAddress->point_id ?? $address['point_id'] ?? $address['paczkomat_id'] ?? null;
            
            if (empty($pointId)) {
                throw new \Exception('Point ID is required for paczkomat delivery');
            }

            if (config('services.inpost.environment') === 'sandbox') {
                if (!preg_match('/^[A-Z]{3}\d{3}$/', $pointId)) {
                    Log::warning('Using test point ID for sandbox', [
                        'original_point_id' => $pointId,
                        'order_id' => $order->id,
                        'parcel_number' => $parcelNumber
                    ]);
                    $pointId = 'GDA029';
                }
            }

            if (isset($parcel['oversized']) && $parcel['oversized']) {
                Log::warning('Parcel may be too large for selected paczkomat', [
                    'order_id' => $order->id,
                    'parcel_number' => $parcelNumber,
                    'point_id' => $pointId,
                    'dimensions' => $parcel
                ]);
            }
            
            return array_merge($baseData, [
                'service' => 'inpost_locker_standard',
                'custom_attributes' => [
                    'target_point' => $pointId
                ]
            ]);
        }

        // For courier delivery
        if (!is_array($address)) {
            throw new \Exception('Delivery address data is missing or invalid');
        }

        $requiredFields = ['street', 'building_number', 'post_code', 'city'];
        foreach ($requiredFields as $field) {
            if (empty($address[$field])) {
                throw new \Exception("Missing required address field: {$field}");
            }
        }

        $postCode = preg_replace('/\s+/', '', $address['post_code']);
        if (!preg_match('/^\d{2}-\d{3}$/', $postCode)) {
            throw new \Exception('Invalid postal code format. Expected format: XX-XXX');
        }

        return array_merge($baseData, [
            'service' => 'inpost_courier_standard',
            'receiver' => array_merge($baseData['receiver'], [
                'address' => [
                    'street' => trim($address['street']),
                    'building_number' => trim($address['building_number']),
                    'apartment_number' => !empty($address['apartment_number']) ? trim($address['apartment_number']) : null,
                    'post_code' => $postCode,
                    'city' => trim($address['city']),
                    'country_code' => 'PL'
                ]
            ])
        ]);
    }

    protected function prepareShipmentData(Order $order)
    {
        // Calculate intelligent parcel dimensions
        $parcelCalculator = app(ParcelCalculatorService::class);
        $calculatedParcels = $parcelCalculator->calculateParcelDimensions($order);

        Log::info('Calculated parcel dimensions for order', [
            'order_id' => $order->id,
            'parcel_count' => count($calculatedParcels),
            'parcels' => $calculatedParcels
        ]);

        // Check if we have multiple parcels
        if (count($calculatedParcels) > 1) {
            return [
                'multiple_parcels' => true,
                'parcels' => $calculatedParcels,
                'total_parcels' => count($calculatedParcels)
            ];
        }

        // Single parcel - prepare standard shipment data
        $parcel = $calculatedParcels[0];
        return $this->prepareSingleParcelData($order, $parcel, 1, 1);
    }

    public function getShipmentStatus($shipmentId)
    {
        try {
            // Check if we have multiple shipment IDs (comma-separated)
            // This happens for parcel locker deliveries with multiple separate shipments
            if (strpos($shipmentId, ',') !== false) {
                return $this->getMultipleShipmentStatuses($shipmentId);
            }

            // Single shipment status (may contain multiple parcels for courier deliveries)
            $response = $this->client->get('/v1/shipments/' . $shipmentId);
            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::error('Failed to get InPost shipment status', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get status for multiple separate shipments
     */
    protected function getMultipleShipmentStatuses($shipmentIds)
    {
        $shipmentIdArray = explode(',', $shipmentIds);
        $statuses = [];

        foreach ($shipmentIdArray as $shipmentId) {
            $shipmentId = trim($shipmentId);
            if (empty($shipmentId)) continue;

            try {
                $response = $this->client->get('/v1/shipments/' . $shipmentId);
                $statuses[] = json_decode($response->getBody(), true);
            } catch (\Exception $e) {
                Log::error('Failed to get status for shipment', [
                    'shipment_id' => $shipmentId,
                    'error' => $e->getMessage()
                ]);
                // Continue with other shipments even if one fails
                $statuses[] = [
                    'id' => $shipmentId,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'multiple_shipments' => true,
            'shipments' => $statuses,
            'total_shipments' => count($statuses)
        ];
    }

    public function cancelShipment($shipmentId)
    {
        try {
            $response = $this->client->delete('/v1/shipments/' . $shipmentId);
            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::error('Failed to cancel InPost shipment', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function getLabel($shipmentId, $format = 'pdf')
    {
        try {
            // Check if we have multiple shipment IDs (comma-separated)
            // This happens for parcel locker deliveries with multiple separate shipments
            if (strpos($shipmentId, ',') !== false) {
                return $this->getCombinedLabels($shipmentId, $format);
            }

            // Single shipment label
            // For courier deliveries with multiple parcels, this returns a single PDF with all parcel labels
            // For single parcel shipments, this returns a single label
            $response = $this->client->get("/v1/shipments/{$shipmentId}/label?format={$format}");
            return $response->getBody();
        } catch (\Exception $e) {
            Log::error('Failed to get InPost label', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    protected function getCombinedLabels($shipmentIds, $format = 'pdf')
    {
        $shipmentIdArray = explode(',', $shipmentIds);
        $labels = [];

        foreach ($shipmentIdArray as $shipmentId) {
            $shipmentId = trim($shipmentId);
            if (empty($shipmentId)) continue;

            try {
                $response = $this->client->get("/v1/shipments/{$shipmentId}/label?format={$format}");
                $labels[] = [
                    'shipment_id' => $shipmentId,
                    'content' => $response->getBody()->getContents()
                ];
            } catch (\Exception $e) {
                Log::error('Failed to get label for shipment', [
                    'shipment_id' => $shipmentId,
                    'error' => $e->getMessage()
                ]);
                // Continue with other labels even if one fails
            }
        }

        if (empty($labels)) {
            throw new \Exception('No labels could be retrieved');
        }

        // If only one label, return it directly
        if (count($labels) === 1) {
            return $labels[0]['content'];
        }

        // For multiple labels, create a ZIP file
        return $this->createLabelZip($labels, $format);
    }

    protected function createLabelZip($labels, $format)
    {
        $zip = new \ZipArchive();
        $tempFile = tempnam(sys_get_temp_dir(), 'inpost_labels_') . '.zip';

        if ($zip->open($tempFile, \ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('Cannot create ZIP file for labels');
        }

        foreach ($labels as $index => $label) {
            $filename = "label_" . ($index + 1) . "_" . $label['shipment_id'] . ".{$format}";
            $zip->addFromString($filename, $label['content']);
        }

        $zip->close();

        $zipContent = file_get_contents($tempFile);
        unlink($tempFile); // Clean up temp file

        return $zipContent;
    }

    /**
     * Retrieve shipment details including available offers
     *
     * @param string $shipmentId The shipment ID to get details for
     * @return array Shipment details including offers array
     * @throws \Exception If the API request fails
     */
    public function getShipmentOffers($shipmentId)
    {
        try {
            // Get shipment details which includes offers in the response
            $response = $this->client->get("/v1/shipments/{$shipmentId}");
            $shipment = json_decode($response->getBody(), true);

            // Extract offers from shipment response
            $offers = $shipment['offers'] ?? [];

            Log::info('Retrieved InPost shipment offers', [
                'shipment_id' => $shipmentId,
                'offers_count' => count($offers),
                'shipment_status' => $shipment['status'] ?? 'unknown',
                'offers' => $offers
            ]);

            return $offers;
        } catch (\Exception $e) {
            Log::error('Failed to get InPost shipment offers', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Purchase a label by buying a specific offer
     * Checks if shipment is already purchased before attempting to buy
     *
     * @param string $shipmentId The shipment ID
     * @param string $offerId The offer ID to purchase
     * @return array The purchase result including tracking number
     * @throws \Exception If the API request fails
     */
    public function purchaseLabel($shipmentId, $offerId)
    {
        try {
            // First, check the current shipment status
            $response = $this->client->get("/v1/shipments/{$shipmentId}");
            $shipment = json_decode($response->getBody(), true);

            // Check if shipment already has a tracking number
            if (!empty($shipment['tracking_number'])) {
                Log::info('InPost shipment already has tracking number, skipping purchase', [
                    'shipment_id' => $shipmentId,
                    'tracking_number' => $shipment['tracking_number'],
                    'status' => $shipment['status'] ?? 'unknown'
                ]);

                return [
                    'id' => $shipment['id'],
                    'status' => $shipment['status'],
                    'tracking_number' => $shipment['tracking_number'],
                    'parcels' => $shipment['parcels'] ?? [],
                    'already_purchased' => true
                ];
            }

            // Check if there's a selected offer that's already bought
            $selectedOffer = $shipment['selected_offer'] ?? null;
            if ($selectedOffer && isset($selectedOffer['status']) && $selectedOffer['status'] === 'bought') {
                Log::info('InPost shipment offer already bought, skipping purchase', [
                    'shipment_id' => $shipmentId,
                    'offer_id' => $selectedOffer['id'],
                    'offer_status' => $selectedOffer['status']
                ]);

                return [
                    'id' => $shipment['id'],
                    'status' => $shipment['status'],
                    'tracking_number' => $shipment['tracking_number'],
                    'parcels' => $shipment['parcels'] ?? [],
                    'selected_offer' => $selectedOffer,
                    'already_purchased' => true
                ];
            }

            // Shipment needs to be purchased - proceed with purchase
            Log::info('Proceeding with InPost label purchase', [
                'shipment_id' => $shipmentId,
                'offer_id' => $offerId,
                'current_status' => $shipment['status'] ?? 'unknown'
            ]);

            $response = $this->client->post("/v1/shipments/{$shipmentId}/buy", [
                'json' => [
                    'offer_id' => $offerId
                ]
            ]);
            $result = json_decode($response->getBody(), true);

            Log::info('Successfully purchased InPost label', [
                'shipment_id' => $shipmentId,
                'offer_id' => $offerId,
                'tracking_number' => $result['tracking_number'] ?? null,
                'status' => $result['status'] ?? 'unknown',
                'newly_purchased' => true
            ]);

            // Add flag to indicate this was a new purchase
            $result['newly_purchased'] = true;

            return $result;

        } catch (\Exception $e) {
            Log::error('Failed to purchase InPost label', [
                'shipment_id' => $shipmentId,
                'offer_id' => $offerId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Check if a shipment is ready for purchase (has available offers)
     *
     * @param string $shipmentId The shipment ID to check
     * @return array Status information about the shipment
     */
    public function checkShipmentReadiness($shipmentId)
    {
        try {
            $response = $this->client->get("/v1/shipments/{$shipmentId}");
            $shipment = json_decode($response->getBody(), true);

            // If shipment already has tracking number, it's "ready" but doesn't need purchase
            if (!empty($shipment['tracking_number'])) {
                return [
                    'ready' => true,
                    'already_purchased' => true,
                    'status' => $shipment['status'] ?? 'unknown',
                    'offers_count' => count($shipment['offers'] ?? []),
                    'available_offers_count' => 0,
                    'tracking_number' => $shipment['tracking_number'],
                    'offers' => $shipment['offers'] ?? []
                ];
            }

            $offers = $shipment['offers'] ?? [];

            // Filter offers that are ready for purchase (available or selected but not bought)
            $availableOffers = array_filter($offers, function($offer) {
                if (!isset($offer['status'])) return false;

                // Available offers are ready for purchase
                if ($offer['status'] === 'available') return true;

                // Selected offers are ready only if not already bought
                if ($offer['status'] === 'selected') {
                    // Check if this is the selected offer and if it's bought
                    return true; // Let the purchase method handle the bought check
                }

                return false;
            });

            return [
                'ready' => !empty($availableOffers),
                'already_purchased' => false,
                'status' => $shipment['status'] ?? 'unknown',
                'offers_count' => count($offers),
                'available_offers_count' => count($availableOffers),
                'tracking_number' => $shipment['tracking_number'] ?? null,
                'offers' => $offers
            ];
        } catch (\Exception $e) {
            Log::error('Failed to check InPost shipment readiness', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Wait for offers to be prepared (with timeout)
     *
     * @param string $shipmentId The shipment ID to wait for
     * @param int $maxWaitSeconds Maximum time to wait in seconds
     * @param int $checkInterval Interval between checks in seconds
     * @return bool True if offers are ready, false if timeout
     */
    public function waitForOffers($shipmentId, $maxWaitSeconds = 30, $checkInterval = 2)
    {
        $startTime = time();

        while ((time() - $startTime) < $maxWaitSeconds) {
            try {
                $readiness = $this->checkShipmentReadiness($shipmentId);

                if ($readiness['ready']) {
                    if ($readiness['already_purchased']) {
                        Log::info('InPost shipment already purchased, no need to wait for offers', [
                            'shipment_id' => $shipmentId,
                            'tracking_number' => $readiness['tracking_number']
                        ]);
                    } else {
                        Log::info('InPost offers are ready for purchase', [
                            'shipment_id' => $shipmentId,
                            'wait_time' => time() - $startTime,
                            'available_offers' => $readiness['available_offers_count']
                        ]);
                    }
                    return true;
                }

                Log::debug('Waiting for InPost offers', [
                    'shipment_id' => $shipmentId,
                    'status' => $readiness['status'],
                    'offers_count' => $readiness['offers_count'],
                    'available_offers_count' => $readiness['available_offers_count'],
                    'elapsed_time' => time() - $startTime
                ]);

                sleep($checkInterval);
            } catch (\Exception $e) {
                Log::warning('Error while waiting for offers', [
                    'shipment_id' => $shipmentId,
                    'error' => $e->getMessage()
                ]);
                sleep($checkInterval);
            }
        }

        Log::warning('Timeout waiting for InPost offers', [
            'shipment_id' => $shipmentId,
            'max_wait_seconds' => $maxWaitSeconds
        ]);

        return false;
    }

    /**
     * Check if a shipment has already been purchased
     *
     * @param string $shipmentId The shipment ID to check
     * @return array Status information about the purchase state
     */
    public function isShipmentPurchased($shipmentId)
    {
        try {
            $response = $this->client->get("/v1/shipments/{$shipmentId}");
            $shipment = json_decode($response->getBody(), true);

            $hasTrackingNumber = !empty($shipment['tracking_number']);
            $selectedOffer = $shipment['selected_offer'] ?? null;
            $offerBought = $selectedOffer && isset($selectedOffer['status']) && $selectedOffer['status'] === 'bought';

            $isPurchased = $hasTrackingNumber || $offerBought;

            return [
                'is_purchased' => $isPurchased,
                'has_tracking_number' => $hasTrackingNumber,
                'tracking_number' => $shipment['tracking_number'] ?? null,
                'selected_offer_status' => $selectedOffer['status'] ?? null,
                'shipment_status' => $shipment['status'] ?? 'unknown',
                'parcels' => $shipment['parcels'] ?? []
            ];
        } catch (\Exception $e) {
            Log::error('Failed to check InPost shipment purchase status', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
