<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ProductService
{
    /**
     * Find similar products based on category and name similarity
     *
     * @param Product $product
     * @return Collection
     */
    public function findSimilarProducts(Product $product): Collection
    {
        if (!$product->category_id) {
            return collect();
        }

        // Get all products in the same category (excluding current product)
        $candidateProducts = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('stock', '>', 0) // Only show products in stock
            ->get();

        // Extract base name from the current product
        $baseProductName = $this->extractBaseName($product->name);

        // Filter products with similar base names using strict matching
        $similarProducts = $candidateProducts->filter(function ($candidateProduct) use ($baseProductName, $product) {
            $candidateBaseName = $this->extractBaseName($candidateProduct->name);

            // First check if base names are similar
            if (!$this->areBaseNamesSimilar($baseProductName, $candidateBaseName)) {
                return false;
            }

            // Additional strict validation for specific product types
            return $this->passesStrictVariantValidation($product->name, $candidateProduct->name);
        });

        return $similarProducts;
    }

    /**
     * Extract base name by removing only truly variable elements (weight, dimensions, colors, patterns)
     * while preserving ALL critical product specifications (life stage, breed size, material type, etc.)
     *
     * @param string $productName
     * @return string
     */
    protected function extractBaseName(string $productName): string
    {
        // Normalize the name
        $name = trim($productName);

        // Apply patterns in order of specificity - most specific first
        $patterns = [
            // Dimensions with patterns: "25mm/260cm Aussie" -> remove "Aussie" but keep dimensions
            '/(\s+\d+mm[\/x]\d+(?:\/\d+)?cm)\s+([A-Za-z][A-Za-z\s]*?)$/i',

            // Weight patterns: 17kg, 11,4kg, 2.5kg, 70g, 156g etc.
            '/\s+\d+[,.]?\d*\s*kg$/i',
            '/\s+\d+[,.]?\d*\s*g$/i',
            '/\s+\d+[,.]?\d*\s*l$/i',
            '/\s+\d+[,.]?\d*\s*ml$/i',

            // Dimensions for leashes/collars: "10mm x 138/260cm", "15mm x 120cm", etc.
            '/\s+\d+mm\s*x\s*\d+(?:\/\d+)?cm$/i',
            '/\s+\d+cm\s*x\s*\d+cm$/i',

            // Pattern/design names at the end (common pet accessory patterns)
            '/\s+(Aussie|Carnival|Classic|Premium|Deluxe|Standard|Basic|Sport|Active|Comfort|Soft|Strong|Heavy|Light|Pro|Elite|Royal|Noble|Elegant|Modern|Vintage|Retro|Urban|Country|Wild|Nature|Ocean|Forest|Mountain|Desert|Tropical|Arctic|Summer|Winter|Spring|Autumn|Rainbow|Sunset|Sunrise|Galaxy|Star|Moon|Sun|Fire|Ice|Thunder|Lightning|Storm|Calm|Peace|Joy|Happy|Lucky|Magic|Dream|Fantasy|Adventure|Explorer|Hunter|Guardian|Warrior|Hero|Champion|Winner|Leader|Master|Expert|Professional)$/i',

            // Colors at the end: "czarna", "czerwona", "niebieska", etc.
            '/\s+(czarna|czarne|czarny|czerwona|czerwone|czerwony|niebieska|niebieskie|niebieski|zielona|zielone|zielony|żółta|żółte|żółty|różowa|różowe|różowy|szara|szare|szary|brązowa|brązowe|brązowy|biała|białe|biały)$/i',

            // Pack sizes: "2szt", "5 sztuk", etc.
            '/\s+\d+\s*szt\.?$/i',
            '/\s+\d+\s*sztuk$/i',
            '/\s+\d+\s*pack$/i',

            // Basic clothing sizes only when clearly at the end
            '/\s+[SMLX]{1,3}$/i',
        ];

        // Special handling for dimensions with patterns
        if (preg_match('/(\s+\d+mm[\/x]\d+(?:\/\d+)?cm)\s+([A-Za-z][A-Za-z\s]*?)$/i', $name, $matches)) {
            // Keep the dimensions but remove the pattern name
            $name = str_replace($matches[0], $matches[1], $name);
        } else {
            // Apply other patterns normally
            foreach ($patterns as $pattern) {
                $name = preg_replace($pattern, '', $name);
            }
        }

        return trim($name);
    }

    /**
     * Check if two base names are similar enough to be considered variants
     * This method is now much more restrictive to avoid false positives
     *
     * @param string $baseName1
     * @param string $baseName2
     * @return bool
     */
    protected function areBaseNamesSimilar(string $baseName1, string $baseName2): bool
    {
        // Exact match
        if (strcasecmp($baseName1, $baseName2) === 0) {
            return true;
        }

        // Check for critical product specification mismatches that should disqualify similarity
        if ($this->hasCriticalSpecificationMismatch($baseName1, $baseName2)) {
            return false;
        }

        // Normalize for comparison
        $name1 = $this->normalizeForComparison($baseName1);
        $name2 = $this->normalizeForComparison($baseName2);

        // For very short names, require exact match
        if (str_word_count($name1) <= 2 || str_word_count($name2) <= 2) {
            return strcasecmp($name1, $name2) === 0;
        }

        // Check if one is contained in the other (but both must be substantial)
        $minLength = min(strlen($name1), strlen($name2));
        if ($minLength > 10) { // Only for longer product names
            if (strpos($name1, $name2) !== false || strpos($name2, $name1) !== false) {
                return true;
            }
        }

        // Much higher similarity threshold - require 95% similarity
        $similarity = similar_text($name1, $name2, $percent);
        return $percent >= 95;
    }

    /**
     * Check if two product names have critical specification mismatches
     * that should prevent them from being considered variants
     *
     * @param string $name1
     * @param string $name2
     * @return bool
     */
    protected function hasCriticalSpecificationMismatch(string $name1, string $name2): bool
    {
        $name1Lower = mb_strtolower($name1, 'UTF-8');
        $name2Lower = mb_strtolower($name2, 'UTF-8');

        // Life stage mismatches for pet food
        $lifeStages = ['puppy', 'adult', 'senior', 'kitten', 'szczeniak', 'dorosły'];
        $name1LifeStage = null;
        $name2LifeStage = null;

        foreach ($lifeStages as $stage) {
            if (strpos($name1Lower, $stage) !== false) {
                $name1LifeStage = $stage;
            }
            if (strpos($name2Lower, $stage) !== false) {
                $name2LifeStage = $stage;
            }
        }

        // If both have life stages and they're different, not variants
        if ($name1LifeStage && $name2LifeStage && $name1LifeStage !== $name2LifeStage) {
            return true;
        }

        // Breed size mismatches for dog food
        $breedSizes = ['small breed', 'large breed', 'medium breed', 'mini', 'maxi'];
        $name1BreedSize = null;
        $name2BreedSize = null;

        foreach ($breedSizes as $size) {
            if (strpos($name1Lower, $size) !== false) {
                $name1BreedSize = $size;
            }
            if (strpos($name2Lower, $size) !== false) {
                $name2BreedSize = $size;
            }
        }

        // If both have breed sizes and they're different, not variants
        if ($name1BreedSize && $name2BreedSize && $name1BreedSize !== $name2BreedSize) {
            return true;
        }

        // Material type mismatches for leashes/collars
        $materials = ['linka', 'taśma', 'skóra', 'nylon', 'rope', 'leather', 'fabric'];
        $name1Material = null;
        $name2Material = null;

        foreach ($materials as $material) {
            if (strpos($name1Lower, $material) !== false) {
                $name1Material = $material;
            }
            if (strpos($name2Lower, $material) !== false) {
                $name2Material = $material;
            }
        }

        // If both have materials and they're different, not variants
        if ($name1Material && $name2Material && $name1Material !== $name2Material) {
            return true;
        }

        // Product type mismatches
        $productTypes = [
            'regulowana', 'automatyczna', 'standardowa', 'premium', 'classic', 'comfort',
            'puszka', 'sucha karma', 'mokra karma', 'przysmak', 'treat'
        ];

        foreach ($productTypes as $type) {
            $hasType1 = strpos($name1Lower, $type) !== false;
            $hasType2 = strpos($name2Lower, $type) !== false;

            // If one has the type and the other doesn't, they might not be variants
            // This is less strict than the above checks
            if ($hasType1 !== $hasType2) {
                // Additional check: if the difference is significant, consider it a mismatch
                if (abs(strlen($name1) - strlen($name2)) > 10) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Additional strict validation to ensure products are true variants
     * This method performs final checks beyond base name similarity
     *
     * @param string $originalProductName
     * @param string $candidateProductName
     * @return bool
     */
    protected function passesStrictVariantValidation(string $originalProductName, string $candidateProductName): bool
    {
        $originalLower = mb_strtolower($originalProductName, 'UTF-8');
        $candidateLower = mb_strtolower($candidateProductName, 'UTF-8');

        // For pet food products, ensure exact match of critical specifications
        if ($this->isPetFoodProduct($originalProductName)) {
            return $this->validatePetFoodVariant($originalLower, $candidateLower);
        }

        // For leash/collar products, ensure material and type consistency
        if ($this->isLeashCollarProduct($originalProductName)) {
            return $this->validateLeashCollarVariant($originalLower, $candidateLower);
        }

        // For other products, use general validation
        return $this->validateGeneralVariant($originalLower, $candidateLower);
    }

    /**
     * Check if product is pet food
     */
    protected function isPetFoodProduct(string $productName): bool
    {
        $petFoodIndicators = ['acana', 'royal canin', 'hills', 'purina', 'karma', 'puszka', 'sucha', 'mokra'];
        $nameLower = mb_strtolower($productName, 'UTF-8');

        foreach ($petFoodIndicators as $indicator) {
            if (strpos($nameLower, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if product is leash/collar
     */
    protected function isLeashCollarProduct(string $productName): bool
    {
        $leashCollarIndicators = ['smycz', 'obroża', 'szelki', 'leash', 'collar', 'harness'];
        $nameLower = mb_strtolower($productName, 'UTF-8');

        foreach ($leashCollarIndicators as $indicator) {
            if (strpos($nameLower, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate pet food variants with strict specification matching
     */
    protected function validatePetFoodVariant(string $original, string $candidate): bool
    {
        // Extract and compare life stages
        $originalLifeStage = $this->extractLifeStage($original);
        $candidateLifeStage = $this->extractLifeStage($candidate);

        if ($originalLifeStage !== $candidateLifeStage) {
            return false;
        }

        // Extract and compare breed sizes
        $originalBreedSize = $this->extractBreedSize($original);
        $candidateBreedSize = $this->extractBreedSize($candidate);

        if ($originalBreedSize !== $candidateBreedSize) {
            return false;
        }

        // Extract and compare main flavor (but allow flavor variations)
        $originalMainFlavor = $this->extractMainFlavor($original);
        $candidateMainFlavor = $this->extractMainFlavor($candidate);

        // If both have main flavors, they should be the same or compatible
        if ($originalMainFlavor && $candidateMainFlavor) {
            return $this->areFlavorsCompatible($originalMainFlavor, $candidateMainFlavor);
        }

        return true;
    }

    /**
     * Validate leash/collar variants with material and type consistency
     */
    protected function validateLeashCollarVariant(string $original, string $candidate): bool
    {
        // Extract and compare materials
        $originalMaterial = $this->extractMaterial($original);
        $candidateMaterial = $this->extractMaterial($candidate);

        if ($originalMaterial && $candidateMaterial && $originalMaterial !== $candidateMaterial) {
            return false;
        }

        // Extract and compare construction types
        $originalType = $this->extractConstructionType($original);
        $candidateType = $this->extractConstructionType($candidate);

        if ($originalType && $candidateType && $originalType !== $candidateType) {
            return false;
        }

        return true;
    }

    /**
     * General variant validation for other product types
     */
    protected function validateGeneralVariant(string $original, string $candidate): bool
    {
        // For general products, ensure they're not too different
        // Remove weights/sizes and compare the core product description
        $originalCore = preg_replace('/\d+[,.]?\d*\s*(kg|g|l|ml|cm|mm|szt)/', '', $original);
        $candidateCore = preg_replace('/\d+[,.]?\d*\s*(kg|g|l|ml|cm|mm|szt)/', '', $candidate);

        $originalCore = trim($originalCore);
        $candidateCore = trim($candidateCore);

        // Require very high similarity for general products
        similar_text($originalCore, $candidateCore, $percent);
        return $percent >= 90;
    }

    /**
     * Extract life stage from product name
     */
    protected function extractLifeStage(string $productName): ?string
    {
        $lifeStages = ['puppy', 'adult', 'senior', 'kitten', 'szczeniak', 'dorosły'];

        foreach ($lifeStages as $stage) {
            if (strpos($productName, $stage) !== false) {
                return $stage;
            }
        }

        return null;
    }

    /**
     * Extract breed size from product name
     */
    protected function extractBreedSize(string $productName): ?string
    {
        $breedSizes = ['small breed', 'large breed', 'medium breed', 'mini', 'maxi'];

        foreach ($breedSizes as $size) {
            if (strpos($productName, $size) !== false) {
                return $size;
            }
        }

        return null;
    }

    /**
     * Extract main flavor from product name
     */
    protected function extractMainFlavor(string $productName): ?string
    {
        $flavors = ['tuńczyk', 'łosoś', 'kurczak', 'wołowina', 'wieprzowina', 'jagnięcina', 'chicken', 'beef', 'salmon', 'tuna'];

        foreach ($flavors as $flavor) {
            if (strpos($productName, $flavor) !== false) {
                return $flavor;
            }
        }

        return null;
    }

    /**
     * Check if flavors are compatible (same main flavor, different combinations allowed)
     */
    protected function areFlavorsCompatible(string $flavor1, string $flavor2): bool
    {
        // Same flavor is always compatible
        if ($flavor1 === $flavor2) {
            return true;
        }

        // Map similar flavors
        $flavorGroups = [
            'tuna' => ['tuńczyk', 'tuna'],
            'chicken' => ['kurczak', 'chicken'],
            'salmon' => ['łosoś', 'salmon'],
            'beef' => ['wołowina', 'beef']
        ];

        foreach ($flavorGroups as $group) {
            if (in_array($flavor1, $group) && in_array($flavor2, $group)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract material from leash/collar product name
     */
    protected function extractMaterial(string $productName): ?string
    {
        $materials = ['linka', 'taśma', 'skóra', 'nylon', 'rope', 'leather', 'fabric'];

        foreach ($materials as $material) {
            if (strpos($productName, $material) !== false) {
                return $material;
            }
        }

        return null;
    }

    /**
     * Extract construction type from leash/collar product name
     */
    protected function extractConstructionType(string $productName): ?string
    {
        $types = ['regulowana', 'automatyczna', 'standardowa', 'adjustable', 'retractable', 'standard'];

        foreach ($types as $type) {
            if (strpos($productName, $type) !== false) {
                return $type;
            }
        }

        return null;
    }

    /**
     * Normalize string for comparison - now more conservative
     * Only removes truly insignificant words to preserve product specifications
     *
     * @param string $name
     * @return string
     */
    protected function normalizeForComparison(string $name): string
    {
        // Convert to lowercase
        $name = mb_strtolower($name, 'UTF-8');

        // Remove extra spaces
        $name = preg_replace('/\s+/', ' ', $name);

        // Only remove the most common connecting words that truly don't affect product identity
        // Be very conservative - keep most words that might be specifications
        $insignificantWords = ['dla', 'the', 'a', 'an'];
        foreach ($insignificantWords as $word) {
            $name = preg_replace('/\b' . preg_quote($word, '/') . '\b/', '', $name);
        }

        // Clean up extra spaces again
        $name = preg_replace('/\s+/', ' ', $name);

        return trim($name);
    }

    /**
     * Extract the difference between product name and base name
     * This shows what makes this variant unique (flavor + weight, color + size, etc.)
     *
     * @param string $fullName
     * @param string $baseName
     * @return string
     */
    public function extractVariantDifference(string $fullName, string $baseName): string
    {
        // First, try to find the distinguishing part by removing the base name
        $difference = $this->findDistinguishingPart($fullName, $baseName);

        // If we couldn't find a clear difference, try pattern-based extraction
        if (empty($difference)) {
            $difference = $this->extractVariantByPatterns($fullName);
        }

        // Post-process the difference to ensure it's meaningful
        $difference = $this->postProcessVariantDifference($difference, $fullName);

        return $difference ?: 'Standard';
    }

    /**
     * Find the distinguishing part by intelligently comparing full name with base name
     *
     * @param string $fullName
     * @param string $baseName
     * @return string
     */
    protected function findDistinguishingPart(string $fullName, string $baseName): string
    {
        // Normalize both names for comparison
        $fullNormalized = mb_strtolower(trim($fullName), 'UTF-8');
        $baseNormalized = mb_strtolower(trim($baseName), 'UTF-8');

        // If base name is contained in full name, extract what's different
        if (strpos($fullNormalized, $baseNormalized) !== false) {
            // Remove the base name and clean up
            $difference = trim(str_ireplace($baseName, '', $fullName));
            $difference = trim($difference, ' -,');
            return $difference;
        }

        // Try to find the distinguishing part by comparing word by word
        $fullWords = explode(' ', $fullName);
        $baseWords = explode(' ', $baseName);

        // Find words that are in full name but not in base name (or vice versa)
        $distinguishingWords = [];

        // Look for flavor/taste information and measurements
        foreach ($fullWords as $word) {
            $wordLower = mb_strtolower($word, 'UTF-8');
            $isInBase = false;

            foreach ($baseWords as $baseWord) {
                if (mb_strtolower($baseWord, 'UTF-8') === $wordLower) {
                    $isInBase = true;
                    break;
                }
            }

            // Include words that are not in base name and seem to be distinguishing features
            if (!$isInBase && $this->isDistinguishingWord($word)) {
                $distinguishingWords[] = $word;
            }
        }

        return implode(' ', $distinguishingWords);
    }

    /**
     * Check if a word is likely to be a distinguishing feature
     *
     * @param string $word
     * @return bool
     */
    protected function isDistinguishingWord(string $word): bool
    {
        // Weight/size patterns
        if (preg_match('/^\d+[,.]?\d*\s*(kg|g|l|ml)$/i', $word)) {
            return true;
        }

        // Common flavor/taste words in Polish
        $flavorWords = [
            'tuńczyk', 'łosoś', 'kurczak', 'wołowina', 'wieprzowina', 'jagnięcina',
            'ser', 'wodorosty', 'warzywa', 'ryż', 'ziemniaki', 'marchew',
            'szpinak', 'dynia', 'jabłko', 'banan', 'jagody', 'maliny',
            'czekolada', 'wanilia', 'truskawka', 'pomarańcza', 'cytryna',
            'mięso', 'drób', 'ryba', 'owoce', 'zioła', 'miód'
        ];

        $wordLower = mb_strtolower($word, 'UTF-8');
        if (in_array($wordLower, $flavorWords)) {
            return true;
        }

        // Color words
        $colorWords = [
            'czarny', 'czarne', 'biały', 'białe', 'czerwony', 'czerwone',
            'niebieski', 'niebieskie', 'zielony', 'zielone', 'żółty', 'żółte',
            'różowy', 'różowe', 'szary', 'szare', 'brązowy', 'brązowe'
        ];

        if (in_array($wordLower, $colorWords)) {
            return true;
        }

        // Size indicators
        if (preg_match('/^[SMLX]{1,3}$/i', $word)) {
            return true;
        }

        // Conjunctions that connect flavors (i, z, w, na, etc.)
        $conjunctions = ['i', 'z', 'w', 'na', 'do', 'ze'];
        if (in_array($wordLower, $conjunctions)) {
            return true;
        }

        // Pattern/design names (common pet accessory patterns)
        $patternWords = [
            'aussie', 'carnival', 'classic', 'premium', 'deluxe', 'standard', 'basic',
            'sport', 'active', 'comfort', 'soft', 'strong', 'heavy', 'light', 'pro',
            'elite', 'royal', 'noble', 'elegant', 'modern', 'vintage', 'retro',
            'urban', 'country', 'wild', 'nature', 'ocean', 'forest', 'mountain',
            'desert', 'tropical', 'arctic', 'summer', 'winter', 'spring', 'autumn',
            'rainbow', 'sunset', 'sunrise', 'galaxy', 'star', 'moon', 'sun',
            'fire', 'ice', 'thunder', 'lightning', 'storm', 'calm', 'peace',
            'joy', 'happy', 'lucky', 'magic', 'dream', 'fantasy', 'adventure',
            'explorer', 'hunter', 'guardian', 'warrior', 'hero', 'champion',
            'winner', 'leader', 'master', 'expert', 'professional'
        ];

        if (in_array($wordLower, $patternWords)) {
            return true;
        }

        return false;
    }

    /**
     * Extract variant information using pattern matching as fallback
     *
     * @param string $fullName
     * @return string
     */
    protected function extractVariantByPatterns(string $fullName): string
    {
        // Try to extract meaningful patterns from the end of the product name
        $patterns = [
            // Pattern/design names after dimensions: "25mm/260cm Aussie"
            '/\d+mm[\/x]\d+(?:\/\d+)?cm\s+([A-Za-z][A-Za-z\s]*?)$/i',

            // Pattern/design names at the end (common pet accessory patterns)
            '/(Aussie|Carnival|Classic|Premium|Deluxe|Standard|Basic|Sport|Active|Comfort|Soft|Strong|Heavy|Light|Pro|Elite|Royal|Noble|Elegant|Modern|Vintage|Retro|Urban|Country|Wild|Nature|Ocean|Forest|Mountain|Desert|Tropical|Arctic|Summer|Winter|Spring|Autumn|Rainbow|Sunset|Sunrise|Galaxy|Star|Moon|Sun|Fire|Ice|Thunder|Lightning|Storm|Calm|Peace|Joy|Happy|Lucky|Magic|Dream|Fantasy|Adventure|Explorer|Hunter|Guardian|Warrior|Hero|Champion|Winner|Leader|Master|Expert|Professional)$/i',

            // Flavor + weight combinations: "Tuńczyk i Ser 70g"
            '/([a-ząćęłńóśźż\s]+(?:i|z|w|na)\s+[a-ząćęłńóśźż\s]+\s+\d+[,.]?\d*\s*(?:kg|g|l|ml))$/iu',
            // Single flavor + weight: "Tuńczyk 156g"
            '/([a-ząćęłńóśźż]+\s+\d+[,.]?\d*\s*(?:kg|g|l|ml))$/iu',
            // Just weight: "70g", "156g"
            '/(\d+[,.]?\d*\s*(?:kg|g|l|ml))$/i',
            // Color + size combinations: "M czarne"
            '/([SMLX]{1,3}\s+(?:czarny|czarne|biały|białe|czerwony|czerwone|niebieski|niebieskie|zielony|zielone|żółty|żółte|różowy|różowe|szary|szare|brązowy|brązowe))$/i',
            // Just size: "M", "L", "XL"
            '/([SMLX]{1,3})$/i',
            // Just color
            '/(czarny|czarne|biały|białe|czerwony|czerwone|niebieski|niebieskie|zielony|zielone|żółty|żółte|różowy|różowe|szary|szare|brązowy|brązowe)$/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $fullName, $matches)) {
                return trim($matches[1]);
            }
        }

        return '';
    }

    /**
     * Post-process variant difference to ensure it's meaningful and concise
     *
     * @param string $difference
     * @param string $fullName
     * @return string
     */
    protected function postProcessVariantDifference(string $difference, string $fullName): string
    {
        if (empty($difference)) {
            return '';
        }

        // If the difference is too long (more than half the original name),
        // try to extract just the essential parts
        if (strlen($difference) > strlen($fullName) / 2) {
            // Try to extract just flavor and weight/size
            if (preg_match('/([a-ząćęłńóśźż\s]+(?:i|z|w|na)\s+[a-ząćęłńóśźż\s]+\s+\d+[,.]?\d*\s*(?:kg|g|l|ml))/iu', $difference, $matches)) {
                return trim($matches[1]);
            }

            if (preg_match('/([a-ząćęłńóśźż]+\s+\d+[,.]?\d*\s*(?:kg|g|l|ml))/iu', $difference, $matches)) {
                return trim($matches[1]);
            }

            if (preg_match('/(\d+[,.]?\d*\s*(?:kg|g|l|ml))/i', $difference, $matches)) {
                return trim($matches[1]);
            }
        }

        // Clean up the difference
        $difference = trim($difference);

        // Remove leading/trailing punctuation
        $difference = trim($difference, ' -,.');

        return $difference;
    }

    /**
     * Get variant differences for a collection of similar products
     *
     * @param Product $mainProduct
     * @param Collection $similarProducts
     * @return array
     */
    public function getVariantDifferences(Product $mainProduct, Collection $similarProducts): array
    {
        // Collect all products (main + similar) to find the best base name
        $allProducts = collect([$mainProduct])->merge($similarProducts);

        // Find the most appropriate base name by analyzing all product names
        $baseName = $this->findOptimalBaseName($allProducts->pluck('name')->toArray());

        $variants = [];

        // Add main product
        $variants[] = [
            'product' => $mainProduct,
            'difference' => $this->extractVariantDifference($mainProduct->name, $baseName),
            'is_current' => true,
        ];

        // Add similar products
        foreach ($similarProducts as $product) {
            $variants[] = [
                'product' => $product,
                'difference' => $this->extractVariantDifference($product->name, $baseName),
                'is_current' => false,
            ];
        }

        return $variants;
    }

    /**
     * Find the optimal base name by analyzing all product names in the variant group
     *
     * @param array $productNames
     * @return string
     */
    protected function findOptimalBaseName(array $productNames): string
    {
        if (empty($productNames)) {
            return '';
        }

        if (count($productNames) === 1) {
            return $this->extractBaseName($productNames[0]);
        }

        // Find the longest common prefix that makes sense
        $words = array_map(function($name) {
            return explode(' ', trim($name));
        }, $productNames);

        $commonWords = [];
        $maxLength = min(array_map('count', $words));

        // Find common words from the beginning
        for ($i = 0; $i < $maxLength; $i++) {
            $currentWord = mb_strtolower($words[0][$i], 'UTF-8');
            $isCommon = true;

            foreach ($words as $wordArray) {
                if (mb_strtolower($wordArray[$i], 'UTF-8') !== $currentWord) {
                    $isCommon = false;
                    break;
                }
            }

            if ($isCommon) {
                $commonWords[] = $words[0][$i];
            } else {
                break;
            }
        }

        $baseName = implode(' ', $commonWords);

        // If we got a very short base name, try the original extraction method
        if (str_word_count($baseName) < 2) {
            return $this->extractBaseName($productNames[0]);
        }

        return trim($baseName);
    }

    /**
     * Generate dimensions for multiple products in batch
     * Only processes products where dimensions are null
     *
     * @param Collection|array $products
     * @param int $chunkSize
     * @return array Statistics about the batch processing
     */
    public function generateProductsDimensions($products, int $chunkSize = 50): array
    {
        $stats = [
            'total_requested' => is_countable($products) ? count($products) : $products->count(),
            'processed' => 0,
            'skipped' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => []
        ];

        // Convert to collection if needed
        if (!$products instanceof Collection) {
            $products = collect($products);
        }

        // Filter products that need dimensions (where dimensions are null)
        $productsNeedingDimensions = $products->filter(function ($product) {
            return is_null($product->length) || is_null($product->width) || is_null($product->height);
        });

        Log::info('Starting batch dimension generation', [
            'total_products' => $stats['total_requested'],
            'products_needing_dimensions' => $productsNeedingDimensions->count(),
            'chunk_size' => $chunkSize
        ]);

        // Process products in chunks
        $chunks = $productsNeedingDimensions->chunk($chunkSize);

        foreach ($chunks as $chunkIndex => $chunk) {
            Log::info('Processing chunk', [
                'chunk_index' => $chunkIndex + 1,
                'chunk_size' => $chunk->count(),
                'total_chunks' => $chunks->count()
            ]);

            $chunkResult = $this->processProductChunkForDimensions($chunk);

            // Update statistics
            $stats['processed'] += $chunkResult['processed'];
            $stats['successful'] += $chunkResult['successful'];
            $stats['failed'] += $chunkResult['failed'];
            $stats['errors'] = array_merge($stats['errors'], $chunkResult['errors']);
        }

        // Calculate skipped products (those that already had dimensions)
        $stats['skipped'] = $stats['total_requested'] - $productsNeedingDimensions->count();

        Log::info('Batch dimension generation completed', $stats);

        return $stats;
    }

    /**
     * Process a single chunk of products for dimension generation
     *
     * @param Collection $products
     * @return array
     */
    protected function processProductChunkForDimensions(Collection $products): array
    {
        $stats = [
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $apiKey = config('services.openai.api_key');

        if (empty($apiKey)) {
            $error = 'OpenAI API key not configured for dimension generation';
            Log::warning($error);
            $stats['errors'][] = $error;
            $stats['failed'] = $products->count();
            return $stats;
        }

        // Prepare batch request data
        $batchData = [];
        foreach ($products as $product) {
            $productInfo = $product->name;
            if ($product->description) {
                $productInfo .= '. ' . strip_tags($product->description);
            }
            if ($product->category) {
                $productInfo .= '. Category: ' . $product->category->name;
            }

            $batchData[] = [
                'product_id' => $product->id,
                'product_info' => $productInfo,
                'sku' => $product->sku
            ];
        }

        // Create batch prompt for multiple products
        $prompt = $this->createBatchDimensionPrompt($batchData);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(120)->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant that estimates product dimensions for shipping purposes. Always respond with valid JSON.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.3,
                'max_tokens' => 2000,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Parse the batch response
                $batchResults = $this->parseBatchDimensionResponse($content, $products);

                // Save dimensions for each product
                foreach ($batchResults as $productId => $dimensions) {
                    $product = $products->firstWhere('id', $productId);
                    if ($product && $dimensions) {
                        if ($this->saveDimensionsToProduct($product, $dimensions)) {
                            $stats['successful']++;
                        } else {
                            $stats['failed']++;
                            $stats['errors'][] = "Failed to save dimensions for product ID: {$productId}";
                        }
                    } else {
                        $stats['failed']++;
                        $stats['errors'][] = "Invalid dimensions for product ID: {$productId}";
                    }
                    $stats['processed']++;
                }
            } else {
                $error = 'OpenAI API request failed for batch dimension generation';
                Log::error($error, [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                $stats['errors'][] = $error;
                $stats['failed'] = $products->count();
            }
        } catch (\Exception $e) {
            $error = 'Exception during batch dimension generation: ' . $e->getMessage();
            Log::error($error);
            $stats['errors'][] = $error;
            $stats['failed'] = $products->count();
        }

        return $stats;
    }

    /**
     * Create a batch prompt for multiple products
     *
     * @param array $batchData
     * @return string
     */
    protected function createBatchDimensionPrompt(array $batchData): string
    {
        $prompt = "Based on the product information below, estimate realistic dimensions (length, width, height in centimeters) and weight (in kilograms) for shipping purposes.\n\n";
        $prompt .= "Please respond with a JSON object where each key is the product_id and the value contains the dimensions:\n\n";

        foreach ($batchData as $data) {
            $prompt .= "Product ID: {$data['product_id']}\n";
            $prompt .= "SKU: {$data['sku']}\n";
            $prompt .= "Info: {$data['product_info']}\n\n";
        }

        $prompt .= "Expected JSON format:\n";
        $prompt .= "{\n";
        $prompt .= '  "product_id_1": {"length": 25.5, "width": 15.0, "height": 10.0, "weight": 0.8},' . "\n";
        $prompt .= '  "product_id_2": {"length": 30.0, "width": 20.0, "height": 12.0, "weight": 1.2}' . "\n";
        $prompt .= "}\n\n";
        $prompt .= "Guidelines:\n";
        $prompt .= "- Length, width, height should be between 0.5 and 200 cm\n";
        $prompt .= "- Weight should be between 0.01 and 100 kg\n";
        $prompt .= "- Consider the product type and typical packaging\n";
        $prompt .= "- For pet food, consider bag/can dimensions\n";
        $prompt .= "- For accessories, consider typical product sizes\n";
        $prompt .= "- Return only valid JSON, no additional text";

        return $prompt;
    }

    /**
     * Parse batch dimension response from OpenAI
     *
     * @param string $content
     * @param Collection $products
     * @return array
     */
    protected function parseBatchDimensionResponse(string $content, Collection $products): array
    {
        $results = [];

        try {
            // Clean the content to extract JSON
            $content = trim($content);

            // Try to find JSON in the response
            if (preg_match('/\{.*\}/s', $content, $matches)) {
                $jsonContent = $matches[0];
                $dimensions = json_decode($jsonContent, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($dimensions)) {
                    foreach ($dimensions as $productId => $dims) {
                        if (is_array($dims) &&
                            isset($dims['length'], $dims['width'], $dims['height'], $dims['weight'])) {

                            // Validate dimensions are reasonable
                            if ($dims['length'] > 0 && $dims['length'] <= 200 &&
                                $dims['width'] > 0 && $dims['width'] <= 200 &&
                                $dims['height'] > 0 && $dims['height'] <= 200 &&
                                $dims['weight'] > 0 && $dims['weight'] <= 100) {

                                $results[$productId] = [
                                    'length' => round($dims['length'], 2),
                                    'width' => round($dims['width'], 2),
                                    'height' => round($dims['height'], 2),
                                    'weight' => round($dims['weight'], 3),
                                ];
                            } else {
                                Log::warning('Generated dimensions are out of reasonable bounds', [
                                    'product_id' => $productId,
                                    'dimensions' => $dims
                                ]);
                            }
                        }
                    }
                } else {
                    Log::warning('Invalid JSON format in batch dimension response', [
                        'content' => $content,
                        'json_error' => json_last_error_msg()
                    ]);
                }
            } else {
                Log::warning('No JSON found in batch dimension response', [
                    'content' => $content
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Exception parsing batch dimension response', [
                'error' => $e->getMessage(),
                'content' => $content
            ]);
        }

        return $results;
    }

    /**
     * Save dimensions to a product
     *
     * @param Product $product
     * @param array $dimensions
     * @return bool
     */
    protected function saveDimensionsToProduct(Product $product, array $dimensions): bool
    {
        try {
            $product->update([
                'length' => $dimensions['length'],
                'width' => $dimensions['width'],
                'height' => $dimensions['height'],
                'weight' => $dimensions['weight'],
            ]);

            Log::info('Generated dimensions for product', [
                'product_id' => $product->id,
                'sku' => $product->sku,
                'dimensions' => $dimensions,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to save dimensions to product', [
                'product_id' => $product->id,
                'sku' => $product->sku,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
