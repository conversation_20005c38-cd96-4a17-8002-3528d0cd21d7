<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ParcelCalculationService
{
    // InPost parcel size limits (length × width × height in cm, weight in kg)
    const PARCEL_SIZES = [
        'small' => [
            'dimensions' => [8, 38, 64],
            'max_weight' => 25,
            'name' => 'Small'
        ],
        'medium' => [
            'dimensions' => [19, 38, 64],
            'max_weight' => 25,
            'name' => 'Medium'
        ],
        'large' => [
            'dimensions' => [41, 38, 64],
            'max_weight' => 25,
            'name' => 'Large'
        ]
    ];

    // Default dimensions for products without dimensions (cm and kg)
    const DEFAULT_DIMENSIONS = [
        'length' => 10,
        'width' => 10,
        'height' => 10,
        'weight' => 0.5
    ];

    // Packaging overhead
    const PACKAGING_OVERHEAD = [
        'dimension_add' => 2, // cm added to each dimension
        'weight_add' => 0.1   // kg added to weight
    ];

    // Delivery pricing
    const FREE_DELIVERY_THRESHOLD = 149; // PLN
    const ADDITIONAL_PARCEL_COST = 14.99; // PLN

    /**
     * Calculate parcels needed for an order
     *
     * @param Collection $cartItems Array of items with product and quantity
     * @return array
     */
    public function calculateParcelsForOrder(Collection $cartItems): array
    {
        $cacheKey = 'parcel_calculation_' . md5(serialize($cartItems->toArray()));
        
        return Cache::remember($cacheKey, 300, function () use ($cartItems) {
            // Prepare products with dimensions
            $products = $this->prepareProductsWithDimensions($cartItems);
            
            // Calculate parcels using bin-packing algorithm
            $parcels = $this->packProductsIntoParcels($products);
            
            return [
                'parcels' => $parcels,
                'parcel_count' => count($parcels),
                'total_weight' => array_sum(array_column($parcels, 'weight')),
                'products_packed' => array_sum(array_map(fn($p) => count($p['products']), $parcels))
            ];
        });
    }

    /**
     * Calculate delivery price based on parcel count and order total
     *
     * @param int $parcelCount
     * @param float $orderTotal
     * @param float $standardDeliveryFee
     * @return array
     */
    public function calculateDeliveryPrice(int $parcelCount, float $orderTotal, float $standardDeliveryFee = 14.99): array
    {
        $breakdown = [];
        $totalCost = 0;

        if ($parcelCount === 0) {
            return [
                'total_cost' => 0,
                'breakdown' => ['No parcels needed'],
                'is_free' => true
            ];
        }

        // First parcel pricing
        if ($orderTotal >= self::FREE_DELIVERY_THRESHOLD) {
            $breakdown[] = 'First parcel: Free (order ≥ ' . self::FREE_DELIVERY_THRESHOLD . ' PLN)';
            $firstParcelCost = 0;
        } else {
            $breakdown[] = 'First parcel: ' . number_format($standardDeliveryFee, 2) . ' PLN';
            $firstParcelCost = $standardDeliveryFee;
        }

        $totalCost += $firstParcelCost;

        // Additional parcels
        if ($parcelCount > 1) {
            $additionalParcels = $parcelCount - 1;
            $additionalCost = $additionalParcels * self::ADDITIONAL_PARCEL_COST;
            $totalCost += $additionalCost;
            
            $breakdown[] = 'Additional parcels: ' . $additionalParcels . ' × ' . 
                          number_format(self::ADDITIONAL_PARCEL_COST, 2) . ' PLN = ' . 
                          number_format($additionalCost, 2) . ' PLN';
        }

        return [
            'total_cost' => $totalCost,
            'breakdown' => $breakdown,
            'is_free' => $totalCost == 0,
            'parcel_count' => $parcelCount,
            'first_parcel_cost' => $firstParcelCost,
            'additional_parcels_cost' => ($parcelCount > 1) ? ($parcelCount - 1) * self::ADDITIONAL_PARCEL_COST : 0
        ];
    }

    /**
     * Prepare products with dimensions, using defaults where needed
     *
     * @param Collection $cartItems
     * @return array
     */
    protected function prepareProductsWithDimensions(Collection $cartItems): array
    {
        $products = [];

        foreach ($cartItems as $item) {
            $product = $item['product'] ?? $item->product;
            $quantity = $item['quantity'] ?? $item->quantity ?? 1;

            // Get dimensions with packaging overhead
            $dimensions = $this->getProductDimensionsWithPackaging($product);

            // Add each quantity as separate item for packing algorithm
            for ($i = 0; $i < $quantity; $i++) {
                $products[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'length' => $dimensions['length'],
                    'width' => $dimensions['width'],
                    'height' => $dimensions['height'],
                    'weight' => $dimensions['weight'],
                    'volume' => $dimensions['length'] * $dimensions['width'] * $dimensions['height']
                ];
            }
        }

        return $products;
    }

    /**
     * Get product dimensions with packaging overhead
     *
     * @param Product $product
     * @return array
     */
    protected function getProductDimensionsWithPackaging(Product $product): array
    {
        // Use product dimensions if available, otherwise use defaults
        $length = $product->length ?? self::DEFAULT_DIMENSIONS['length'];
        $width = $product->width ?? self::DEFAULT_DIMENSIONS['width'];
        $height = $product->height ?? self::DEFAULT_DIMENSIONS['height'];
        $weight = $product->weight ?? self::DEFAULT_DIMENSIONS['weight'];

        // Add packaging overhead
        return [
            'length' => $length + self::PACKAGING_OVERHEAD['dimension_add'],
            'width' => $width + self::PACKAGING_OVERHEAD['dimension_add'],
            'height' => $height + self::PACKAGING_OVERHEAD['dimension_add'],
            'weight' => $weight + self::PACKAGING_OVERHEAD['weight_add']
        ];
    }

    /**
     * Pack products into parcels using bin-packing algorithm
     *
     * @param array $products
     * @return array
     */
    protected function packProductsIntoParcels(array $products): array
    {
        if (empty($products)) {
            return [];
        }

        // Sort products by volume (largest first) for better packing
        usort($products, fn($a, $b) => $b['volume'] <=> $a['volume']);

        $parcels = [];
        $unpackedProducts = $products;

        while (!empty($unpackedProducts)) {
            $parcel = $this->createNewParcel();
            $packedInThisParcel = [];

            // Try to pack products into current parcel
            foreach ($unpackedProducts as $index => $product) {
                if ($this->canFitInParcel($product, $parcel, $packedInThisParcel)) {
                    $packedInThisParcel[] = $product;
                    $parcel['weight'] += $product['weight'];
                    unset($unpackedProducts[$index]);
                }
            }

            // Reindex array after unsetting elements
            $unpackedProducts = array_values($unpackedProducts);

            // Add parcel if it has products
            if (!empty($packedInThisParcel)) {
                $parcel['products'] = $packedInThisParcel;
                $parcel['product_count'] = count($packedInThisParcel);
                $parcels[] = $parcel;
            } else {
                // If no products could be packed, there might be an oversized item
                if (!empty($unpackedProducts)) {
                    $oversizedProduct = array_shift($unpackedProducts);
                    Log::warning('Oversized product detected', [
                        'product_id' => $oversizedProduct['id'],
                        'dimensions' => [
                            $oversizedProduct['length'],
                            $oversizedProduct['width'], 
                            $oversizedProduct['height']
                        ],
                        'weight' => $oversizedProduct['weight']
                    ]);
                    
                    // Force pack oversized item in its own parcel
                    $parcel['products'] = [$oversizedProduct];
                    $parcel['product_count'] = 1;
                    $parcel['weight'] = $oversizedProduct['weight'];
                    $parcel['oversized'] = true;
                    $parcels[] = $parcel;
                }
            }
        }

        return $parcels;
    }

    /**
     * Create a new empty parcel with optimal size
     *
     * @return array
     */
    protected function createNewParcel(): array
    {
        // Start with large parcel size for optimal packing
        $parcelSize = self::PARCEL_SIZES['large'];
        
        return [
            'size' => 'large',
            'max_dimensions' => $parcelSize['dimensions'],
            'max_weight' => $parcelSize['max_weight'],
            'weight' => 0,
            'products' => [],
            'product_count' => 0
        ];
    }

    /**
     * Check if product can fit in parcel
     *
     * @param array $product
     * @param array $parcel
     * @param array $existingProducts
     * @return bool
     */
    protected function canFitInParcel(array $product, array $parcel, array $existingProducts): bool
    {
        // Check weight limit
        if (($parcel['weight'] + $product['weight']) > $parcel['max_weight']) {
            return false;
        }

        // Simple volume-based check (can be enhanced with 3D bin packing)
        $maxVolume = $parcel['max_dimensions'][0] * $parcel['max_dimensions'][1] * $parcel['max_dimensions'][2];
        $usedVolume = array_sum(array_column($existingProducts, 'volume'));
        
        if (($usedVolume + $product['volume']) > $maxVolume) {
            return false;
        }

        // Check if any dimension exceeds parcel limits
        if ($product['length'] > $parcel['max_dimensions'][0] ||
            $product['width'] > $parcel['max_dimensions'][1] ||
            $product['height'] > $parcel['max_dimensions'][2]) {
            return false;
        }

        return true;
    }

    /**
     * Get parcel size information
     *
     * @return array
     */
    public function getParcelSizes(): array
    {
        return self::PARCEL_SIZES;
    }

    /**
     * Clear parcel calculation cache
     *
     * @return void
     */
    public function clearCache(): void
    {
        Cache::flush();
    }
}
