<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class DeliveryPricingService
{
    protected ParcelCalculationService $parcelService;

    public function __construct(ParcelCalculationService $parcelService)
    {
        $this->parcelService = $parcelService;
    }

    /**
     * Calculate delivery cost for cart items
     *
     * @param Collection $cartItems
     * @param float $orderTotal
     * @param float $standardDeliveryFee
     * @return array
     */
    public function calculateDeliveryCost(Collection $cartItems, float $orderTotal, float $standardDeliveryFee = 14.99): array
    {
        try {
            // Calculate parcels needed
            $parcelData = $this->parcelService->calculateParcelsForOrder($cartItems);
            
            // Calculate delivery pricing
            $pricingData = $this->parcelService->calculateDeliveryPrice(
                $parcelData['parcel_count'],
                $orderTotal,
                $standardDeliveryFee
            );

            return [
                'success' => true,
                'delivery_cost' => $pricingData['total_cost'],
                'parcel_count' => $parcelData['parcel_count'],
                'parcels' => $parcelData['parcels'],
                'pricing_breakdown' => $pricingData['breakdown'],
                'is_free_delivery' => $pricingData['is_free'],
                'first_parcel_cost' => $pricingData['first_parcel_cost'] ?? 0,
                'additional_parcels_cost' => $pricingData['additional_parcels_cost'] ?? 0,
                'total_weight' => $parcelData['total_weight'],
                'products_packed' => $parcelData['products_packed'],
                'calculation_method' => 'parcel_based'
            ];
        } catch (\Exception $e) {
            Log::error('Parcel-based delivery calculation failed', [
                'error' => $e->getMessage(),
                'cart_items_count' => $cartItems->count(),
                'order_total' => $orderTotal
            ]);

            // Fallback to traditional calculation
            return $this->calculateTraditionalDelivery($orderTotal, $standardDeliveryFee);
        }
    }

    /**
     * Fallback to traditional delivery calculation
     *
     * @param float $orderTotal
     * @param float $standardDeliveryFee
     * @return array
     */
    protected function calculateTraditionalDelivery(float $orderTotal, float $standardDeliveryFee = 14.99): array
    {
        $isFree = $orderTotal >= ParcelCalculationService::FREE_DELIVERY_THRESHOLD;
        $deliveryCost = $isFree ? 0 : $standardDeliveryFee;

        return [
            'success' => true,
            'delivery_cost' => $deliveryCost,
            'parcel_count' => 1, // Assume single parcel for fallback
            'parcels' => [],
            'pricing_breakdown' => [
                $isFree 
                    ? 'Free delivery (order ≥ ' . ParcelCalculationService::FREE_DELIVERY_THRESHOLD . ' PLN)'
                    : 'Standard delivery: ' . number_format($standardDeliveryFee, 2) . ' PLN'
            ],
            'is_free_delivery' => $isFree,
            'first_parcel_cost' => $deliveryCost,
            'additional_parcels_cost' => 0,
            'total_weight' => 0,
            'products_packed' => 0,
            'calculation_method' => 'traditional_fallback'
        ];
    }

    /**
     * Get delivery information for display
     *
     * @param Collection $cartItems
     * @param float $orderTotal
     * @return array
     */
    public function getDeliveryInfo(Collection $cartItems, float $orderTotal): array
    {
        $deliveryData = $this->calculateDeliveryCost($cartItems, $orderTotal);

        $info = [
            'cost' => $deliveryData['delivery_cost'],
            'is_free' => $deliveryData['is_free_delivery'],
            'parcel_count' => $deliveryData['parcel_count'],
            'method' => $deliveryData['calculation_method']
        ];

        // Generate user-friendly description
        if ($deliveryData['parcel_count'] === 1) {
            if ($deliveryData['is_free_delivery']) {
                $info['description'] = 'Free delivery (1 parcel)';
            } else {
                $info['description'] = 'Delivery: ' . number_format($deliveryData['delivery_cost'], 2) . ' PLN (1 parcel)';
            }
        } else {
            $info['description'] = sprintf(
                'Delivery: %s PLN (%d parcels)',
                number_format($deliveryData['delivery_cost'], 2),
                $deliveryData['parcel_count']
            );
        }

        // Add detailed breakdown for multiple parcels
        if ($deliveryData['parcel_count'] > 1) {
            $info['breakdown'] = $deliveryData['pricing_breakdown'];
        }

        return $info;
    }

    /**
     * Update order with parcel information
     *
     * @param Order $order
     * @param Collection $cartItems
     * @return void
     */
    public function updateOrderWithParcelInfo(Order $order, Collection $cartItems): void
    {
        try {
            $deliveryData = $this->calculateDeliveryCost($cartItems, $order->total);
            
            // Update order with parcel information
            $order->update([
                'parcel_count' => $deliveryData['parcel_count'],
                'delivery_method' => $deliveryData['calculation_method'],
                'parcel_data' => json_encode([
                    'parcels' => $deliveryData['parcels'],
                    'total_weight' => $deliveryData['total_weight'],
                    'pricing_breakdown' => $deliveryData['pricing_breakdown']
                ])
            ]);

            Log::info('Order updated with parcel information', [
                'order_id' => $order->id,
                'parcel_count' => $deliveryData['parcel_count'],
                'delivery_cost' => $deliveryData['delivery_cost']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update order with parcel info', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get parcel summary for order
     *
     * @param Order $order
     * @return array
     */
    public function getOrderParcelSummary(Order $order): array
    {
        $parcelData = json_decode($order->parcel_data ?? '{}', true);
        
        return [
            'parcel_count' => $order->parcel_count ?? 1,
            'delivery_method' => $order->delivery_method ?? 'traditional',
            'total_weight' => $parcelData['total_weight'] ?? 0,
            'pricing_breakdown' => $parcelData['pricing_breakdown'] ?? [],
            'parcels' => $parcelData['parcels'] ?? []
        ];
    }

    /**
     * Validate parcel configuration
     *
     * @param Collection $cartItems
     * @return array
     */
    public function validateParcelConfiguration(Collection $cartItems): array
    {
        $issues = [];
        
        try {
            $parcelData = $this->parcelService->calculateParcelsForOrder($cartItems);
            
            // Check for oversized items
            foreach ($parcelData['parcels'] as $index => $parcel) {
                if (isset($parcel['oversized']) && $parcel['oversized']) {
                    $product = $parcel['products'][0];
                    $issues[] = [
                        'type' => 'oversized_product',
                        'message' => "Product '{$product['name']}' exceeds standard parcel dimensions",
                        'product_id' => $product['id'],
                        'parcel_index' => $index
                    ];
                }
            }

            // Check for excessive parcel count
            if ($parcelData['parcel_count'] > 5) {
                $issues[] = [
                    'type' => 'excessive_parcels',
                    'message' => "Order requires {$parcelData['parcel_count']} parcels, which may result in high delivery costs",
                    'parcel_count' => $parcelData['parcel_count']
                ];
            }

        } catch (\Exception $e) {
            $issues[] = [
                'type' => 'calculation_error',
                'message' => 'Unable to calculate parcel requirements: ' . $e->getMessage()
            ];
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues
        ];
    }

    /**
     * Get delivery cost comparison (old vs new method)
     *
     * @param Collection $cartItems
     * @param float $orderTotal
     * @param float $standardDeliveryFee
     * @return array
     */
    public function getDeliveryCostComparison(Collection $cartItems, float $orderTotal, float $standardDeliveryFee = 14.99): array
    {
        // New parcel-based calculation
        $parcelBased = $this->calculateDeliveryCost($cartItems, $orderTotal, $standardDeliveryFee);
        
        // Traditional calculation
        $traditional = $this->calculateTraditionalDelivery($orderTotal, $standardDeliveryFee);

        return [
            'parcel_based' => [
                'cost' => $parcelBased['delivery_cost'],
                'parcel_count' => $parcelBased['parcel_count'],
                'method' => 'Parcel-based calculation'
            ],
            'traditional' => [
                'cost' => $traditional['delivery_cost'],
                'parcel_count' => 1,
                'method' => 'Traditional calculation'
            ],
            'difference' => $parcelBased['delivery_cost'] - $traditional['delivery_cost'],
            'recommendation' => $parcelBased['delivery_cost'] <= $traditional['delivery_cost'] ? 'parcel_based' : 'traditional'
        ];
    }
}
