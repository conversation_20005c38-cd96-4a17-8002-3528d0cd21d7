<?php

namespace App\Services;

use App\Models\Producent;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class MolosProducents extends MolosApiService
{
    /**
     * Import all producents from the Molos API.
     *
     * @return void
     */
    public function importProducents(): void
    {
        $response = $this->getHttpClient()->get($this->apiUrl . '/products/producers', [
            'page' => 1,
            'limit' => 300,
        ]);

        if ($response->successful()) {
            $result = $response->json();
            $producers = $result['producers'] ?? [];

            foreach ($producers as $producerData) {
                $this->importOneProducent($producerData);
            }
        } else {
            Log::error('Failed to fetch producents from Molos API', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
        }
    }

    /**
     * Import a single producent.
     *
     * @param array $producerData
     * @return \App\Models\Producent|null
     */
    public function importOneProducent($producerData)
    {
        // Validate necessary data
        if (empty($producerData['id']) || empty($producerData['name'])) {
            Log::warning('Incomplete producent data received', ['data' => $producerData]);
            return null;
        }

        // Check if producent exists before update/create
        $existingProducent = Producent::where('molos_id', $producerData['id'])->first();
        
        // Update or create the producent record
        $producent = Producent::updateOrCreate(
            ['molos_id' => $producerData['id']],
            [
                'name' => $producerData['name'],
                'slug' => Str::slug($producerData['name']),
                'image' => $producerData['image'] ?? null,
                'priority' => $producerData['priority'] ?? true,
            ]
        );

        // Handle logo image import only if:
        // - there's an image URL in the data
        // - AND either the producent is new OR doesn't have a logo yet
        if (!empty($producerData['image']) && 
            (!$existingProducent || !$existingProducent->hasMedia('logos'))) {
            $this->handleLogo($producent, $producerData['image']);
        }

        // Generate and save description if not present
        if (empty($producent->description)) {
            // $producent->description = $this->generateDescription('Bosch');
            // $producent->save();
        }

        return $producent;
    }

    /**
     * Handle the import of the producent's logo into the media collection.
     *
     * @param \App\Models\Producent $producent
     * @param string $logoUrl
     * @return void
     */
    protected function handleLogo(Producent $producent, string $logoUrl): void
    {
        try {
            // Encode the URL to handle spaces and special characters
            $encodedUrl = $this->encodeUrl($logoUrl);

            // Clear existing logos to maintain single file per producent
            $producent->clearMediaCollection('logos');

            // Add the new logo to the 'logos' collection
            $producent->addMediaFromUrl($encodedUrl)
                      ->withResponsiveImages()
                      ->toMediaCollection('logos');

            Log::info('Successfully imported logo for producent', [
                'producent_id' => $producent->id,
                'logo_url' => $encodedUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to import logo for producent', [
                'producent_id' => $producent->id,
                'logo_url' => $logoUrl,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate a SEO-friendly description using the ChatGPT API.
     *
     * @param string $producerName
     * @return string|null
     */
    protected function generateDescription($producerName)
    {
        $apiKey = config('services.openai.api_key');

        if (empty($apiKey)) {
            Log::warning('OpenAI API key is not set.');
            return null;
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/chat/completions', [
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a helpful assistant.'
                ],
                [
                    'role' => 'user',
                    'content' => "Generate a SEO friendly description for the following producent: $producerName (Products for pets) with html tags. It should include a full description of the producent, and additional information if possible. Don't include your system tags. only generated text with html tags. Don't include h1 as title. It should be in Polish"
                ]
            ],
            'max_tokens' => 550,
        ]);

        if ($response->successful()) {
            $result = $response->json();
            return $result['choices'][0]['message']['content'] ?? '';
        }

        Log::error('Failed to generate description via OpenAI API', [
            'status' => $response->status(),
            'body' => $response->body(),
        ]);

        return null;
    }

    /**
     * Retrieve a producent by its Molos ID.
     *
     * @param int $id
     * @return \App\Models\Producent|null
     */
    public function handleProducent(int $id): ?Producent
    {
        return Producent::where('molos_id', $id)->first();
    }
}
