<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\DB;

class MolosOrderService
{
    protected $molosApi;

    public function __construct(MolosApiService $molosApi)
    {
        $this->molosApi = $molosApi;
    }

    public function createOrder(Order $order)
    {
        $molosOrderData = $this->prepareOrderData($order);
        return $this->molosApi->post('/cart/confirm', $molosOrderData);
    }

    protected function prepareOrderData(Order $order)
    {
        return [
            'customer' => [
                'name' => $order->user->name,
                'email' => $order->user->email,
                'phone' => $order->user->phone ?? '',
                'address' => [
                    'street' => $order->shipping_address['street'] ?? '',
                    'post_code' => $order->shipping_address['post_code'] ?? '',
                    'city' => $order->shipping_address['city'] ?? '',
                    'country' => $order->shipping_address['country'] ?? 'PL',
                ],
                'bill_data' => [
                    'kind' => $order->billing_address['kind'] ?? 0,
                    'name' => $order->billing_address['name'] ?? '',
                    'street' => $order->billing_address['street'] ?? '',
                    'post_code' => $order->billing_address['post_code'] ?? '',
                    'city' => $order->billing_address['city'] ?? '',
                    'country' => $order->billing_address['country'] ?? 'PL',
                    'nip' => $order->billing_address['nip'] ?? '',
                ],
            ],
            'products' => $order->items->map(function ($item) {
                return [
                    'id' => $item->product->id,
                    'symbol' => $item->product->sku,
                    'count' => $item->quantity,
                    'price' => $item->product->price,
                ];
            })->toArray(),
            'delivery_id' => $order->delivery_method,
            'payment_id' => $order->payment_method,
            'info' => $order->notes ?? '',
        ];
    }

    public function getOrderStatus($molosOrderId)
    {
        return $this->molosApi->get("/orders/{$molosOrderId}");
    }
} 