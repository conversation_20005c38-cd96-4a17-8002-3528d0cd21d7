<?php

namespace App\Services;

use App\Models\Product;

class ProductFilterService
{
    public function filterAndSort($categoryIds, $filters)
    {
        // Start building the product query
        $query = Product::inStock()->whereIn('category_id', $categoryIds);

        // Apply filter by multiple producents if provided
        if (!empty($filters['producent_id'])) {
            // Check if it's an array of producent IDs
            if (is_array($filters['producent_id'])) {
                $query->whereIn('producent_id', $filters['producent_id']);
            } else {
                $query->where('producent_id', $filters['producent_id']);
            }
        }

        // Apply filter by properties if provided
        if (!empty($filters['properties'])) {
            foreach ($filters['properties'] as $propertyId => $values) {
                $query->whereHas('properties', function ($q) use ($propertyId, $values) {
                    $q->where('property_id', $propertyId);

                    // Handle range filters (from/to structure)
                    if (is_array($values) && isset($values['from']) && isset($values['to'])) {
                        $from = $values['from'];
                        $to = $values['to'];

                        $q->where(function ($subQuery) use ($from, $to) {
                            $subQuery->whereRaw('CAST(value AS DECIMAL(10,2)) BETWEEN ? AND ?', [$from, $to]);
                        });
                    }
                    // Handle checkbox/multiple selection filters
                    elseif (is_array($values)) {
                        $q->where(function ($subQuery) use ($values) {
                            foreach ($values as $value) {
                                if (is_numeric($value)) {
                                    // Property option ID
                                    $subQuery->orWhere('property_option_id', $value);
                                } else {
                                    // Text value
                                    $subQuery->orWhere('value', 'LIKE', '%' . $value . '%');
                                }
                            }
                        });
                    }
                    // Handle single value (text search)
                    else {
                        if (is_numeric($values)) {
                            $q->where('property_option_id', $values);
                        } else {
                            $q->where('value', 'LIKE', '%' . $values . '%');
                        }
                    }
                });
            }
        }

        // Apply sorting if provided
        if (!empty($filters['sort'])) {
            $sort = $filters['sort'];
            switch ($sort) {
                case 'name-asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name-desc':
                    $query->orderBy('name', 'desc');
                    break;
                case 'price-asc':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price-desc':
                    $query->orderBy('price', 'desc');
                    break;
                default:
                    $query->orderBy('name', 'asc'); // Default sort
                    break;
            }
        }else{
            $query->orderBy('name', 'asc'); // Default sort
        }

        return $query->paginate(24)->appends(request()->query());
    }
}
