<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Category;
use App\Models\Producent;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MolosApiService
{
    protected $apiUrl = 'https://b2b.molos.com.pl/api/v1';
    protected $apiKey;

    public function __construct()
    {
        $this->apiKey = config('services.molos.api_key');
    }

    protected function getHttpClient()
    {
        return Http::timeout(120)->withHeaders([
            'X-API-KEY' => $this->apiKey,
            'User-Agent' => 'curl/7.68.0',
        ]);
    }

    public function getApiKey()
    {
        return $this->apiKey;
    }

    public function getApiUrl()
    {
        return $this->apiUrl;
    }

    public function encodeUrl($url)
    {
        return str_replace(' ', '%20', $url);
    }
}
