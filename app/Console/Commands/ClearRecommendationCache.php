<?php

namespace App\Console\Commands;

use App\Services\App\ProductRecommendationService;
use Illuminate\Console\Command;

class ClearRecommendationCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recommendations:clear-cache {type?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear product recommendation cache';

    /**
     * Execute the console command.
     */
    public function handle(ProductRecommendationService $recommendationService): int
    {
        $type = $this->argument('type');
        
        $this->info('Clearing recommendation cache...');
        
        try {
            $recommendationService->clearCache($type);
            
            if ($type) {
                $this->info("Successfully cleared {$type} recommendation cache.");
            } else {
                $this->info('Successfully cleared all recommendation cache.');
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to clear recommendation cache: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
