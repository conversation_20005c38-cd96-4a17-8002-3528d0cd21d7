<?php

namespace App\Console\Commands;

use App\Jobs\ImportProductsJob;
use Illuminate\Console\Command;

class ImportProductsCommand extends Command
{
    protected $signature = 'app:import-products';
    protected $description = 'Import products from Molos API';

    public function handle()
    {
        $this->info('Dispatching import job...');
        ImportProductsJob::dispatch();
        $this->info('Import job dispatched.');
    }
}
