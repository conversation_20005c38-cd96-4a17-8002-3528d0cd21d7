<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Services\ParcelCalculatorService;
use Illuminate\Console\Command;

class TestMultiParcelCommand extends Command
{
    protected $signature = 'test:multi-parcel {order_id?}';
    protected $description = 'Test multi-parcel calculation for an order';

    public function handle()
    {
        $orderId = $this->argument('order_id');
        
        if (!$orderId) {
            // Get the most recent order with items
            $order = Order::with(['items.product', 'deliveryAddress.deliveryMethod'])
                          ->whereHas('items')
                          ->latest()
                          ->first();
                          
            if (!$order) {
                $this->error('No orders found with items');
                return 1;
            }
            
            $this->info("Using latest order: {$order->id} (UUID: {$order->uuid})");
        } else {
            $order = Order::with(['items.product', 'deliveryAddress.deliveryMethod'])->find($orderId);
            
            if (!$order) {
                $this->error("Order {$orderId} not found");
                return 1;
            }
        }

        $this->info("Testing multi-parcel calculation for Order #{$order->id}");
        $this->info("Order UUID: {$order->uuid}");
        $this->info("Items count: " . $order->items->count());
        $this->info("Total value: {$order->total} PLN");
        
        // Show delivery method info
        if ($order->deliveryAddress && $order->deliveryAddress->deliveryMethod) {
            $dm = $order->deliveryAddress->deliveryMethod;
            $this->info("Delivery method: {$dm->name} (Type: {$dm->type}, Provider: {$dm->api_provider})");
        }

        $this->newLine();

        // List all products in the order
        $this->info("Products in order:");
        foreach ($order->items as $item) {
            $product = $item->product;
            $this->line("- {$product->name} (SKU: {$product->sku}) x{$item->quantity}");
            $this->line("  Dimensions: {$product->length}x{$product->width}x{$product->height} cm, Weight: {$product->weight} kg");
            
            if (!$product->hasDimensions()) {
                $this->warn("  ⚠️  Product missing dimensions!");
            }
        }

        $this->newLine();

        // Calculate parcels
        $parcelCalculator = app(ParcelCalculatorService::class);
        
        try {
            $this->info("Calculating parcel dimensions...");
            $parcels = $parcelCalculator->calculateParcelDimensions($order);
            
            $this->info("Result: " . count($parcels) . " parcel(s) needed");
            $this->newLine();
            
            foreach ($parcels as $index => $parcel) {
                $parcelNum = $index + 1;
                $this->info("📦 Parcel {$parcelNum}:");
                $this->line("  Dimensions: {$parcel['length']} x {$parcel['width']} x {$parcel['height']} cm");
                $this->line("  Weight: {$parcel['weight']} kg");
                
                if (isset($parcel['inpost_size'])) {
                    $this->line("  InPost Size: {$parcel['inpost_size']}");
                }
                
                if (isset($parcel['oversized']) && $parcel['oversized']) {
                    $this->warn("  ⚠️  OVERSIZED - may not fit in InPost lockers");
                }
                
                if (isset($parcel['product_count'])) {
                    $this->line("  Contains {$parcel['product_count']} product(s)");
                }
                
                if (isset($parcel['packed_products'])) {
                    $this->line("  Products:");
                    foreach ($parcel['packed_products'] as $product) {
                        $this->line("    - {$product['product_name']} ({$product['length']}x{$product['width']}x{$product['height']} cm, {$product['weight']} kg)");
                    }
                }
                
                $this->newLine();
            }
            
            // Test millimeter conversion
            $this->info("API format (millimeters):");
            foreach ($parcels as $index => $parcel) {
                $mmDimensions = $parcelCalculator->convertToMillimeters($parcel);
                $parcelNum = $index + 1;
                $this->line("Parcel {$parcelNum}: {$mmDimensions['length']}x{$mmDimensions['width']}x{$mmDimensions['height']} mm, {$mmDimensions['weight']} kg");
            }
            
        } catch (\Exception $e) {
            $this->error("Error calculating parcels: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
} 