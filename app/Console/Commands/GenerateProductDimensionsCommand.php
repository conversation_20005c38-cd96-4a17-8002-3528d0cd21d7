<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Services\MolosProducts;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateProductDimensionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:dimensions {--limit=50 : Number of products to process} {--force : Force regenerate even if dimensions exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate missing product dimensions using AI';

    protected $molosProducts;

    public function __construct(MolosProducts $molosProducts)
    {
        parent::__construct();
        $this->molosProducts = $molosProducts;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = (int) $this->option('limit');
        $force = $this->option('force');

        $this->info("Generating product dimensions (limit: {$limit}, force: " . ($force ? 'yes' : 'no') . ")");

        // Get products that need dimensions
        $query = Product::with('category');
        
        if (!$force) {
            $query->where(function($q) {
                $q->whereNull('length')
                  ->orWhereNull('width')
                  ->orWhereNull('height')
                  ->orWhere('length', '<=', 0)
                  ->orWhere('width', '<=', 0)
                  ->orWhere('height', '<=', 0);
            });
        }

        $products = $query->limit($limit)->get();

        if ($products->isEmpty()) {
            $this->info('No products need dimension generation.');
            return 0;
        }

        $this->info("Found {$products->count()} products to process...");

        $generated = 0;
        $failed = 0;
        $skipped = 0;

        $progressBar = $this->output->createProgressBar($products->count());
        $progressBar->start();

        foreach ($products as $product) {
            try {
                // Check if product already has reasonable dimensions (unless forced)
                if (!$force && $product->hasReasonableDimensions()) {
                    $skipped++;
                    $progressBar->advance();
                    continue;
                }

                $dimensions = $this->molosProducts->generateProductDimensions($product);
                
                if ($dimensions) {
                    $generated++;
                    $this->newLine();
                    $this->info("✓ Generated dimensions for: {$product->name} (ID: {$product->id})");
                    $this->line("  Dimensions: {$dimensions['length']}x{$dimensions['width']}x{$dimensions['height']}cm, {$dimensions['weight']}kg");
                } else {
                    $failed++;
                    $this->newLine();
                    $this->error("✗ Failed to generate dimensions for: {$product->name} (ID: {$product->id})");
                }

                // Add delay to avoid overwhelming OpenAI API
                usleep(1000000); // 1 second delay

            } catch (\Exception $e) {
                $failed++;
                $this->newLine();
                $this->error("✗ Exception for product {$product->id}: " . $e->getMessage());
                Log::error('Dimension generation command failed for product', [
                    'product_id' => $product->id,
                    'error' => $e->getMessage()
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->info("Dimension generation completed!");
        $this->info("Generated: {$generated}");
        $this->info("Failed: {$failed}");
        $this->info("Skipped: {$skipped}");
        $this->info("Total processed: " . ($generated + $failed + $skipped));

        return 0;
    }
} 