<?php

namespace App\Console\Commands;

use App\Services\MolosProducts;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateProductsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:update {type=both : Type of update (stock, prices, both)} {--limit=100 : Number of products per page}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update product stock and/or prices from Molos API';

    protected $molosProducts;

    public function __construct(MolosProducts $molosProducts)
    {
        parent::__construct();
        $this->molosProducts = $molosProducts;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $limit = (int) $this->option('limit');

        $this->info("Starting {$type} update with limit: {$limit}");

        $page = 1;
        $totalUpdated = 0;
        $totalProcessed = 0;

        try {
            do {
                $this->info("Processing page {$page}...");

                switch ($type) {
                    case 'stock':
                        $hasNext = $this->molosProducts->updateStockOnly($page, $limit);
                        break;
                    case 'prices':
                    case 'both':
                    default:
                        $hasNext = $this->molosProducts->updateStockAndPrices($page, $limit);
                        break;
                }

                if ($hasNext === false) {
                    $this->info("No more products to process on page {$page}");
                    break;
                }

                $page++;
                $totalProcessed += $limit;

                // Add a small delay to avoid overwhelming the API
                usleep(500000); // 0.5 second delay

            } while ($hasNext);

            $this->info("Update completed successfully!");
            $this->info("Total pages processed: " . ($page - 1));
            $this->info("Estimated products processed: " . $totalProcessed);

        } catch (\Exception $e) {
            $this->error("Update failed: " . $e->getMessage());
            Log::error('Product update command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }
} 