<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MolosCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'molos_id',
        'name',
        'path',
        'parent_molos_id',
    ];

    public function products()
    {
        return $this->hasMany(Product::class, 'molos_category_id', 'molos_id');
    }

    public function children()
    {
        return $this->hasMany(MolosCategory::class, 'parent_molos_id', 'molos_id');
    }

    public function parent()
    {
        return $this->belongsTo(MolosCategory::class, 'parent_molos_id', 'molos_id');
    }
} 