<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Cart extends Model
{
    use HasFactory;

    // Use UUIDs for primary key
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = ['user_id', 'status'];

    // Automatically generate UUID when creating a new cart
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->{$model->getKeyName()} = (string) Str::uuid();
        });
    }

    public function items()
    {
        return $this->hasMany(CartItem::class);
    }
}
