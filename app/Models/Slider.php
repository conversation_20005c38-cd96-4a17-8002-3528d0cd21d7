<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Slider extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = ['title', 'link', 'is_active', 'start_date', 'end_date'];


    protected static $filters = [
        'title' => ['type' => 'text'],
    ];

    protected static $sorts = [
        'title',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
        $this->addMediaCollection('mobile_image')->singleFile();
    }
    

    public function scopeMain($query)
    {
        return $query;//->where('is_active', true);//->orderBy('sort', 'asc');
    }
}