<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'delivery_address_id',
        'delivery_method',
        'delivery_address',
        'first_name',
        'last_name',
        'email',
        'phone',
        'status',
        'payment_method',
        'payment_status',
        'subtotal',
        'delivery_cost',
        'total',
        'notes',
        'billing_address',
        'molos_order_id',
        'molos_status',
        'inpost_shipment_id',
        'inpost_tracking_number',
        'inpost_status',
        'payu_order_id',
        'payu_external_id',
        'payment_checked_at',
    ];

    protected $casts = [
        'delivery_address' => 'array',
        'billing_address' => 'array',
        'subtotal' => 'decimal:2',
        'delivery_cost' => 'decimal:2',
        'total' => 'decimal:2',
        'payment_checked_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($order) {
            $order->uuid = Str::uuid();
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withDefault([
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone
        ]);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function deliveryAddress()
    {
        return $this->belongsTo(DeliveryAddress::class);
    }

    public function getDeliveryMethodNameAttribute()
    {
        return $this->delivery_method ?: ($this->deliveryAddress?->deliveryMethod?->name);
    }

    public function isPaczkomatDelivery()
    {
        return isset($this->delivery_address['type']) && $this->delivery_address['type'] === 'point';
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class)->latest();
    }
}
