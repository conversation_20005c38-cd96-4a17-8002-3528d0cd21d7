<?php

namespace App\Models;

use App\Traits\HasFiltersAndSorts;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Producent extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, HasFiltersAndSorts;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'molos_id',
        'image',
        'priority',
    ];

    /**
     * Filters available for querying.
     *
     * @var array
     */
    protected static $filters = [
        'name' => ['type' => 'text'],
    ];

    /**
     * Sortable attributes.
     *
     * @var array
     */
    protected static $sorts = [
        'name',
        'description',
        'priority'
    ];

    
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    /**
     * Register the media collections.
     *
     * @return void
     */
    public function registerMediaCollections(): void
    {
        // New 'logos' collection for producent logos
        $this->addMediaCollection('logos')->singleFile();
    }

    /**
     * Register media conversions for the 'logos' collection.
     *
     * @param \Spatie\MediaLibrary\MediaCollections\Models\Media|null $media
     * @return void
     */
    public function registerMediaConversions(Media $media = null): void
    {
        // Conversion for preview logo (max 200x200 pixels)
        $this->addMediaConversion('preview')
            ->width(220)
            ->height(220)
            ->sharpen(10);

        // You can add more conversions here if needed
    }

    /**
     * Get the preview logo URL.
     *
     * @return string
     */
    public function getPreviewLogoAttribute(): string
    {
        $media = $this->getFirstMedia('logos');
        if ($media) {
            if ($media->hasGeneratedConversion('preview')) {
                return $media->getUrl('preview');
            }
            return $media->getUrl(); // Fallback to original if conversion not available
        }

        // Fallback image if no logo is available
        return '/img/nophoto.jpg';
    }

    /**
     * Get the full-size logo URL.
     *
     * @return string
     */
    public function getLogoAttribute(): string
    {
        $media = $this->getFirstMedia('logos');
        if ($media) {
            return $media->getUrl();
        }

        // Fallback image if no logo is available
        return '/img/nophoto.jpg';
    }
}
