<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasFiltersAndSorts;

class Property extends Model
{
    use HasFactory, HasFiltersAndSorts;

    protected $fillable = ['name', 'type', 'is_multiple'];

    protected static $filters = [
        'name' => ['type' => 'text'],
        'type' => ['type' => 'text'],
    ];

    protected static $sorts = [
        'name',
        'type',
        'created_at',
    ];

    // Define the relationship with categories
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_property');
    }

    public function options()
    {
        return $this->hasMany(PropertyOption::class);
    }
}
