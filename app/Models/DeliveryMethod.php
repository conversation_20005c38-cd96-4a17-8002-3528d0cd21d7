<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DeliveryMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'type', // 'point' or 'courier'
        'is_active',
        'sort_order',
        'price',
        'min_cart_amount', // for free shipping
        'api_provider', // 'inpost', 'orlen', etc.
        'api_config', // JSON field for API-specific configuration
        'map_config', // JSON field for map display configuration
        'description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'api_config' => 'array',
        'map_config' => 'array',
        'price' => 'decimal:2',
        'min_cart_amount' => 'decimal:2',
    ];

    public static function getFilters()
    {
        return [
            'name' => ['type' => 'text', 'label' => 'Name'],
            'type' => ['type' => 'select', 'label' => 'Type', 'options' => [
                'point' => 'Pickup Point',
                'courier' => 'Courier'
            ]],
            'is_active' => ['type' => 'boolean', 'label' => 'Active'],
        ];
    }

    public static function getSorts()
    {
        return ['name', 'type', 'sort_order', 'price'];
    }

    // Relationships
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function deliveryAddresses()
    {
        return $this->hasMany(DeliveryAddress::class);
    }
} 