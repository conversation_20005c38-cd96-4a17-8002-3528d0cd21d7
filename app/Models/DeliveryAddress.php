<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DeliveryAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'delivery_method_id',
        'name',
        'type',
        'street',
        'building_number',
        'apartment_number',
        'post_code',
        'city',
        'phone',
        'is_default',
        'point_id',
        'point_address',
        'point_data',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'point_data' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function deliveryMethod()
    {
        return $this->belongsTo(DeliveryMethod::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function getFullAddressAttribute()
    {
        if ($this->deliveryMethod && $this->deliveryMethod->type === 'point') {
            $address = '';
            if ($this->point_id) {
                $address .= $this->point_id;
            }
            if ($this->point_address) {
                $address .= ($address ? ' - ' : '') . $this->point_address;
            }
            return $address ?: 'Pickup Point';
        }

        $address = $this->street . ' ' . $this->building_number;
        if ($this->apartment_number) {
            $address .= '/' . $this->apartment_number;
        }
        $address .= ', ' . $this->post_code . ' ' . $this->city;
        
        return $address;
    }
} 