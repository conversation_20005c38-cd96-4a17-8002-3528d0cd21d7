<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductProperty extends Model
{
    use HasFactory;

    protected $fillable = ['product_id', 'property_id', 'property_option_id', 'value'];

    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    public function propertyOption()
    {
        return $this->belongsTo(PropertyOption::class);
    }
}
