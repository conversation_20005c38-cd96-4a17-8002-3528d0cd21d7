<?php

namespace App\Exceptions;

use App\Listeners\MailEventListener;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\Mailer\Exception\TransportException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });

        // Handle mail transport exceptions specifically
        $this->reportable(function (TransportException $e) {
            // Use the MailEventListener for consistent mail error handling
            $listener = new MailEventListener();
            $listener->handleMessageFailed($e);

            // Also log basic error information
            logger()->error('Mail transport error occurred', [
                'error' => $e->getMessage(),
                'mailer' => config('mail.default'),
                'trace' => $e->getTraceAsString(),
            ]);

            // Don't re-throw for mail errors in production to prevent breaking the application
            if (app()->environment('production')) {
                return false;
            }
        });
    }
}
