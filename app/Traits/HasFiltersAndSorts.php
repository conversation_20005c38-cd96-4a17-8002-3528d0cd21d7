<?php

namespace App\Traits;

trait HasFiltersAndSorts
{
    // protected static $filters = [];
    // protected static $sorts = [];

    public static function getFilters()
    {
        return static::$filters;
    }

    public static function getSorts()
    {
        return static::$sorts;
    }

    public static function setFilters(array $filters)
    {
        static::$filters = $filters;
    }

    public static function setSorts(array $sorts)
    {
        static::$sorts = $sorts;
    }
}
