<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;

trait ApiResponseTrait
{
    /**
     * Return a success JSON response.
     *
     * @param string $message
     * @param array $data
     * @param int $statusCode
     * @return JsonResponse
     */
    protected function successResponse($message = '', $data = [], $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ] + $data;

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error JSON response.
     *
     * @param string $message
     * @param array $data
     * @param int $statusCode
     * @return JsonResponse
     */
    protected function errorResponse($message = '', $data = [], $statusCode = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ] + $data;

        return response()->json($response, $statusCode);
    }
}
