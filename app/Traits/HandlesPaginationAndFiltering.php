<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HandlesPaginationAndFiltering
{
    protected function applyPaginationAndFiltering(Builder $query, $perPage = 15)
    {
        // Get the model instance
        $model = $query->getModel();

        // Retrieve filters and sorts from the model
        $filters = method_exists($model, 'getFilters') ? $model::getFilters() : [];
        $sorts = method_exists($model, 'getSorts') ? $model::getSorts() : [];

        // Apply filters and sorting
        $query = $this->applyFilters($query, $filters);
        $query = $this->applySorting($query, $sorts);

        // Paginate the query
        $paginator = $query->paginate($perPage)->appends(request()->query());

        // Return the paginator, filters, and sorts
        return [$paginator, $filters, $sorts];
    }

    protected function applyFilters(Builder $query, array $filters)
    {
        foreach ($filters as $field => $filter) {
            $method = 'apply' . ucfirst($filter['type']) . 'Filter';
            if (method_exists($this, $method)) {
                $query = $this->{$method}($query, $field, $filter);
            }
        }

        return $query;
    }

    protected function applySorting(Builder $query, array $sorts)
    {
        foreach ($sorts as $sort) {
            if (request()->has('sort') && request('sort') === $sort) {
                $direction = request('direction', 'asc');
                $query->orderBy($sort, $direction);
            }
        }

        return $query;
    }

    // Filter application methods...

    protected function applyTextFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field)) {
            $query->where($field, 'like', '%' . request($field) . '%');
        }
        return $query;
    }

    protected function applyRelationFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field)) {
            $relation = $filter['relation'];
            $query->whereHas($relation, function ($q) use ($field) {
                $q->where('name', 'like', '%' . request($field) . '%');
            });
        }
        return $query;
    }

    protected function applyDateFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field)) {
            $query->whereDate($field, request($field));
        }
        return $query;
    }

    protected function applyDaterangeFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field . '_from') && request()->has($field . '_to')) {
            $query->whereBetween($field, [request($field . '_from'), request($field . '_to')]);
        }
        return $query;
    }

    protected function applyNumberFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field)) {
            $query->where($field, request($field));
        }
        return $query;
    }

    protected function applyNumberrangeFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field . '_from') && request()->has($field . '_to')) {
            $query->whereBetween($field, [request($field . '_from'), request($field . '_to')]);
        }
        return $query;
    }
}
