<?php

namespace App\Listeners;

use App\Services\App\CartService;
use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\Cookie;

class MergeGuestCart
{
    protected $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function handle(Login $event)
    {
        $guestCartId = Cookie::get('cart_id');

        if ($guestCartId) {
            $guestCart = \App\Models\Cart::where('id', $guestCartId)->whereNull('user_id')->first();

            if ($guestCart) {
                // Get or create the authenticated user's cart
                $userCart = $this->cartService->getCart();

                // Merge items
                foreach ($guestCart->items as $item) {
                    $this->cartService->addProduct($item->product, $item->quantity);
                }

                // Delete the guest cart
                $guestCart->delete();

                // Remove the guest cart cookie
                Cookie::queue(Cookie::forget('cart_id'));
            }
        }
    }
}
