<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSending;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Mailer\Exception\TransportException;

class MailEventListener
{
    /**
     * Handle message sending event
     *
     * @param MessageSending $event
     * @return void
     */
    public function handleMessageSending(MessageSending $event)
    {
        $message = $event->message;
        $recipients = $this->getRecipients($message);
        
        Log::channel('mail')->info('Email sending initiated', [
            'mailer' => config('mail.default'),
            'subject' => $message->getSubject(),
            'recipients' => $recipients,
            'from' => $this->getFromAddress($message),
            'message_id' => $message->getHeaders()->get('Message-ID')?->getBody(),
        ]);
    }

    /**
     * Handle message sent event
     *
     * @param MessageSent $event
     * @return void
     */
    public function handleMessageSent(MessageSent $event)
    {
        $message = $event->message;
        $recipients = $this->getRecipients($message);
        
        Log::channel('mail')->info('Email sent successfully', [
            'mailer' => config('mail.default'),
            'subject' => $message->getSubject(),
            'recipients' => $recipients,
            'from' => $this->getFromAddress($message),
            'message_id' => $message->getHeaders()->get('Message-ID')?->getBody(),
        ]);

        // Log Postmark-specific information if available
        if (config('mail.default') === 'postmark') {
            $this->logPostmarkSuccess($message, $recipients);
        }
    }

    /**
     * Handle failed email sending
     *
     * @param \Exception $exception
     * @param mixed $message
     * @return void
     */
    public function handleMessageFailed($exception, $message = null)
    {
        $recipients = $message ? $this->getRecipients($message) : 'unknown';
        
        Log::channel('mail')->error('Email sending failed', [
            'mailer' => config('mail.default'),
            'subject' => $message ? $message->getSubject() : 'unknown',
            'recipients' => $recipients,
            'error' => $exception->getMessage(),
            'exception_class' => get_class($exception),
            'trace' => $exception->getTraceAsString(),
        ]);

        // Handle Postmark-specific errors
        if (config('mail.default') === 'postmark') {
            $this->handlePostmarkError($exception, $message, $recipients);
        }
    }

    /**
     * Log Postmark success details
     *
     * @param mixed $message
     * @param array $recipients
     * @return void
     */
    protected function logPostmarkSuccess($message, $recipients)
    {
        Log::channel('mail')->info('Postmark email delivery successful', [
            'service' => 'postmark',
            'subject' => $message->getSubject(),
            'recipients' => $recipients,
            'stream_id' => config('mail.mailers.postmark.message_stream_id'),
        ]);
    }

    /**
     * Handle Postmark-specific errors
     *
     * @param \Exception $exception
     * @param mixed $message
     * @param array $recipients
     * @return void
     */
    protected function handlePostmarkError($exception, $message, $recipients)
    {
        $errorData = [
            'service' => 'postmark',
            'subject' => $message ? $message->getSubject() : 'unknown',
            'recipients' => $recipients,
            'error_message' => $exception->getMessage(),
        ];

        // Check for specific Postmark error types
        if ($exception instanceof TransportException) {
            $errorData['transport_error'] = true;
            
            // Parse common Postmark error codes
            $errorMessage = $exception->getMessage();
            
            if (strpos($errorMessage, '401') !== false) {
                $errorData['error_type'] = 'authentication';
                $errorData['suggestion'] = 'Check POSTMARK_TOKEN configuration';
            } elseif (strpos($errorMessage, '422') !== false) {
                $errorData['error_type'] = 'validation';
                $errorData['suggestion'] = 'Check email format and content';
            } elseif (strpos($errorMessage, '500') !== false) {
                $errorData['error_type'] = 'server_error';
                $errorData['suggestion'] = 'Postmark server error, retry later';
            } elseif (strpos($errorMessage, 'timeout') !== false) {
                $errorData['error_type'] = 'timeout';
                $errorData['suggestion'] = 'Network timeout, check connectivity';
            }
        }

        Log::channel('mail')->error('Postmark email delivery failed', $errorData);

        // Optionally, you could implement retry logic here
        // or send alerts to administrators
    }

    /**
     * Get recipients from message
     *
     * @param mixed $message
     * @return array
     */
    protected function getRecipients($message)
    {
        $recipients = [];
        
        if ($to = $message->getTo()) {
            foreach ($to as $address) {
                $recipients[] = $address->getAddress();
            }
        }
        
        if ($cc = $message->getCc()) {
            foreach ($cc as $address) {
                $recipients[] = $address->getAddress();
            }
        }
        
        if ($bcc = $message->getBcc()) {
            foreach ($bcc as $address) {
                $recipients[] = $address->getAddress();
            }
        }
        
        return $recipients;
    }

    /**
     * Get from address from message
     *
     * @param mixed $message
     * @return string
     */
    protected function getFromAddress($message)
    {
        $from = $message->getFrom();
        return $from ? $from[0]->getAddress() : 'unknown';
    }
}
