<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\FullImportJob;
use App\Services\MolosApiService;
use App\Services\MolosCategories;
use App\Services\MolosProducents;
use App\Services\MolosProducts;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;

class ImportController extends Controller
{
    //
    public function index()
    {
        try {
            // dd('test');
            // Check Redis connection
            Redis::ping();
            
            // If Redis is connected, dispatch the job
            FullImportJob::dispatch();
            
            return redirect()->back()->with('success', 'Import job has been queued successfully.');
        } catch (\RedisException $e) {
            return redirect()->back()->with('error', 'Redis connection failed. Please check your Redis configuration.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }
}
