<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Builder;

class AdminController extends Controller
{
    protected function applyPaginationAndFiltering(Builder $query, $perPage = 15)
    {
        // Get the model instance
        $model = $query->getModel();

        // Retrieve filters and sorts from the model
        $filters = method_exists($model, 'getFilters') ? $model::getFilters() : [];
        $sorts = method_exists($model, 'getSorts') ? $model::getSorts() : [];

        // Apply filters and sorting
        $query = $this->applyFilters($query, $filters);
        $query = $this->applySorting($query, $sorts);

        // Paginate the query
        $paginator = $query->paginate($perPage)->appends(request()->query());

        // Return the paginator, filters, and sorts
        return [$paginator, $filters, $sorts];
    }

    protected function applyFilters(Builder $query, array $filters)
    {
        foreach ($filters as $field => $filter) {
            // Skip if the field is empty in the request
            if (!request()->filled($field) && 
                !request()->filled($field . '_from') && 
                !request()->filled($field . '_to')) {
                continue;
            }

            // Skip if the field value is empty
            if (request()->has($field) && request($field) === '') {
                continue;
            }

            $method = 'apply' . ucfirst($filter['type']) . 'Filter';
            if (method_exists($this, $method)) {
                $query = $this->{$method}($query, $field, $filter);
            }
        }

        return $query;
    }

    protected function applySorting(Builder $query, array $sorts)
    {
        foreach ($sorts as $sort) {
            if (request()->has('sort') && request('sort') === $sort) {
                $direction = request('direction', 'asc');
                $query->orderBy($sort, $direction);
            }
        }

        return $query;
    }

    // Filter application methods

    protected function applyTextFilter(Builder $query, $field, $filter)
    {
        if (request()->filled($field)) {
            $value = request($field);
            $query->where($field, 'like', '%' . $value . '%');
        }
        return $query;
    }

    protected function applyRelationFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field) && request($field) !== '') {
            $relation = $filter['relation'];
            $value = request($field);

            // Special handling for category filter
            if ($field === 'category_id') {
                $query->whereHas($relation, function ($q) use ($value) {
                    // Get all subcategory IDs
                    $subcategoryIds = \App\Models\Category::where('parent_id', $value)
                        ->pluck('id')
                        ->toArray();
                    
                    // Include both the selected category and its subcategories
                    $categoryIds = array_merge([$value], $subcategoryIds);
                    
                    $q->whereIn('id', $categoryIds);
                });
            } else {
                // Default relation filter behavior
                $query->whereHas($relation, function ($q) use ($field) {
                    $q->where('id', request($field));
                });
            }
        }
        return $query;
    }

    protected function applyDateFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field)) {
            $query->whereDate($field, request($field));
        }
        return $query;
    }

    protected function applyDaterangeFilter(Builder $query, $field, $filter)
    {
        $from = request($field . '_from');
        $to = request($field . '_to');

        if (filled($from)) {
            $query->whereDate($field, '>=', $from);
        }
        if (filled($to)) {
            $query->whereDate($field, '<=', $to);
        }

        return $query;
    }

    protected function applyNumberFilter(Builder $query, $field, $filter)
    {
        if (request()->has($field)) {
            $query->where($field, request($field));
        }
        return $query;
    }

    protected function applyNumberrangeFilter(Builder $query, $field, $filter)
    {
        $from = request($field . '_from');
        $to = request($field . '_to');

        if (filled($from)) {
            $query->where($field, '>=', $from);
        }
        if (filled($to)) {
            $query->where($field, '<=', $to);
        }

        return $query;
    }
}
