<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Producent;
use App\Traits\HandlesPaginationAndFiltering;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProducentController extends AdminController
{
    use HandlesPaginationAndFiltering;

    /**
     * Display a listing of the producents.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        list($producents, $filters, $sorts) = $this->applyPaginationAndFiltering(Producent::query());

        return view('admin.producents.index', compact('producents', 'filters', 'sorts'));
    }

    /**
     * Show the form for editing the specified producent.
     *
     * @param \App\Models\Producent $producent
     * @return \Illuminate\View\View
     */
    public function edit(Producent $producent)
    {
        return view('admin.producents.edit', compact('producent'));
    }

    /**
     * Update the specified producent in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Producent $producent
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Producent $producent)
    {
        // Validate the incoming request data
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'nullable|boolean',
            'image' => 'nullable|image|max:2048',
            'logo' => 'nullable|image|max:2048', // New validation for logo
        ]);

        // Update producent details
        $producent->update([
            'name' => $request->name,
            'description' => $request->description,
            'slug' => Str::slug($request->name),
            'priority' => $request->has('priority') ? true : false,
        ]);

        // Handle 'image' upload if present
        if ($request->hasFile('image')) {
            // Clear existing images in the 'images' collection
            $producent->clearMediaCollection('images');

            // Add the new image to the 'images' collection
            $producent->addMedia($request->file('image'))
                      ->withResponsiveImages()
                      ->toMediaCollection('images');
        }

        // Handle 'logo' upload if present
        if ($request->hasFile('logo')) {
            // Clear existing logos in the 'logos' collection
            $producent->clearMediaCollection('logos');

            // Add the new logo to the 'logos' collection
            $producent->addMedia($request->file('logo'))
                      ->withResponsiveImages()
                      ->toMediaCollection('logos');
        }

        // Redirect based on the action button clicked
        if ($request->input('action') === 'save') {
            return redirect()->route('admin.producents.index')->with('success', 'Producent updated successfully.');
        }

        return redirect()->route('admin.producents.edit', $producent->id)->with('success', 'Producent updated successfully.');
    }
}
