<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\GenerateProductDimensions;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProductDimensionController extends Controller
{
    /**
     * Show the dimension generation page
     */
    public function index()
    {
        // Get statistics for the page
        $totalProducts = Product::count();
        $productsWithDimensions = Product::whereNotNull('length')
            ->whereNotNull('width')
            ->whereNotNull('height')
            ->count();
        $productsWithoutDimensions = $totalProducts - $productsWithDimensions;

        // Get products with reasonable dimensions (additional validation)
        $productsWithReasonableDimensions = Product::whereNotNull('length')
            ->whereNotNull('width')
            ->whereNotNull('height')
            ->where('length', '>', 0.5)
            ->where('length', '<=', 200)
            ->where('width', '>', 0.5)
            ->where('width', '<=', 200)
            ->where('height', '>', 0.5)
            ->where('height', '<=', 200)
            ->where('weight', '>', 0)
            ->where('weight', '<=', 100)
            ->count();

        $stats = [
            'total_products' => $totalProducts,
            'products_with_dimensions' => $productsWithDimensions,
            'products_without_dimensions' => $productsWithoutDimensions,
            'products_with_reasonable_dimensions' => $productsWithReasonableDimensions,
            'products_with_invalid_dimensions' => $productsWithDimensions - $productsWithReasonableDimensions,
        ];

        return view('admin.product-dimensions.index', compact('stats'));
    }

    /**
     * Generate dimensions using the job
     */
    public function generate(Request $request)
    {
        $request->validate([
            'process_all' => 'boolean',
            'batch_size' => 'integer|min:10|max:100',
        ]);

        $processAll = $request->boolean('process_all', false);
        $batchSize = $request->integer('batch_size', 50);

        // Check if there are products that need dimensions
        $productsNeedingDimensions = Product::where(function ($query) {
            $query->whereNull('length')
                  ->orWhereNull('width')
                  ->orWhereNull('height');
        })->count();

        if ($productsNeedingDimensions === 0) {
            return redirect()->route('admin.product-dimensions.index')
                           ->with('info', 'All products already have dimensions. No processing needed.');
        }

        try {
            // Dispatch the job
            GenerateProductDimensions::dispatch($processAll, $batchSize);

            Log::info('GenerateProductDimensions job dispatched', [
                'process_all' => $processAll,
                'batch_size' => $batchSize,
                'products_needing_dimensions' => $productsNeedingDimensions,
                'user_id' => auth()->id(),
            ]);

            $message = $processAll
                ? "Dimension generation started for all {$productsNeedingDimensions} products! This may take several minutes. Check the logs for progress."
                : "Single batch dimension generation started for up to {$batchSize} products! Check the logs for progress.";

            return redirect()->route('admin.product-dimensions.index')
                           ->with('success', $message);

        } catch (\Exception $e) {
            Log::error('Failed to dispatch GenerateProductDimensions job', [
                'error' => $e->getMessage(),
                'process_all' => $processAll,
                'batch_size' => $batchSize,
                'user_id' => auth()->id(),
            ]);

            return redirect()->route('admin.product-dimensions.index')
                           ->with('error', 'Failed to start dimension generation: ' . $e->getMessage());
        }
    }

    /**
     * Get generation status (for AJAX polling if needed)
     */
    public function status()
    {
        // Get current statistics
        $totalProducts = Product::count();
        $productsWithDimensions = Product::whereNotNull('length')
            ->whereNotNull('width')
            ->whereNotNull('height')
            ->count();
        $productsWithoutDimensions = $totalProducts - $productsWithDimensions;

        // Get products with reasonable dimensions
        $productsWithReasonableDimensions = Product::whereNotNull('length')
            ->whereNotNull('width')
            ->whereNotNull('height')
            ->where('length', '>', 0.5)
            ->where('length', '<=', 200)
            ->where('width', '>', 0.5)
            ->where('width', '<=', 200)
            ->where('height', '>', 0.5)
            ->where('height', '<=', 200)
            ->where('weight', '>', 0)
            ->where('weight', '<=', 100)
            ->count();

        return response()->json([
            'total_products' => $totalProducts,
            'products_with_dimensions' => $productsWithDimensions,
            'products_without_dimensions' => $productsWithoutDimensions,
            'products_with_reasonable_dimensions' => $productsWithReasonableDimensions,
            'products_with_invalid_dimensions' => $productsWithDimensions - $productsWithReasonableDimensions,
            'completion_percentage' => $totalProducts > 0 ? round(($productsWithReasonableDimensions / $totalProducts) * 100, 1) : 0,
        ]);
    }

    /**
     * Show products without dimensions (for debugging/monitoring)
     */
    public function showProductsWithoutDimensions(Request $request)
    {
        $query = Product::with(['category', 'producent'])
            ->where(function ($q) {
                $q->whereNull('length')
                  ->orWhereNull('width')
                  ->orWhereNull('height');
            });

        // Add search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        $products = $query->orderBy('created_at', 'desc')
                         ->paginate(50)
                         ->appends($request->query());

        return view('admin.product-dimensions.products-without-dimensions', compact('products'));
    }

    /**
     * Show products with invalid dimensions (for debugging/monitoring)
     */
    public function showProductsWithInvalidDimensions(Request $request)
    {
        $query = Product::with(['category', 'producent'])
            ->whereNotNull('length')
            ->whereNotNull('width')
            ->whereNotNull('height')
            ->where(function ($q) {
                $q->where('length', '<=', 0.5)
                  ->orWhere('length', '>', 200)
                  ->orWhere('width', '<=', 0.5)
                  ->orWhere('width', '>', 200)
                  ->orWhere('height', '<=', 0.5)
                  ->orWhere('height', '>', 200)
                  ->orWhere('weight', '<=', 0)
                  ->orWhere('weight', '>', 100);
            });

        // Add search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        $products = $query->orderBy('updated_at', 'desc')
                         ->paginate(50)
                         ->appends($request->query());

        return view('admin.product-dimensions.products-with-invalid-dimensions', compact('products'));
    }
}
