<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\App\OrderService;
use App\Services\InPostService;
use App\Services\PayuService;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    protected $orderService;
    protected $inPostService;
    protected $payuService;

    public function __construct(OrderService $orderService, InPostService $inPostService, PayuService $payuService)
    {
        $this->orderService = $orderService;
        $this->inPostService = $inPostService;
        $this->payuService = $payuService;
    }

    public function index()
    {
        $orders = Order::with(['user'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.orders.index', compact('orders'));
    }

    public function show(Order $order)
    {
        $order->load(['user', 'items.product', 'deliveryAddress']);
        // dd($order, $order->deliveryAddress);
        return view('admin.orders.show', compact('order'));
    }

    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,completed,cancelled'
        ]);

        $order->update([
            'status' => $request->status
        ]);

        return redirect()->back()->with('success', 'Order status updated successfully.');
    }

    public function generateInPostLabel(Order $order)
    {
        try {
            // Check if order is eligible for InPost shipping
            if (!$order->deliveryAddress || 
                !$order->deliveryAddress->deliveryMethod || 
                $order->deliveryAddress->deliveryMethod->api_provider !== 'inpost') {
                return redirect()->back()->with('error', 'This order does not use InPost shipping method.');
            }

            // Generate the label
            $shipment = $this->orderService->regenerateShippingLabel($order);

            return redirect()->back()->with('success', 'InPost shipping label generated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to generate InPost label: ' . $e->getMessage());
        }
    }

    public function downloadInPostLabel(Order $order)
    {
        try {
            // Check if order has InPost shipment
            if (!$order->inpost_shipment_id) {
                return redirect()->back()->with('error', 'Order does not have an InPost shipment.');
            }

            // Additional check for InPost delivery method
            if (!$order->deliveryAddress || 
                !$order->deliveryAddress->deliveryMethod || 
                $order->deliveryAddress->deliveryMethod->api_provider !== 'inpost') {
                return redirect()->back()->with('error', 'This order does not use InPost shipping method.');
            }

            // Get the label PDF
            $labelContent = $this->inPostService->getLabel($order->inpost_shipment_id, 'pdf');

            // Check if we have multiple shipments (contains comma)
            $isMultiple = strpos($order->inpost_shipment_id, ',') !== false;
            
            if ($isMultiple) {
                // Multiple parcels - return ZIP file
                return response($labelContent)
                    ->header('Content-Type', 'application/zip')
                    ->header('Content-Disposition', 'attachment; filename="inpost-labels-' . $order->uuid . '.zip"');
            } else {
                // Single parcel - return PDF
                return response($labelContent)
                    ->header('Content-Type', 'application/pdf')
                    ->header('Content-Disposition', 'attachment; filename="inpost-label-' . $order->uuid . '.pdf"');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to download InPost label: ' . $e->getMessage());
        }
    }

    public function checkPaymentStatus(Order $order)
    {
        try {
            // Check if order uses PayU payment method
            if ($order->payment_method !== 'payu') {
                return redirect()->back()->with('error', 'This order does not use PayU payment method.');
            }

            // Check if we have a PayU order ID
            if (!$order->payu_order_id) {
                return redirect()->back()->with('error', 'No PayU order ID found for this order.');
            }

            // Get payment status from PayU
            $paymentStatus = $this->payuService->getOrderStatus($order->payu_order_id);
            
            // Update order with payment status
            $order->update([
                'payment_status' => strtolower($paymentStatus['orders'][0]['status'] ?? 'unknown'),
                'payment_checked_at' => now(),
            ]);

            return redirect()->back()->with('success', 'Payment status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to check payment status: ' . $e->getMessage());
        }
    }

    /**
     * Purchase InPost label by retrieving offers and purchasing the first available one
     */
    public function purchaseLabel(Order $order)
    {
        try {
            // Check if order has InPost shipment
            if (!$order->inpost_shipment_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order does not have an InPost shipment.'
                ], 400);
            }

            // Check if order already has tracking number
            if ($order->inpost_tracking_number) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order already has a tracking number.'
                ], 400);
            }

            // Additional check for InPost delivery method
            if (!$order->deliveryAddress ||
                !$order->deliveryAddress->deliveryMethod ||
                $order->deliveryAddress->deliveryMethod->api_provider !== 'inpost') {
                return response()->json([
                    'success' => false,
                    'message' => 'This order does not use InPost shipping method.'
                ], 400);
            }

            $trackingNumbers = [];
            $shipmentIds = explode(',', $order->inpost_shipment_id);

            // Handle multiple shipments (paczkomat deliveries)
            foreach ($shipmentIds as $shipmentId) {
                $shipmentId = trim($shipmentId);
                if (empty($shipmentId)) continue;

                // First, check if shipment already has tracking number to avoid unnecessary waiting
                $purchaseStatus = $this->inPostService->isShipmentPurchased($shipmentId);

                if ($purchaseStatus['is_purchased'] && $purchaseStatus['has_tracking_number']) {
                    // Shipment already has tracking number - extract it immediately
                    \Log::info('Shipment already has tracking number, skipping offer waiting and purchase', [
                        'shipment_id' => $shipmentId,
                        'tracking_number' => $purchaseStatus['tracking_number']
                    ]);

                    $trackingNumbers[] = $purchaseStatus['tracking_number'];

                    // Also check for multiple parcels in the tracking
                    if (!empty($purchaseStatus['parcels'])) {
                        foreach ($purchaseStatus['parcels'] as $parcel) {
                            if (isset($parcel['tracking_number']) && $parcel['tracking_number'] !== $purchaseStatus['tracking_number']) {
                                $trackingNumbers[] = $parcel['tracking_number'];
                            }
                        }
                    }

                    continue; // Skip to next shipment
                }

                // Shipment needs purchase - check if offers are ready, wait if necessary
                $offersReady = $this->inPostService->waitForOffers($shipmentId, 30, 2);

                if (!$offersReady) {
                    throw new \Exception("Offers are not ready for shipment {$shipmentId}. Please try again in a few minutes.");
                }

                // Get available offers for this shipment
                $offers = $this->inPostService->getShipmentOffers($shipmentId);

                if (empty($offers)) {
                    throw new \Exception("No offers available for shipment {$shipmentId}. The shipment may still be processing or may have expired offers.");
                }

                // Select the first available offer
                $selectedOffer = null;
                foreach ($offers as $offer) {
                    if (isset($offer['status']) && in_array($offer['status'], ['available', 'selected'])) {
                        $selectedOffer = $offer;
                        break;
                    }
                }

                if (!$selectedOffer) {
                    // Log available offers for debugging
                    \Log::warning('No available offers found for shipment', [
                        'shipment_id' => $shipmentId,
                        'offers' => $offers
                    ]);
                    throw new \Exception("No available offers found for shipment {$shipmentId}. Available statuses: " .
                        implode(', ', array_column($offers, 'status')));
                }

                // Purchase the selected offer (or get existing tracking if already purchased)
                $purchaseResult = $this->inPostService->purchaseLabel($shipmentId, $selectedOffer['id']);

                // Log whether this was a new purchase or existing tracking
                if (isset($purchaseResult['already_purchased']) && $purchaseResult['already_purchased']) {
                    \Log::info('Retrieved existing tracking number for shipment', [
                        'shipment_id' => $shipmentId,
                        'tracking_number' => $purchaseResult['tracking_number'] ?? null
                    ]);
                } elseif (isset($purchaseResult['newly_purchased']) && $purchaseResult['newly_purchased']) {
                    \Log::info('Successfully purchased new label for shipment', [
                        'shipment_id' => $shipmentId,
                        'tracking_number' => $purchaseResult['tracking_number'] ?? null
                    ]);
                }

                // Extract tracking number from purchase result
                if (isset($purchaseResult['tracking_number'])) {
                    $trackingNumbers[] = $purchaseResult['tracking_number'];
                } elseif (isset($purchaseResult['parcels']) && is_array($purchaseResult['parcels'])) {
                    // Handle multiple parcels in single shipment (courier deliveries)
                    foreach ($purchaseResult['parcels'] as $parcel) {
                        if (isset($parcel['tracking_number'])) {
                            $trackingNumbers[] = $parcel['tracking_number'];
                        }
                    }
                }
            }

            if (empty($trackingNumbers)) {
                throw new \Exception('No tracking numbers were returned from the purchase');
            }

            // Update order with tracking numbers
            $trackingNumbersString = implode(',', $trackingNumbers);
            $order->update([
                'inpost_tracking_number' => $trackingNumbersString
            ]);

            // Determine appropriate success message
            $hasExistingTracking = false;
            $hasNewPurchase = false;

            // Check if we had any already purchased shipments
            foreach ($shipmentIds as $shipmentId) {
                $shipmentId = trim($shipmentId);
                if (empty($shipmentId)) continue;

                // This is a simple way to check - in a more complex scenario you might want to track this per shipment
                if (strpos($shipmentId, ',') !== false) {
                    // Multiple shipments - assume mixed scenario possible
                    $hasNewPurchase = true;
                } else {
                    $hasNewPurchase = true;
                }
            }

            $message = count($trackingNumbers) > 1
                ? 'Labels processed successfully! Retrieved ' . count($trackingNumbers) . ' tracking numbers.'
                : 'Label processed successfully!';

            return response()->json([
                'success' => true,
                'message' => $message,
                'tracking_numbers' => $trackingNumbers,
                'tracking_numbers_string' => $trackingNumbersString
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to purchase InPost label', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to purchase label: ' . $e->getMessage()
            ], 500);
        }
    }
}
