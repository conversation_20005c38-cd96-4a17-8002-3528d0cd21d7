<?php

namespace App\Http\Controllers\Admin;

use App\Models\DeliveryMethod;
use Illuminate\Http\Request;

class DeliveryMethodController extends AdminController
{
    public function index()
    {
        list($methods, $filters, $sorts) = $this->applyPaginationAndFiltering(DeliveryMethod::query());
        return view('admin.delivery-methods.index', compact('methods', 'filters', 'sorts'));
    }

    public function create()
    {
        return view('admin.delivery-methods.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|unique:delivery_methods',
            'type' => 'required|in:point,courier',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
            'price' => 'required|numeric|min:0',
            'min_cart_amount' => 'nullable|numeric|min:0',
            'api_provider' => 'nullable|string',
            'api_config' => 'nullable|json',
            'map_config' => 'nullable|json',
            'description' => 'nullable|string',
        ]);

        DeliveryMethod::create($validated);
        return redirect()->route('admin.delivery-methods.index')
            ->with('success', 'Delivery method created successfully');
    }

    public function edit(DeliveryMethod $deliveryMethod)
    {
        return view('admin.delivery-methods.edit', compact('deliveryMethod'));
    }

    public function update(Request $request, DeliveryMethod $deliveryMethod)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|unique:delivery_methods,code,' . $deliveryMethod->id,
            'type' => 'required|in:point,courier',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
            'price' => 'required|numeric|min:0',
            'min_cart_amount' => 'nullable|numeric|min:0',
            'api_provider' => 'nullable|string',
            'api_config' => 'nullable|json',
            'map_config' => 'nullable|json',
            'description' => 'nullable|string',
        ]);

        $deliveryMethod->update($validated);
        return redirect()->route('admin.delivery-methods.index')
            ->with('success', 'Delivery method updated successfully');
    }

    public function destroy(DeliveryMethod $deliveryMethod)
    {
        if ($deliveryMethod->orders()->exists()) {
            return back()->with('error', 'Cannot delete delivery method with associated orders');
        }
        
        $deliveryMethod->delete();
        return redirect()->route('admin.delivery-methods.index')
            ->with('success', 'Delivery method deleted successfully');
    }
} 