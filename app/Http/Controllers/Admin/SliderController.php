<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Slider;
use Illuminate\Http\Request;

class SliderController extends AdminController
{
    public function index(Request $request)
    {
        list($sliders, $filters, $sorts) = $this->applyPaginationAndFiltering(Slider::query());

        return view('admin.sliders.index', compact('sliders', 'filters', 'sorts'));
    }

    public function create()
    {
        return view('admin.sliders.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'link' => 'nullable|url|max:255',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            // 'image' => 'required|image',
            // 'mobile_image' => 'nullable|image',
        ]);

        $slider = Slider::create([
            'title' => $request->title,
            'link' => $request->link,
            'is_active' => $request->is_active ?? false,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
        ]);
        
        if ($request->hasFile('image')) {
            $slider->addMedia($request->file('image'))->toMediaCollection('image');
        }

        if ($request->hasFile('mobile_image')) {
            $slider->addMedia($request->file('mobile_image'))->toMediaCollection('mobile_image');
        }

        return redirect()->route('admin.sliders.index')->with('success', 'Slider created successfully.');
    }

    public function edit(Slider $slider)
    {
        return view('admin.sliders.edit', compact('slider'));
    }

    public function update(Request $request, Slider $slider)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'link' => 'nullable|url|max:255',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'image' => 'nullable|image|max:2048',
            'mobile_image' => 'nullable|image|max:2048',
        ]);

        $slider->update([
            'title' => $request->title,
            'link' => $request->link,
            'is_active' => $request->is_active ?? false,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
        ]);

        if ($request->hasFile('image')) {
            $slider->clearMediaCollection('image');
            $slider->addMedia($request->file('image'))->toMediaCollection('image');
        }

        if ($request->hasFile('mobile_image')) {
            $slider->clearMediaCollection('mobile_image');
            $slider->addMedia($request->file('mobile_image'))->toMediaCollection('mobile_image');
        }

        return redirect()->route('admin.sliders.index')->with('success', 'Slider updated successfully.');
    }

    public function destroy(Slider $slider)
    {
        $slider->clearMediaCollection('image');
        $slider->clearMediaCollection('mobile_image');
        $slider->delete();

        return redirect()->route('admin.sliders.index')->with('success', 'Slider deleted successfully.');
    }
}
