<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends AdminController
{
    public function index()
    {
        list($users, $filters, $sorts) = $this->applyPaginationAndFiltering(User::query());

        return view('admin.users.index', compact('users', 'filters', 'sorts'));
    }

    public function show(User $user)
    {
        $user->load(['deliveryAddresses', 'orders']);
        return view('admin.users.show', compact('user'));
    }
} 