<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends AdminController
{
    public function index(Request $request)
    {
        $query = User::with(['orders']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');

        $allowedSorts = ['id', 'first_name', 'email', 'created_at'];
        if (in_array($sortField, $allowedSorts)) {
            $query->orderBy($sortField, $sortDirection);
        }

        // Get pagination size
        $perPage = $request->get('per_page', 15);
        $allowedPerPage = [15, 25, 50];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 15;
        }

        $users = $query->withCount('orders')->paginate($perPage)->appends($request->query());

        return view('admin.users.index', compact('users'));
    }

    public function show(User $user)
    {
        $user->load(['deliveryAddresses', 'orders']);
        return view('admin.users.show', compact('user'));
    }
} 