<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Notifications\OrderStatusChanged;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Mail\Markdown;
use Illuminate\Support\Facades\View;

class EmailPreviewController extends Controller
{
    /**
     * Show the email preview dashboard
     */
    public function index()
    {
        // Get sample orders for preview
        $orders = Order::with(['items.product', 'user', 'deliveryAddress.deliveryMethod'])
            ->latest()
            ->take(10)
            ->get();
            
        // Available email templates
        $templates = [
            'order-status' => 'Order Status Notification',
            'promotion' => 'Promotional Email'
        ];
        
        // Available statuses for order emails
        $statuses = [
            'paid' => 'Opłacone',
            'processing' => 'W realizacji',
            'shipped' => 'Wysłane',
            'delivered' => 'Dostarczone',
            'cancelled' => 'Anulowane'
        ];
        
        return view('admin.email-preview.index', compact('orders', 'templates', 'statuses'));
    }
    
    /**
     * Preview a specific email template
     */
    public function preview(Request $request)
    {
        $request->validate([
            'template' => 'required|string|in:order-status,promotion',
            'order_id' => 'required_if:template,order-status|exists:orders,id',
            'status' => 'required_if:template,order-status|string'
        ]);
        
        $template = $request->input('template');
        $markdown = new Markdown(view(), config('mail.markdown'));
        
        if ($template === 'order-status') {
            $order = Order::with(['items.product', 'user', 'deliveryAddress.deliveryMethod'])
                ->findOrFail($request->input('order_id'));
                
            $status = $request->input('status');
            
            // Use the notification class to generate the email
            $message = (new OrderStatusChanged($order, $status))->toMail($order->user);
            
            // Render the email template with the notification data
            return $markdown->render('emails.orders.status-notification', [
                'order' => $order,
                'status' => $status
            ]);
        } elseif ($template === 'promotion') {
            // For promotional emails, directly render the template
            return $markdown->render('emails.promotions.new-year-discount', [
                'discountPercent' => 20,
                'promoCode' => 'NEWYEAR2023',
                'expiryDate' => '31.01.2023',
                'shopUrl' => url('/shop')
            ]);
        }
        
        return back()->with('error', 'Invalid template selected');
    }
}
