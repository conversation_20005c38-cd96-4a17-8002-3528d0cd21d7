<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Traits\HandlesPaginationAndFiltering;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Http\Controllers\Admin\AdminController;
use Illuminate\Support\Facades\DB;
use App\Models\Product;

class CategoryController extends AdminController
{
    public function index(Request $request, $parentId = null)
    {
        // $hierarchicallyCategories = Category::getCategoriesHierarchically();
        // foreach($hierarchicallyCategories as $category) {
        //     echo $category->name . '<br/>';
        // }
        // dd('1');
        $query = Category::where('parent_id', $parentId);

        $parentCategory = $parentId ? Category::find($parentId) : null;        
        list($categories, $filters, $sorts) = $this->applyPaginationAndFiltering($query);

        return view('admin.categories.index', compact('categories', 'filters', 'sorts', 'parentCategory'));
    }

    public function create()
    {
        $categories = Category::all();
        return view('admin.categories.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'sort' => 'nullable|integer',
            'price_percentage' => 'nullable|integer|min:0|max:1000',
        ]);
    
        Category::create([
            'name' => $request->name,
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'slug' => Str::slug($request->name),
            'path' => $request->path,
            'sort' => $request->sort ?? 100,
            'price_percentage' => $request->price_percentage,
            'is_visible' => $request->has('is_visible'),
        ]);
    
        return redirect()->route('admin.categories.index')->with('success', 'Category created successfully.');
    }

    public function edit(Category $category)
    {
        $categories = Category::all();
        return view('admin.categories.edit', compact('category', 'categories'));
    }

    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'sort' => 'nullable|integer',
            'price_percentage' => 'nullable|integer|min:0|max:1000',
        ]);

        $oldPercentage = $category->price_percentage;
    
        // dd($request->price_percentage);
        $category->update([
            'name' => $request->name,
            'description' => $request->description,
            'slug' => Str::slug($request->name),
            'path' => $request->path,
            'parent_id' => $request->parent_id,
            'sort' => $request->sort ?? 100,
            // 'price_percentage' => $request->price_percentage,
            'is_visible' => $request->has('is_visible'),
        ]);

        // Update product prices if percentage changed and products don't have individual percentages
        // if ($oldPercentage !== $request->price_percentage) {
        //     $category->products()
        //         ->whereNull('price_percentage')
        //         ->chunk(100, function ($products) {
        //             foreach ($products as $product) {
        //                 $product->updatePriceFromPercentage();
        //             }
        //         });
        // }
    
        return redirect()->route('admin.categories.index')->with('success', 'Category updated successfully.');
    }

    public function destroy(Category $category)
    {
        try {
            DB::beginTransaction();

            // Get parent category
            $parentCategory = $category->parent;

            // If no parent category exists, move products to uncategorized
            if (!$parentCategory) {
                $parentCategory = Category::firstOrCreate(
                    ['slug' => 'uncategorized'],
                    [
                        'name' => 'Uncategorized',
                        'slug' => 'uncategorized',
                        'is_visible' => false,
                    ]
                );
            }

            // Move all products to parent category
            Product::where('category_id', $category->id)
                ->update(['category_id' => $parentCategory->id]);

            // Delete the category
            $category->delete();

            DB::commit();
            return redirect()
                ->route('admin.categories.index', ['parentId' => $parentCategory->id])
                ->with('success', 'Category deleted and products moved successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()
                ->back()
                ->with('error', 'Failed to delete category: ' . $e->getMessage());
        }
    }

    public function deleteWithChildren(Category $category)
    {
        try {
            DB::beginTransaction();

            // Get all descendant categories
            $descendantIds = $this->getAllDescendantIds($category);

            // Move all products from descendant categories to the current category
            Product::whereIn('category_id', $descendantIds)
                ->update(['category_id' => $category->id]);

            // Delete all descendant categories
            Category::whereIn('id', $descendantIds)->delete();

            DB::commit();
            return redirect()
                ->route('admin.categories.index', ['parentId' => $category->parent_id])
                ->with('success', 'All child categories deleted and products moved successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()
                ->back()
                ->with('error', 'Failed to delete categories: ' . $e->getMessage());
        }
    }

    protected function getAllDescendantIds(Category $category)
    {
        $ids = [];
        $this->getDescendantIdsRecursive($category, $ids);
        return $ids;
    }

    protected function getDescendantIdsRecursive(Category $category, &$ids)
    {
        foreach ($category->children as $child) {
            $ids[] = $child->id;
            $this->getDescendantIdsRecursive($child, $ids);
        }
    }
}
