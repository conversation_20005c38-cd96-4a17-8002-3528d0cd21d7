<?php

namespace App\Http\Controllers\Profile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Auth;
use App\Models\DeliveryAddress;

class AddressController extends Controller
{
    public function list(Request $request)
    {
        $addresses = Auth::user()
            ->deliveryAddresses()
            ->with('deliveryMethod') // Load delivery method relationship
            ->withCount('orders') // Count related orders
            ->orderBy('is_default', 'desc') // Show default first
            ->orderBy('id', 'desc')
            ->paginate(10);

        return view('app.pages.profile.addresses', compact('addresses'));
    }

    public function delete(Request $request, DeliveryAddress $delivery_address)
    {
        if (Auth::id() != $delivery_address->user_id) {
            return redirect('/');
        }

        // Check if this address has any orders
        if ($delivery_address->orders()->count() > 0) {
            return redirect()->back()->with('error', 'Nie można usunąć adresu który ma przypisane zamówienia.');
        }

        // If this was the default address, set another one as default
        if ($delivery_address->is_default) {
            $nextAddress = Auth::user()->deliveryAddresses()
                ->where('id', '!=', $delivery_address->id)
                ->first();
            
            if ($nextAddress) {
                $nextAddress->update(['is_default' => true]);
            }
        }

        $delivery_address->delete();
        
        return redirect()->back()->with('success', 'Adres został usunięty.');
    }

    public function setDefault(Request $request, DeliveryAddress $delivery_address)
    {
        if (Auth::id() != $delivery_address->user_id) {
            return redirect('/');
        }

        // Remove default from all other addresses
        Auth::user()->deliveryAddresses()
            ->where('id', '!=', $delivery_address->id)
            ->update(['is_default' => false]);

        // Set this address as default
        $delivery_address->update(['is_default' => true]);

        return redirect()->back()->with('success', 'Adres został ustawiony jako domyślny.');
    }
}
