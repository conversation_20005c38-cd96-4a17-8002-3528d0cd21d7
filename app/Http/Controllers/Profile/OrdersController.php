<?php

namespace App\Http\Controllers\Profile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Auth;
use App\Models\Order;

class OrdersController extends Controller
{
    public function list(Request $request)
    {
        $orders = Auth::user()
            ->orders()
            ->with(['items.product', 'deliveryAddress.deliveryMethod']) // Load relationships
            ->orderBy('id', 'desc')
            ->paginate(10);

        return view('app.pages.profile.orders', compact('orders'));
    }

    public function details(Request $request, $uuid)
    {
        $order = Auth::user()
            ->orders()
            ->with(['items.product', 'deliveryAddress.deliveryMethod'])
            ->where('uuid', $uuid)
            ->firstOrFail();

        return view('app.pages.profile.order-details', compact('order'));
    }

    public function cancel(Request $request, $uuid)
    {
        $order = Auth::user()
            ->orders()
            ->where('uuid', $uuid)
            ->where('status', '!=', 'completed')
            ->where('status', '!=', 'cancelled')
            ->firstOrFail();

        $order->update([
            'status' => 'cancelled'
        ]);

        return redirect()->route('profile.order.list')
            ->with('success', 'Zamówienie zostało anulowane.');
    }

    public function getStatusClass($status)
    {
        return match($status) {
            'pending' => 'pending',
            'processing' => 'processing', 
            'completed' => 'completed',
            'cancelled' => 'cancelled',
            default => 'pending'
        };
    }

    public function getStatusName($status)
    {
        return match($status) {
            'pending' => 'Oczekujące',
            'processing' => 'W realizacji',
            'completed' => 'Zakończone',
            'cancelled' => 'Anulowane',
            default => 'Nieznany'
        };
    }

    public function getPaymentStatusName($paymentStatus)
    {
        return match($paymentStatus) {
            'pending' => 'Oczekująca',
            'completed' => 'Opłacone',
            'failed' => 'Nieudana',
            'refunded' => 'Zwrócona',
            default => 'Nieznany'
        };
    }

    public function continuePayment(Request $request, $uuid)
    {
        $order = Auth::user()
            ->orders()
            ->where('uuid', $uuid)
            ->where(function($query) {
                $query->where('payment_status', 'pending')
                      ->orWhere('payment_status', 'new')
                      ->orWhere('payment_status', 'failed');
            })
            ->firstOrFail();
        // dd($order);
        // Check if we have a PayU order ID
        if (!$order->payu_order_id) {
            return redirect()->route('profile.order.list')
                ->with('error', 'Nie można kontynuować płatności dla tego zamówienia.');
        }
        
        // Get PayU service - Fix the namespace issue
        $payuService = app(\App\Services\PayuService::class);
        
        // Get payment URL for existing order
        try {
            $paymentUrl = $payuService->getPaymentUrl($order->payu_order_id, $order);
            
            if ($paymentUrl) {
                return redirect($paymentUrl);
            }
            
            return redirect()->route('profile.order.list')
                ->with('error', 'Nie można kontynuować płatności. Proszę skontaktować się z obsługą.');
        } catch (\Exception $e) {
            return redirect()->route('profile.order.list')
                ->with('error', 'Wystąpił błąd podczas próby kontynuacji płatności.' . $e->getMessage());
        }
    }
}
