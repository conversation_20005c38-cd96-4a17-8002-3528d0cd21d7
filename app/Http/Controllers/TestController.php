<?php

namespace App\Http\Controllers;

use App\Jobs\FullImportJob;
use App\Services\MolosApiService;
use App\Services\MolosCategories;
use App\Services\MolosProducents;
use App\Services\MolosProducts;
use Illuminate\Http\Request;

class TestController extends Controller
{
    //
    public function index()
    {
        FullImportJob::dispatch();
        // $molosApiService = new MolosCategories();
        // $molosApiService->importCategories();
        // $molosApiService = new MolosProducents();
        // $molosApiService->importProducents();
        // $molosApiService = new MolosProducts();
        // $molosApiService->importProducts();
        // $molosApiService->generateDescription("4DOGS Gryzak z sera himalajskiego Churpi Mięta M");
        // $molosApiService->importCategories();
    }
}
