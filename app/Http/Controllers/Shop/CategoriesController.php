<?php

namespace App\Http\Controllers\Shop;

use App\Models\Category;
use App\Models\Product;
use App\Models\Producent;
use App\Models\Property;
use App\Models\PropertyOption;
use App\Services\ProductFilterService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CategoriesController extends Controller
{

    protected $productFilterService;

    public function __construct(ProductFilterService $productFilterService)
    {
        $this->productFilterService = $productFilterService;
    }

    public function showCategory($path, Request $request)
    {
        // Find the category based on the path and check visibility
        $category = Category::where('path', $path)
            ->visible()
            ->firstOrFail();
    
        $categoryIds = $this->getDescendantCategoryIds($category);
        $categoryIds[] = $category->id; // Include the current category
    
        // Gather filters from the request, including sorting from the form
        $filters = [
            'producent_id' => $request->input('filter.producent_id', []),  // Accept multiple producent IDs
            'properties'   => $this->extractPropertyFilters($request),
            'sort'         => $request->input('orderby') // Use the orderby field from the form
        ];
    
        // Use the service to filter and sort products
        $products = $this->productFilterService->filterAndSort($categoryIds, $filters);
    
        // Fetch children categories
        $children = $category->children;
    
        // Pass the current sort option to the view
        $orderby = $filters['sort'] ?? 'name-asc'; // Default to name ascending if not provided

        $producents = Producent::whereHas('products', function ($query) use ($categoryIds) {
            $query->whereIn('category_id', $categoryIds);
        })->get();

        // Build filter array with producents and dynamic property filters
        $filter = [
            [
                'type' => 'checkbox',
                'name' => 'Producent',
                'code' => 'producent_id',
                'variables' => $producents,
            ]
        ];

        // Add property-based filters
        $propertyFilters = $this->getPropertyFilters($category, $categoryIds, $request);
        $filter = array_merge($filter, $propertyFilters);
    
        return view('app.pages.category', compact('category', 'products', 'children', 'orderby', 'filter'));
    }

    public function index()
    {
        $categories = Category::whereNull('parent_id')->get();
        return view('app.categories.index', compact('categories'));
    }

    private function getDescendantCategoryIds($category)
    {
        $descendants = collect();

        foreach ($category->children()->visible()->get() as $child) {
            $descendants->push($child->id);
            $descendants = $descendants->merge($this->getDescendantCategoryIds($child));
        }

        return $descendants->all();
    }

    public function ajaxSearch(Request $request)
    {
        $query = $request->input('search');

        $products = Product::search($query)
            ->query(function ($builder) use ($request) {
                // Apply additional filters if necessary
                if ($request->filled('category')) {
                    $builder->where('category', $request->input('category'));
                }
                // Add more filters as needed
            })
            ->take(5)
            ->get()
            ->map(function ($product) {
                return [
                    'label' => $product->name,
                    'value' => route('detail', ['path' => $product->category->url, 'product' => $product]),
                ];
            });

        return response()->json($products);
    }

    /**
     * Extract property filters from request
     */
    private function extractPropertyFilters(Request $request)
    {
        $propertyFilters = [];

        // Get all filter inputs that start with 'property_'
        $allFilters = $request->input('filter', []);

        foreach ($allFilters as $key => $value) {
            if (strpos($key, 'property_') === 0 && !empty($value)) {
                $propertyId = str_replace('property_', '', $key);
                $propertyFilters[$propertyId] = $value;
            }
        }

        return $propertyFilters;
    }

    /**
     * Get property-based filters for the category
     */
    private function getPropertyFilters($category, $categoryIds, Request $request)
    {
        $filters = [];

        // Get properties associated with this category and its ancestors
        $categoryWithAncestors = array_merge([$category->id], $category->getAncestorIds());

        // Get properties that are either:
        // 1. Associated with this category or its ancestors
        // 2. Not associated with any category (global properties)
        $properties = Property::with('options')
            ->where(function ($query) use ($categoryWithAncestors) {
                $query->whereDoesntHave('categories')
                      ->orWhereHas('categories', function ($subQuery) use ($categoryWithAncestors) {
                          $subQuery->whereIn('categories.id', $categoryWithAncestors);
                      });
            })
            ->get();

        foreach ($properties as $property) {
            if ($property->type === 'select') {
                // For select properties, get available options from products in this category
                $availableOptions = $this->getAvailablePropertyOptions($property, $categoryIds);

                if ($availableOptions->isNotEmpty()) {
                    $filters[] = [
                        'type' => 'checkbox',
                        'name' => $property->name,
                        'code' => 'property_' . $property->id,
                        'variables' => $availableOptions,
                        'property_type' => 'select',
                        'is_multiple' => $property->is_multiple
                    ];
                }
            } elseif ($property->type === 'text') {
                // For text properties, check if we have any values in this category
                $hasValues = $this->hasTextPropertyValues($property, $categoryIds);

                if ($hasValues) {
                    // Check if values are numeric for range filter
                    $isNumeric = $this->isNumericProperty($property, $categoryIds);

                    if ($isNumeric) {
                        $range = $this->getNumericPropertyRange($property, $categoryIds);
                        $filters[] = [
                            'type' => 'range',
                            'name' => $property->name,
                            'code' => 'property_' . $property->id,
                            'min' => $range['min'],
                            'max' => $range['max'],
                            'from' => $request->input('filter.property_' . $property->id . '.from', $range['min']),
                            'to' => $request->input('filter.property_' . $property->id . '.to', $range['max']),
                            'property_type' => 'text_numeric'
                        ];
                    } else {
                        $filters[] = [
                            'type' => 'text',
                            'name' => $property->name,
                            'code' => 'property_' . $property->id,
                            'value' => $request->input('filter.property_' . $property->id, ''),
                            'property_type' => 'text'
                        ];
                    }
                }
            }
        }

        return $filters;
    }

    /**
     * Get available property options for products in the given categories
     */
    private function getAvailablePropertyOptions($property, $categoryIds)
    {
        return PropertyOption::where('property_id', $property->id)
            ->whereExists(function ($query) use ($categoryIds) {
                $query->select(\DB::raw(1))
                      ->from('product_property')
                      ->join('products', 'product_property.product_id', '=', 'products.id')
                      ->whereColumn('product_property.property_option_id', 'property_options.id')
                      ->whereIn('products.category_id', $categoryIds)
                      ->where('products.stock', '>', 0);
            })
            ->get();
    }

    /**
     * Check if property has any text values in the given categories
     */
    private function hasTextPropertyValues($property, $categoryIds)
    {
        return \DB::table('product_property')
            ->join('products', 'product_property.product_id', '=', 'products.id')
            ->where('product_property.property_id', $property->id)
            ->whereNotNull('product_property.value')
            ->where('product_property.value', '!=', '')
            ->whereIn('products.category_id', $categoryIds)
            ->where('products.stock', '>', 0)
            ->exists();
    }

    /**
     * Check if text property values are numeric
     */
    private function isNumericProperty($property, $categoryIds)
    {
        $values = \DB::table('product_property')
            ->join('products', 'product_property.product_id', '=', 'products.id')
            ->where('product_property.property_id', $property->id)
            ->whereNotNull('product_property.value')
            ->where('product_property.value', '!=', '')
            ->whereIn('products.category_id', $categoryIds)
            ->where('products.stock', '>', 0)
            ->pluck('product_property.value')
            ->take(10); // Sample first 10 values

        foreach ($values as $value) {
            if (!is_numeric($value)) {
                return false;
            }
        }

        return $values->isNotEmpty();
    }

    /**
     * Get numeric range for text property
     */
    private function getNumericPropertyRange($property, $categoryIds)
    {
        $result = \DB::table('product_property')
            ->join('products', 'product_property.product_id', '=', 'products.id')
            ->where('product_property.property_id', $property->id)
            ->whereNotNull('product_property.value')
            ->where('product_property.value', '!=', '')
            ->whereIn('products.category_id', $categoryIds)
            ->where('products.stock', '>', 0)
            ->selectRaw('MIN(CAST(product_property.value AS DECIMAL(10,2))) as min_val, MAX(CAST(product_property.value AS DECIMAL(10,2))) as max_val')
            ->first();

        return [
            'min' => $result->min_val ?? 0,
            'max' => $result->max_val ?? 100
        ];
    }
}
