<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Services\App\CartService;
use App\Services\App\ProductRecommendationService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;

class CartController extends Controller
{
    use ApiResponseTrait;

    /**
     * Display the cart page.
     */
    public function index(CartService $cartService, ProductRecommendationService $recommendationService)
    {
        // Retrieve cart items and total
        $cartItems = $cartService->getCartItems();
        $subTotal = $cartService->getSubtotal();
        $total = $cartService->getTotal();
        $coupon = null;
        $deliveryInfo = $cartService->getDeliveryCostInfo();

        // Get upsell recommendations
        $cart = $cartService->getCart();
        $upsells = $cart ? $recommendationService->getCartUpsells($cart, 6) : collect();

        return view('app.pages.cart', compact(
            'cartItems',
            'total',
            'subTotal',
            'coupon',
            'cartService',
            'deliveryInfo',
            'upsells'
        ));
    }

    /**
     * Add a product to the cart.
     */
    public function add(Request $request, Product $product, CartService $cartService)
    {
        $quantity = $request->input('quantity', 1);

        $result = $cartService->addProduct($product, $quantity);

        if (!$result['success']) {
            return $this->errorResponse($result['message'], [
                'availableQuantity' => $result['availableQuantity'] ?? 0,
            ], 201);
        }

        // Use CartService methods to get count and total
        $count = $cartService->getCount();
        $total = $cartService->getTotal();

        return $this->successResponse($result['message'], [
            'count' => $count,
            'total' => $total,
        ]);
    }

    /**
     * Update the quantity of a product in the cart.
     */
    public function update(Request $request, Product $product, CartService $cartService)
    {
        $quantity = $request->input('quantity');

        if ($quantity === null) {
            return $this->errorResponse('Quantity is required', [], 400);
        }

        $result = $cartService->updateProductQuantity($product, $quantity);

        if (!$result['success']) {
            return $this->errorResponse('Nie wystarczająca ilość produktu w magazynie', [
                'availableQuantity' => $result['availableQuantity'] ?? 0,
            ], 201);
        }

        // Get updated cart information
        $count = $cartService->getCount();
        $total = $cartService->getTotal();
        $subTotal = $cartService->getSubtotal();
        $deliveryInfo = $cartService->getDeliveryCostInfo();

        // Get the updated cart item
        $cartItem = $cartService->getCartItemByProduct($product);

        return $this->successResponse($result['message'], [
            'count' => $count,
            'total' => $total,
            'subTotal' => $subTotal,
            'deliveryInfo' => $deliveryInfo,
            'item' => [
                'discount' => $cartItem->discount ?? false,
                'subtotal' => $cartItem->subtotal ?? ($product->price * $quantity)
            ]
        ]);
    }

    /**
     * Remove a product from the cart.
     */
    public function remove(Request $request, Product $product, CartService $cartService)
    {
        $cartService->removeProduct($product);

        // Use CartService methods to get count and total
        $count = $cartService->getCount();
        $total = $cartService->getTotal();
        $subTotal = $cartService->getSubtotal();
        $deliveryInfo = $cartService->getDeliveryCostInfo();

        return $this->successResponse('Produkt został usunięty z koszyka', [
            'count' => $count,
            'total' => $total,
            'subTotal' => $subTotal,
            'deliveryInfo' => $deliveryInfo,
        ]);
    }
}
