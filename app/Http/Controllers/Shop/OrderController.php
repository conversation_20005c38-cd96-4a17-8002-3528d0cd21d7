<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function show($uuid)
    {
        $order = Order::where('uuid', $uuid)
            ->with(['items.product', 'user', 'deliveryAddress.deliveryMethod'])
            ->firstOrFail();

        // Check if user has access to this order
        if (auth()->check() && auth()->id() !== $order->user_id) {
            abort(403);
        }

        // Get tracking information
        $trackingInfo = $this->getTrackingInfo($order);

        // Prepare status information
        $statusInfo = [
            'isPaid' => $order->payment_status === 'completed',
            'isCompleted' => $order->status === 'completed',
            'isProcessing' => in_array($order->status, ['pending', 'processing']),
            'hasTracking' => !empty($order->inpost_tracking_number),
            'trackingInfo' => $trackingInfo,
            'statusClass' => $this->getStatusClass($order->status),
            'statusName' => $this->getStatusName($order->status),
            'paymentStatusName' => $this->getPaymentStatusName($order->payment_status),
        ];
        // dd($order, $statusInfo);

        return view('app.pages.order.show', compact('order', 'statusInfo'));
    }

    public function index()
    {
        $orders = auth()->user()
            ->orders()
            ->latest()
            ->paginate(10);

        return view('app.pages.order.index', compact('orders'));
    }

    private function getStatusClass($status)
    {
        return match($status) {
            'pending' => 'pending',
            'processing' => 'processing',
            'completed' => 'completed',
            'cancelled' => 'cancelled',
            default => 'pending'
        };
    }

    private function getStatusName($status)
    {
        return match($status) {
            'pending' => 'Oczekujące',
            'processing' => 'W realizacji',
            'completed' => 'Zakończone',
            'cancelled' => 'Anulowane',
            default => 'Nieznany'
        };
    }

    private function getPaymentStatusName($paymentStatus)
    {
        return match($paymentStatus) {
            'pending' => 'Oczekująca',
            'completed' => 'Opłacone',
            'failed' => 'Nieudana',
            'refunded' => 'Zwrócona',
            default => 'Nieznany'
        };
    }

    private function getTrackingInfo($order)
    {
        if (empty($order->inpost_tracking_number)) {
            return null;
        }
        
        // Check if we have multiple tracking numbers
        if (strpos($order->inpost_tracking_number, ',') !== false) {
            $trackingNumbers = explode(',', $order->inpost_tracking_number);
            return [
                'isMultiple' => true,
                'numbers' => $trackingNumbers,
                'primaryNumber' => $trackingNumbers[0] ?? null
            ];
        }
        
        // Single tracking number
        return [
            'isMultiple' => false,
            'number' => $order->inpost_tracking_number
        ];
    }
}
