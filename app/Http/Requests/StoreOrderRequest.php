<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\DeliveryType;
use Auth;

class StoreOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $rules = [
            'payment' => 'required',
            'regulamen' => 'required',
        ];
        if (!Auth::check())
        {
            if ($request->password)
            {
                $rules = array_merge($rules, [
                    'first_name' => 'required|string|max:255',
                    'last_name' => 'required|string|max:255',
                    'phone' => 'required|string|max:255',
                    'email' => 'required|string|email|max:255|unique:users',
                    'password' => 'required|string|min:8',
                ]);
            }
            else
            {
                $rules = array_merge($rules, [
                    'first_name' => 'required|string|max:255',
                    'last_name' => 'required|string|max:255',
                    'phone' => 'required|string|max:255',
                    'email' => 'required|string|email|max:255',
                ]);
            }
        }
        if (!$request->delivery_address_id)
        {
            $rules = array_merge($rules, [
                'city' => 'required|string|max:255',
                'address' => 'required|string|max:255',
                'zip' => 'required|string|max:255',
                'delivery' => 'required',
            ]);
            if ($request->delivery)
            {
                if (DeliveryType::find($request->delivery)->paczkomat)
                {
                    $rules = array_merge($rules, [
                        'pachkomat_name' => 'required',
                    ]);
                }
            }
        }
        // dd($request);
        return $rules;
    }

    public function messages()
    {
        return [
            'email.unique' => 'Kupujący z takim mailem już jest. Zaloguj się aby kontynuować',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'first_name' => 'Imię',
            'last_name' => 'Nazwisko',
            'phone' => 'Telefon',
            'email' => 'Email',
            'password' => 'Hasło',
            'city' => 'Miasto',
            'address' => 'Ulica i nr domu',
            'zip' => 'Kod pocztowy',
            'delivery' => 'Dostawa',
        ];
    }
}
