<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
use App\Models\User;

class IsAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::user()->email == '<EMAIL>')
        {
            return $next($request);
        }
        if (Auth::user()->email == '<EMAIL>')
        {
            return $next($request);
        }
        if (Auth::user()->admin)
        {
            return $next($request);
        }
        else
        {
            return response('Unauthorized.', 401);
        }
    }
}
