<?php

namespace App\Notifications;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrderStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    protected $order;
    protected $status;

    /**
     * Create a new notification instance.
     *
     * @param Order $order
     * @param string $status
     */
    public function __construct(Order $order, string $status)
    {
        $this->order = $order;
        $this->status = $status;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject("Zamówienie #{$this->order->id} - " . $this->getStatusName())
            ->markdown('emails.orders.status-notification', [
                'order' => $this->order,
                'status' => $this->status
            ]);
    }

    /**
     * Get human-readable status name
     *
     * @return string
     */
    protected function getStatusName(): string
    {
        return match($this->status) {
            'pending' => 'Oczekujące',
            'processing' => 'W trakcie realizacji',
            'shipped' => 'Wysłane',
            'delivered' => 'Dostarczone',
            'completed' => 'Zakończone',
            'cancelled' => 'Anulowane',
            'paid' => 'Opłacone',
            'failed' => 'Nieudane',
            'refunded' => 'Zwrócone',
            default => 'Aktualizacja statusu',
        };
    }

    /**
     * Determine if this status change should trigger a notification
     *
     * @return bool
     */
    public function shouldNotify(): bool
    {
        // Don't send notifications for certain status changes
        $skipStatuses = ['pending']; // Skip initial pending status

        return !in_array($this->status, $skipStatuses);
    }

    /**
     * Get the notification data for queuing
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'order_id' => $this->order->id,
            'order_uuid' => $this->order->uuid,
            'status' => $this->status,
            'status_name' => $this->getStatusName(),
            'customer_email' => $this->order->email,
            'customer_name' => $this->order->first_name . ' ' . $this->order->last_name,
        ];
    }
}