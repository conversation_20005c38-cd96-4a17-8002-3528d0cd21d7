<?php

namespace App\Providers;

use App\Services\App\ProductRecommendationService;
use Illuminate\Support\ServiceProvider;

class RecommendationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ProductRecommendationService::class, function ($app) {
            return new ProductRecommendationService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/recommendations.php' => config_path('recommendations.php'),
        ], 'recommendations-config');

        // Register console commands if needed
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\ClearRecommendationCache::class,
            ]);
        }
    }
}
