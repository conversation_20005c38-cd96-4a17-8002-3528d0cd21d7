<?php

namespace App\Providers;

use App\Listeners\MailEventListener;
use Illuminate\Mail\Events\MessageSending;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\MailManager;
use Symfony\Component\Mailer\Exception\TransportException;

class MailServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register mail event listeners
        Event::listen(MessageSending::class, [MailEventListener::class, 'handleMessageSending']);
        Event::listen(MessageSent::class, [MailEventListener::class, 'handleMessageSent']);

        // Register mail failure handling through macro
        // This provides a way to handle mail failures similar to the old Mail::failures() method
        Mail::macro('handleFailures', function ($callback) {
            // Store the failure callback for use in exception handling
            app()->singleton('mail.failure.callback', function () use ($callback) {
                return $callback;
            });
        });

        // Set up default failure handling
        $this->setupMailFailureHandling();

        // Configure Postmark-specific settings
        if (config('mail.default') === 'postmark') {
            $this->configurePostmark();
        }
    }

    /**
     * Set up mail failure handling
     */
    protected function setupMailFailureHandling(): void
    {
        // Set up default failure handling that mimics the old Mail::failures() behavior
        Mail::handleFailures(function ($exception, $message = null) {
            $listener = new MailEventListener();
            $listener->handleMessageFailed($exception, $message);
        });
    }

    /**
     * Configure Postmark-specific settings
     */
    protected function configurePostmark(): void
    {
        // Validate Postmark configuration
        if (empty(config('mail.mailers.postmark.token'))) {
            logger()->warning('Postmark token not configured. Email delivery may fail.');
        }

        // Set default headers for Postmark
        Mail::alwaysFrom(
            config('mail.from.address'),
            config('mail.from.name')
        );

        // Configure Postmark message stream if not set
        if (empty(config('mail.mailers.postmark.message_stream_id'))) {
            config(['mail.mailers.postmark.message_stream_id' => 'outbound']);
        }
    }
}
