<?php

namespace App\Providers;

use App\Models\Order;
use App\Observers\OrderObserver;
use App\Services\App\CartService;
use App\Services\ParcelCalculationService;
use App\Services\DeliveryPricingService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register ParcelCalculationService
        $this->app->singleton(ParcelCalculationService::class);

        // Register DeliveryPricingService with its dependency
        $this->app->singleton(DeliveryPricingService::class, function ($app) {
            return new DeliveryPricingService($app->make(ParcelCalculationService::class));
        });

        // Register CartService with its dependency
        $this->app->singleton(CartService::class, function ($app) {
            return new CartService($app->make(DeliveryPricingService::class));
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register model observers
        Order::observe(OrderObserver::class);
    }
}
