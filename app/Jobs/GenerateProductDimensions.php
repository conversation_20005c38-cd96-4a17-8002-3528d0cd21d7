<?php

namespace App\Jobs;

use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;

class GenerateProductDimensions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $batchSize;
    protected $processAll;
    protected $stats;

    public $timeout = 1800; // 30 minutes timeout

    /**
     * Create a new job instance.
     *
     * @param bool $processAll Whether to process all products or just one batch
     * @param int $batchSize Number of products to process per batch
     */
    public function __construct(bool $processAll = false, int $batchSize = 25)
    {
        $this->processAll = $processAll;
        $this->batchSize = $batchSize;
        $this->stats = [
            'total_batches' => 0,
            'total_processed' => 0,
            'total_successful' => 0,
            'total_failed' => 0,
            'total_skipped' => 0,
            'errors' => []
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(ProductService $productService)
    {
        Log::info('Starting GenerateProductDimensions job', [
            'process_all' => $this->processAll,
            'batch_size' => $this->batchSize,
            'job_id' => $this->job->getJobId() ?? 'sync'
        ]);

        try {
            if ($this->processAll) {
                $this->processAllProducts($productService);
            } else {
                $this->processSingleBatch($productService);
            }

            Log::info('GenerateProductDimensions job completed successfully', $this->stats);

        } catch (\Exception $e) {
            Log::error('GenerateProductDimensions job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'stats' => $this->stats
            ]);
            
            throw $e;
        }
    }

    /**
     * Process all products that need dimensions
     */
    protected function processAllProducts(ProductService $productService)
    {
        // Get total count of products needing dimensions
        $totalProductsNeedingDimensions = Product::whereNull('length')
            ->orWhereNull('width')
            ->orWhereNull('height')
            ->count();

        if ($totalProductsNeedingDimensions === 0) {
            Log::info('No products need dimension generation');
            return;
        }

        Log::info('Processing all products needing dimensions', [
            'total_products' => $totalProductsNeedingDimensions,
            'batch_size' => $this->batchSize
        ]);

        $offset = 0;
        $batchNumber = 1;

        do {
            // Get next batch of products needing dimensions
            $products = Product::with(['category'])
                ->where(function ($query) {
                    $query->whereNull('length')
                          ->orWhereNull('width')
                          ->orWhereNull('height');
                })
                ->offset($offset)
                ->limit($this->batchSize)
                ->get();

            if ($products->isEmpty()) {
                break;
            }

            Log::info('Processing batch', [
                'batch_number' => $batchNumber,
                'products_in_batch' => $products->count(),
                'offset' => $offset
            ]);

            // Process this batch
            $batchStats = $productService->generateProductsDimensions($products, $this->batchSize);
            
            // Update overall statistics
            $this->updateStats($batchStats);
            $this->stats['total_batches']++;

            $offset += $this->batchSize;
            $batchNumber++;

            // Add a small delay between batches to avoid overwhelming the API
            if ($products->count() === $this->batchSize) {
                sleep(2);
            }

        } while ($products->count() === $this->batchSize);

        Log::info('Completed processing all products', $this->stats);
    }

    /**
     * Process a single batch of products
     */
    protected function processSingleBatch(ProductService $productService)
    {
        // Get one batch of products needing dimensions
        $products = Product::with(['category'])
            ->where(function ($query) {
                $query->whereNull('length')
                      ->orWhereNull('width')
                      ->orWhereNull('height');
            })
            ->limit($this->batchSize)
            ->get();

        if ($products->isEmpty()) {
            Log::info('No products need dimension generation for single batch');
            return;
        }

        Log::info('Processing single batch', [
            'products_in_batch' => $products->count(),
            'batch_size' => $this->batchSize
        ]);

        // Process this batch
        $batchStats = $productService->generateProductsDimensions($products, $this->batchSize);
        
        // Update statistics
        $this->updateStats($batchStats);
        $this->stats['total_batches'] = 1;

        Log::info('Completed single batch processing', $this->stats);
    }

    /**
     * Update overall statistics with batch results
     */
    protected function updateStats(array $batchStats)
    {
        $this->stats['total_processed'] += $batchStats['processed'];
        $this->stats['total_successful'] += $batchStats['successful'];
        $this->stats['total_failed'] += $batchStats['failed'];
        $this->stats['total_skipped'] += $batchStats['skipped'];
        $this->stats['errors'] = array_merge($this->stats['errors'], $batchStats['errors']);
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception)
    {
        Log::error('GenerateProductDimensions job failed permanently', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
            'process_all' => $this->processAll,
            'batch_size' => $this->batchSize,
            'stats' => $this->stats
        ]);
    }

    /**
     * Get job description for logging
     */
    public function getJobDescription(): string
    {
        return $this->processAll 
            ? "Generate dimensions for all products (batch size: {$this->batchSize})"
            : "Generate dimensions for single batch ({$this->batchSize} products)";
    }
}
