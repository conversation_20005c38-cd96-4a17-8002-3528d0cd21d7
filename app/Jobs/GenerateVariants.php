<?php

namespace App\Jobs;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Services\ProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class GenerateVariants implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $dryRun;
    protected $batchSize;
    protected $stats;

    /**
     * Create a new job instance.
     */
    public function __construct(bool $dryRun = false, int $batchSize = 50)
    {
        $this->dryRun = $dryRun;
        $this->batchSize = $batchSize;
        $this->stats = [
            'processed' => 0,
            'variants_created' => 0,
            'products_grouped' => 0,
            'errors' => 0,
            'skipped' => 0,
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(ProductService $productService): void
    {
        $startTime = now();
        
        Log::info('GenerateVariants job started', [
            'dry_run' => $this->dryRun,
            'batch_size' => $this->batchSize,
            'start_time' => $startTime,
        ]);

        try {
            $this->processUnorganizedProducts($productService);
            
            $endTime = now();
            $duration = $endTime->diffInSeconds($startTime);
            
            Log::info('GenerateVariants job completed successfully', [
                'stats' => $this->stats,
                'duration_seconds' => $duration,
                'dry_run' => $this->dryRun,
            ]);
            
        } catch (Exception $e) {
            Log::error('GenerateVariants job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'stats' => $this->stats,
            ]);
            
            throw $e;
        }
    }

    /**
     * Process all unorganized products in batches
     */
    protected function processUnorganizedProducts(ProductService $productService): void
    {
        $processedProductIds = [];
        
        // Get all products that don't have a product_variant_id
        $query = Product::whereNull('product_variant_id')
                       ->with(['category', 'producent'])
                       ->orderBy('id');
        
        $totalProducts = $query->count();
        Log::info("Found {$totalProducts} unorganized products to process");
        
        $query->chunk($this->batchSize, function ($products) use ($productService, &$processedProductIds) {
            foreach ($products as $product) {
                // Skip if this product was already processed as part of another variant group
                if (in_array($product->id, $processedProductIds)) {
                    $this->stats['skipped']++;
                    continue;
                }
                
                try {
                    $variantProductIds = $this->processProduct($product, $productService);
                    
                    // Add all processed product IDs to avoid reprocessing
                    $processedProductIds = array_merge($processedProductIds, $variantProductIds);
                    
                    $this->stats['processed']++;
                    
                } catch (Exception $e) {
                    Log::warning('Error processing product', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'error' => $e->getMessage(),
                    ]);
                    
                    $this->stats['errors']++;
                }
            }
        });
    }

    /**
     * Process a single product and create variant group if similar products found
     */
    protected function processProduct(Product $product, ProductService $productService): array
    {
        // Find similar products using existing ProductService logic
        $similarProducts = $productService->findSimilarProducts($product);
        
        // Only create variant group if we have 2 or more products total (including main product)
        if ($similarProducts->isEmpty()) {
            return [$product->id];
        }
        
        // Create an Eloquent Collection with the main product and similar products
        $allProducts = new EloquentCollection([$product]);
        $allProducts = $allProducts->merge($similarProducts);
        $productIds = $allProducts->pluck('id')->toArray();
        
        Log::info('Found variant group', [
            'main_product' => $product->name,
            'similar_count' => $similarProducts->count(),
            'total_products' => $allProducts->count(),
            'product_ids' => $productIds,
            'dry_run' => $this->dryRun,
        ]);
        
        if (!$this->dryRun) {
            $this->createVariantGroup($product, $allProducts, $productService);
        }
        
        return $productIds;
    }

    /**
     * Create a ProductVariant group and assign products to it
     */
    protected function createVariantGroup($mainProduct, $allProducts, ProductService $productService): void
    {
        DB::transaction(function () use ($mainProduct, $allProducts, $productService) {
            // Create ProductVariant record with main product's name
            $productVariant = ProductVariant::create([
                'name' => $mainProduct->name,
                'show_name' => null, // Let admin customize this later
            ]);
            
            Log::info('Created ProductVariant', [
                'variant_id' => $productVariant->id,
                'variant_name' => $productVariant->name,
            ]);
            
            // Get variant differences using existing ProductService logic
            // Since $allProducts is already an Eloquent Collection, we can slice it directly
            // but we need to convert the slice result back to an Eloquent Collection
            $similarProducts = new EloquentCollection($allProducts->slice(1)->values()->all());
            $variantDifferences = $productService->getVariantDifferences($mainProduct, $similarProducts);
            
            // Assign all products to this variant group
            foreach ($variantDifferences as $variantData) {
                $product = $variantData['product'];
                $variantName = $variantData['difference'];
                
                // Update product with variant information
                $product->update([
                    'product_variant_id' => $productVariant->id,
                    'variant_name' => $variantName !== 'Standard' ? $variantName : null,
                ]);
                
                Log::debug('Assigned product to variant', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'variant_id' => $productVariant->id,
                    'variant_name' => $variantName,
                ]);
                
                $this->stats['products_grouped']++;
            }
            
            $this->stats['variants_created']++;
        });
    }

    /**
     * Get job statistics
     */
    public function getStats(): array
    {
        return $this->stats;
    }

    /**
     * The job failed to process.
     */
    public function failed(Exception $exception): void
    {
        Log::error('GenerateVariants job failed completely', [
            'error' => $exception->getMessage(),
            'stats' => $this->stats,
        ]);
    }
}
