<?php

namespace App\Jobs;

use App\Models\Product;
use App\Models\Category;
use App\Models\Producent;
use App\Services\MolosApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class ImportProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $productData;
    
    public $timeout = 600;

    public function __construct(array $productData)
    {
        $this->productData = $productData;
    }

    public function handle(MolosApiService $molosApiService)
    {
        $productData = $this->productData;

        $molosApiService->importOneProduct($productData);
    }
}
