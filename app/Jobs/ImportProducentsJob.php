<?php

namespace App\Jobs;

use App\Services\MolosProducents;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ImportProducentsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public $timeout = 600;

    public function __construct()
    {
        //
    }

    public function handle()
    {
        $producentsImporter = new MolosProducents();
        $producentsImporter->importProducents(); // Correct method
    }
}
