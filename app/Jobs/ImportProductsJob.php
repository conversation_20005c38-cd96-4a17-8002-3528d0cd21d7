<?php

namespace App\Jobs;

use App\Services\MolosProducts;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ImportProductsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $page;

    public function __construct($page = 1)
    {
        $this->page = $page;
    }

    public function handle()
    {
        $productsImporter = new MolosProducts();
        $next = $productsImporter->importProducts($this->page); // Pass the current page

        // If there are more pages, dispatch the next page
        if ($next) {
            ImportProductsJob::dispatch($this->page + 1);
        }
    }
}
