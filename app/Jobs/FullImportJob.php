<?php

namespace App\Jobs;

use App\Models\Category;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class FullImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {
        //
    }

    public function handle()
    {
        // Check if categories already exist in the database
        $categoriesExist = Category::exists();

        // Build the job chain based on whether categories exist
        $jobChain = [];

        // Only add category import jobs if no categories exist
        if (!$categoriesExist) {
            $jobChain[] = new ImportCategoriesJob();
            $jobChain[] = new ImportCategoriesJob(); // Import categories twice as in original
        }

        // Always add producents and products import jobs
        $jobChain[] = new ImportProducentsJob();
        $jobChain[] = new ImportProductsJob(1); // Start with page 1 for products

        // Dispatch the first job with the remaining jobs chained
        if (!empty($jobChain)) {
            $firstJob = array_shift($jobChain);
            if (!empty($jobChain)) {
                $firstJob::withChain($jobChain)->dispatch();
            } else {
                $firstJob::dispatch();
            }
        }
    }
}
