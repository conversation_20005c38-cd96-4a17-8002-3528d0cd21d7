<?php

namespace App\View\Components;

use App\Services\App\CartService;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Cart extends Component
{
    public $count;
    public $total;

    protected $cartService;
    
    /**
     * Create a new component instance.
     *
     * @param CartService $cartService
     */
    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;

        // Use the CartService methods to get count and total
        $this->count = $this->cartService->getCount();
        $this->total = $this->cartService->getTotal();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.cart', [
            'total' => $this->total,
            'count' => $this->count,
        ]);
    }
}
